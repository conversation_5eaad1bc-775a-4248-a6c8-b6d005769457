#!/usr/bin/env python3
"""
WAN 2.1 Batch Video Generator
Generate multiple videos from a list of prompts using ComfyUI API

Usage:
    python3 batch_video_generator.py
"""

import json
import time
import requests
from typing import List, Dict

# ComfyUI server configuration
COMFYUI_URL = "http://127.0.0.1:8188"

def create_wan_workflow(prompt: str, negative_prompt: str = "", seed: int = 42) -> Dict:
    """Create a WAN 2.1 workflow for the given prompt"""
    return {
        "1": {
            "inputs": {
                "model_name": "wan2.1_t2v_1.3B_fp16.safetensors",
                "model": "wan2.1_t2v_1.3B_fp16.safetensors",
                "load_device": "main_device",
                "base_precision": "fp16",
                "quantization": "disabled"
            },
            "class_type": "WanVideoModelLoader",
            "_meta": {"title": "WanVideo Model Loader"}
        },
        "2": {
            "inputs": {
                "model_name": "wan_2.1_vae.safetensors",
                "vae_name": "wan_2.1_vae.safetensors",
                "precision": "fp16"
            },
            "class_type": "WanVideoVAELoader",
            "_meta": {"title": "WanVideo VAE Loader"}
        },
        "3": {
            "inputs": {},
            "class_type": "LoadWanVideoT5TextEncoder",
            "_meta": {"title": "Load WanVideo T5 TextEncoder"}
        },
        "4": {
            "inputs": {
                "text": prompt,
                "text_encoder": ["3", 0],
                "model": ["1", 0]
            },
            "class_type": "WanVideoTextEncode",
            "_meta": {"title": "WanVideo TextEncode"}
        },
        "5": {
            "inputs": {
                "text": negative_prompt,
                "text_encoder": ["3", 0],
                "model": ["1", 0]
            },
            "class_type": "WanVideoTextEncode",
            "_meta": {"title": "WanVideo TextEncode (Negative)"}
        },
        "6": {
            "inputs": {
                "width": 512,
                "height": 512,
                "num_frames": 16
            },
            "class_type": "WanVideoEmptyEmbeds",
            "_meta": {"title": "WanVideo Empty Embeds"}
        },
        "7": {
            "inputs": {
                "seed": seed,
                "steps": 20,
                "cfg": 7.0,
                "width": 512,
                "height": 512,
                "num_frames": 16,
                "scheduler": "euler",
                "riflex_freq_index": 0,
                "force_offload": False,
                "shift": 1.0,
                "model": ["1", 0],
                "positive": ["4", 0],
                "negative": ["5", 0],
                "image_embeds": ["6", 0]
            },
            "class_type": "WanVideoSampler",
            "_meta": {"title": "WanVideo Sampler"}
        },
        "8": {
            "inputs": {
                "samples": ["7", 0],
                "vae": ["2", 0],
                "enable_vae_tiling": False,
                "tile_x": 2,
                "tile_y": 2,
                "tile_stride_x": 1,
                "tile_stride_y": 1
            },
            "class_type": "WanVideoDecode",
            "_meta": {"title": "WanVideo Decode"}
        },
        "9": {
            "inputs": {
                "images": ["8", 0],
                "filename_prefix": f"wan_video_{int(time.time())}",
                "fps": 8,
                "lossless": False,
                "quality": 80,
                "method": "default"
            },
            "class_type": "SaveAnimatedWEBP",
            "_meta": {"title": "Save Animated WebP"}
        }
    }

def queue_prompt(workflow: Dict) -> Dict:
    """Queue a prompt for generation"""
    response = requests.post(f"{COMFYUI_URL}/prompt", json={"prompt": workflow})
    return response.json()

def wait_for_completion(prompt_id: str, timeout: int = 300) -> bool:
    """Wait for prompt completion"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"{COMFYUI_URL}/history/{prompt_id}")
            history = response.json()
            if prompt_id in history:
                return True
        except:
            pass
        time.sleep(2)
    return False

def generate_videos_from_prompts(prompts: List[str], negative_prompt: str = "") -> None:
    """Generate videos from a list of prompts"""
    print(f"🎬 Starting batch generation for {len(prompts)} prompts...")
    
    successful = 0
    failed = 0
    
    for i, prompt in enumerate(prompts, 1):
        print(f"\n📝 [{i}/{len(prompts)}] Processing: '{prompt[:50]}{'...' if len(prompt) > 50 else ''}'")
        
        try:
            # Create workflow with unique seed for each prompt
            workflow = create_wan_workflow(prompt, negative_prompt, seed=42 + i)
            
            # Queue the prompt
            result = queue_prompt(workflow)
            
            if 'prompt_id' not in result:
                print(f"❌ Failed to queue prompt: {result}")
                failed += 1
                continue
                
            prompt_id = result['prompt_id']
            print(f"✅ Queued with ID: {prompt_id}")
            
            # Wait for completion
            print("⏳ Waiting for generation...")
            if wait_for_completion(prompt_id):
                print("🎉 Generation completed!")
                successful += 1
            else:
                print("⏰ Generation timed out")
                failed += 1
                
        except Exception as e:
            print(f"❌ Error processing prompt: {e}")
            failed += 1
    
    print(f"\n📊 Batch generation complete!")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📁 Check ComfyUI/output/ for generated videos")

# Example usage
if __name__ == "__main__":
    # List of prompts to generate videos for
    prompts = [
        "A cat walking in a garden, realistic style",
        "A dog running on the beach, cinematic",
        "A bird flying through clouds, beautiful",
        "A fish swimming in clear water, peaceful",
        "A butterfly landing on a flower, macro shot"
    ]
    
    # Optional negative prompt (what you don't want)
    negative_prompt = "blurry, low quality, distorted"
    
    # Generate videos
    generate_videos_from_prompts(prompts, negative_prompt)
