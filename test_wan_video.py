#!/usr/bin/env python3
"""
Simple script to test WAN 2.1 video generation via ComfyUI API
"""

import json
import requests
import time
import os

# ComfyUI server URL
COMFYUI_URL = "http://127.0.0.1:8188"

def queue_prompt(prompt):
    """Queue a prompt for generation"""
    p = {"prompt": prompt}
    data = json.dumps(p).encode('utf-8')
    req = requests.post(f"{COMFYUI_URL}/prompt", data=data)
    return json.loads(req.text)

def get_history(prompt_id):
    """Get generation history"""
    req = requests.get(f"{COMFYUI_URL}/history/{prompt_id}")
    return json.loads(req.text)

def wait_for_completion(prompt_id, timeout=300):
    """Wait for generation to complete"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        history = get_history(prompt_id)
        if prompt_id in history:
            return history[prompt_id]
        time.sleep(2)
    return None

# Simple WAN 2.1 workflow using WanVideoWrapper nodes with all required parameters
wan_workflow = {
    "1": {
        "inputs": {
            "model_name": "wan2.1_t2v_1.3B_fp16.safetensors",
            "model": "wan2.1_t2v_1.3B_fp16.safetensors",
            "load_device": "main_device",
            "base_precision": "fp16",
            "quantization": "disabled"
        },
        "class_type": "WanVideoModelLoader",
        "_meta": {"title": "WanVideo Model Loader"}
    },
    "2": {
        "inputs": {
            "model_name": "wan_2.1_vae.safetensors",
            "vae_name": "wan_2.1_vae.safetensors",
            "precision": "fp16"
        },
        "class_type": "WanVideoVAELoader",
        "_meta": {"title": "WanVideo VAE Loader"}
    },
    "3": {
        "inputs": {},
        "class_type": "LoadWanVideoT5TextEncoder",
        "_meta": {"title": "Load WanVideo T5 TextEncoder"}
    },
    "4": {
        "inputs": {
            "text": "A cat walking in a garden, realistic style",
            "text_encoder": ["3", 0],
            "model": ["1", 0]
        },
        "class_type": "WanVideoTextEncode",
        "_meta": {"title": "WanVideo TextEncode"}
    },
    "5": {
        "inputs": {
            "text": "",
            "text_encoder": ["3", 0],
            "model": ["1", 0]
        },
        "class_type": "WanVideoTextEncode",
        "_meta": {"title": "WanVideo TextEncode (Negative)"}
    },
    "6": {
        "inputs": {
            "width": 512,
            "height": 512,
            "num_frames": 16
        },
        "class_type": "WanVideoEmptyEmbeds",
        "_meta": {"title": "WanVideo Empty Embeds"}
    },
    "7": {
        "inputs": {
            "seed": 42,
            "steps": 20,
            "cfg": 7.0,
            "width": 512,
            "height": 512,
            "num_frames": 16,
            "scheduler": "euler",
            "riflex_freq_index": 0,
            "force_offload": False,
            "shift": 1.0,
            "model": ["1", 0],
            "positive": ["4", 0],
            "negative": ["5", 0],
            "image_embeds": ["6", 0]
        },
        "class_type": "WanVideoSampler",
        "_meta": {"title": "WanVideo Sampler"}
    },
    "8": {
        "inputs": {
            "samples": ["7", 0],
            "vae": ["2", 0],
            "enable_vae_tiling": False,
            "tile_x": 2,
            "tile_y": 2,
            "tile_stride_x": 1,
            "tile_stride_y": 1
        },
        "class_type": "WanVideoDecode",
        "_meta": {"title": "WanVideo Decode"}
    },
    "9": {
        "inputs": {
            "images": ["8", 0],
            "filename_prefix": "wan_test_video",
            "fps": 8,
            "lossless": False,
            "quality": 80,
            "method": "default"
        },
        "class_type": "SaveAnimatedWEBP",
        "_meta": {"title": "Save Animated WebP"}
    }
}

def test_wan_generation(prompt_text="A cat walking in a garden, realistic style"):
    """Test WAN 2.1 video generation"""
    print(f"🎬 Testing WAN 2.1 with prompt: '{prompt_text}'")
    
    # Update the prompt in the workflow
    wan_workflow["1"]["inputs"]["text"] = prompt_text
    
    try:
        # Queue the prompt
        result = queue_prompt(wan_workflow)
        print(f"🔍 API Response: {result}")

        if 'prompt_id' not in result:
            print(f"❌ No prompt_id in response. Full response: {result}")
            return False

        prompt_id = result['prompt_id']
        print(f"✅ Prompt queued with ID: {prompt_id}")

        # Wait for completion
        print("⏳ Waiting for generation to complete...")
        history = wait_for_completion(prompt_id)

        if history:
            print("🎉 Video generation completed!")
            print(f"📁 Check the ComfyUI output folder for: wan_test_video_*.webp")
            return True
        else:
            print("❌ Generation timed out or failed")
            return False

    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    # Test with a simple prompt
    success = test_wan_generation()
    
    if success:
        print("\n🎯 WAN 2.1 setup is working correctly!")
        print("📝 You can now generate videos with different prompts.")
    else:
        print("\n⚠️  There might be an issue with the setup.")
        print("💡 Check the ComfyUI console for error messages.")
