## ComfyUI-Manager: installing dependencies. (GitPython)
[2025-06-28 01:48:33.745] ## Co<PERSON>fyUI-Manager: installing dependencies done.
[2025-06-28 01:48:33.746] ** ComfyUI startup time: 2025-06-28 01:48:33.746
[2025-06-28 01:48:33.746] ** Platform: Darwin
[2025-06-28 01:48:33.746] ** Python version: 3.11.3 (main, Apr 19 2023, 18:49:55) [Clang 14.0.6 ]
[2025-06-28 01:48:33.746] ** Python executable: /Users/<USER>/anaconda3/bin/python3
[2025-06-28 01:48:33.746] ** ComfyUI Path: /Users/<USER>/Documents/Data/Data_Science/VS_Code/WAN2.1/ComfyUI
[2025-06-28 01:48:33.746] ** ComfyUI Base Folder Path: /Users/<USER>/Documents/Data/Data_Science/VS_Code/WAN2.1/ComfyUI
[2025-06-28 01:48:33.746] ** User directory: /Users/<USER>/Documents/Data/Data_Science/VS_Code/WAN2.1/ComfyUI/user
[2025-06-28 01:48:33.746] ** ComfyUI-Manager config path: /Users/<USER>/Documents/Data/Data_Science/VS_Code/WAN2.1/ComfyUI/user/default/ComfyUI-Manager/config.ini
[2025-06-28 01:48:33.746] ** Log path: /Users/<USER>/Documents/Data/Data_Science/VS_Code/WAN2.1/ComfyUI/user/comfyui.log

Prestartup times for custom nodes:
[2025-06-28 01:48:35.278]   10.2 seconds: /Users/<USER>/Documents/Data/Data_Science/VS_Code/WAN2.1/ComfyUI/custom_nodes/ComfyUI-Manager
[2025-06-28 01:48:35.278] 
[2025-06-28 01:48:36.193] Checkpoint files will always be loaded safely.
[2025-06-28 01:48:36.230] Total VRAM 16384 MB, total RAM 16384 MB
[2025-06-28 01:48:36.230] pytorch version: 2.7.1
[2025-06-28 01:48:36.230] Mac Version (16, 0)
[2025-06-28 01:48:36.231] Set vram state to: SHARED
[2025-06-28 01:48:36.231] Device: mps
[2025-06-28 01:48:53.743] Using sub quadratic optimization for attention, if you have memory or speed issues try using: --use-split-cross-attention
[2025-06-28 01:48:57.588] Python version: 3.11.3 (main, Apr 19 2023, 18:49:55) [Clang 14.0.6 ]
[2025-06-28 01:48:57.588] ComfyUI version: 0.3.42
[2025-06-28 01:48:57.589] ComfyUI frontend version: 1.23.4
[2025-06-28 01:48:57.590] [Prompt Server] web root: /Users/<USER>/anaconda3/lib/python3.11/site-packages/comfyui_frontend_package/static
[2025-06-28 01:48:59.435] ### Loading: ComfyUI-Manager (V3.33.3)
[2025-06-28 01:48:59.435] [ComfyUI-Manager] network_mode: public
[2025-06-28 01:48:59.579] ### ComfyUI Version: v0.3.42-2-g9093301a | Released on '2025-06-27'
[2025-06-28 01:48:59.592] 
Import times for custom nodes:
[2025-06-28 01:48:59.593]    0.0 seconds: /Users/<USER>/Documents/Data/Data_Science/VS_Code/WAN2.1/ComfyUI/custom_nodes/websocket_image_save.py
[2025-06-28 01:48:59.593]    0.3 seconds: /Users/<USER>/Documents/Data/Data_Science/VS_Code/WAN2.1/ComfyUI/custom_nodes/ComfyUI-Manager
[2025-06-28 01:48:59.593] 
[2025-06-28 01:49:00.342] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/alter-list.json
[2025-06-28 01:49:00.394] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/model-list.json
[2025-06-28 01:49:00.565] Context impl SQLiteImpl.
[2025-06-28 01:49:00.565] Will assume non-transactional DDL.
[2025-06-28 01:49:00.565] No target revision found.
[2025-06-28 01:49:00.594] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/extension-node-map.json
[2025-06-28 01:49:00.603] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/github-stats.json
[2025-06-28 01:49:00.626] [ComfyUI-Manager] default cache updated: https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list.json
