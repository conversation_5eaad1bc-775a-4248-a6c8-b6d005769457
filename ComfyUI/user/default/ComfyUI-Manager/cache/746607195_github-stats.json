{"https://github.com/0x-jerry/comfyui-rembg": {"author_account_age_days": 3577, "last_update": "2025-04-07 09:23:31", "stars": 0}, "https://github.com/0xRavenBlack/ComfyUI-OOP": {"author_account_age_days": 1747, "last_update": "2025-03-02 11:59:14", "stars": 7}, "https://github.com/0xbitches/ComfyUI-LCM": {"author_account_age_days": 899, "last_update": "2023-11-11 21:24:33", "stars": 257}, "https://github.com/1038lab/ComfyUI-EdgeTTS": {"author_account_age_days": 819, "last_update": "2025-06-20 18:14:14", "stars": 46}, "https://github.com/1038lab/ComfyUI-JoyCaption": {"author_account_age_days": 819, "last_update": "2025-06-12 21:15:17", "stars": 19}, "https://github.com/1038lab/ComfyUI-LBM": {"author_account_age_days": 819, "last_update": "2025-05-27 17:37:31", "stars": 46}, "https://github.com/1038lab/ComfyUI-MegaTTS": {"author_account_age_days": 819, "last_update": "2025-06-19 19:12:51", "stars": 42}, "https://github.com/1038lab/ComfyUI-OmniGen": {"author_account_age_days": 819, "last_update": "2025-04-18 18:33:34", "stars": 277}, "https://github.com/1038lab/ComfyUI-Pollinations": {"author_account_age_days": 819, "last_update": "2025-06-19 19:48:05", "stars": 34}, "https://github.com/1038lab/ComfyUI-RMBG": {"author_account_age_days": 819, "last_update": "2025-06-01 21:28:57", "stars": 1091}, "https://github.com/1038lab/ComfyUI-ReduxFineTune": {"author_account_age_days": 819, "last_update": "2025-06-21 19:10:36", "stars": 53}, "https://github.com/1038lab/ComfyUI-SparkTTS": {"author_account_age_days": 819, "last_update": "2025-04-15 19:28:39", "stars": 105}, "https://github.com/1038lab/ComfyUI-WildPromptor": {"author_account_age_days": 819, "last_update": "2025-04-18 18:54:52", "stars": 36}, "https://github.com/111496583yzy/comfyui-PuzzleCrack-Effect": {"author_account_age_days": 2250, "last_update": "2025-01-13 10:15:44", "stars": 3}, "https://github.com/11cafe/comfyui-workspace-manager": {"author_account_age_days": 572, "last_update": "2025-04-16 14:02:54", "stars": 1313}, "https://github.com/11dogzi/CYBERPUNK-STYLE-DIY": {"author_account_age_days": 493, "last_update": "2025-06-20 07:49:50", "stars": 70}, "https://github.com/11dogzi/ComfUI-EGAdapterMadAssistant": {"author_account_age_days": 493, "last_update": "2024-08-02 05:24:19", "stars": 38}, "https://github.com/11dogzi/Comfyui-ergouzi-Nodes": {"author_account_age_days": 493, "last_update": "2024-08-23 12:04:09", "stars": 83}, "https://github.com/11dogzi/Comfyui-ergouzi-kaiguan": {"author_account_age_days": 493, "last_update": "2025-06-22 14:48:37", "stars": 67}, "https://github.com/11dogzi/Comfyui-ergouzi-samplers": {"author_account_age_days": 493, "last_update": "2024-06-28 05:28:05", "stars": 26}, "https://github.com/1hew/ComfyUI-1hewNodes": {"author_account_age_days": 815, "last_update": "2025-06-25 09:27:10", "stars": 3}, "https://github.com/1mckw/Comfyui-Gelbooru": {"author_account_age_days": 1057, "last_update": "2025-04-06 14:11:25", "stars": 4}, "https://github.com/1zhangyy1/comfyui-vidu-nodes": {"author_account_age_days": 826, "last_update": "2025-03-21 12:25:22", "stars": 8}, "https://github.com/2frames/ComfyUI-AQnodes": {"author_account_age_days": 357, "last_update": "2025-06-08 12:51:07", "stars": 1}, "https://github.com/2kpr/ComfyUI-PMRF": {"author_account_age_days": 1286, "last_update": "2024-10-11 00:11:40", "stars": 204}, "https://github.com/2kpr/ComfyUI-UltraPixel": {"author_account_age_days": 1286, "last_update": "2024-07-27 14:52:10", "stars": 225}, "https://github.com/311-code/ComfyUI-MagicClip_Strength": {"author_account_age_days": 3139, "last_update": "2024-09-22 12:07:40", "stars": 2}, "https://github.com/***********/ComfyUI-GrsAI": {"author_account_age_days": 2371, "last_update": "2025-06-23 17:00:45", "stars": 7}, "https://github.com/42lux/ComfyUI-42lux": {"author_account_age_days": 4066, "last_update": "2024-12-19 10:21:03", "stars": 10}, "https://github.com/*********/ComfyUI-GPT4V-Image-Captioner": {"author_account_age_days": 781, "last_update": "2025-04-06 02:06:59", "stars": 27}, "https://github.com/45uee/ComfyUI-Color_Transfer": {"author_account_age_days": 2670, "last_update": "2025-05-12 22:12:06", "stars": 29}, "https://github.com/54rt1n/ComfyUI-DareMerge": {"author_account_age_days": 4418, "last_update": "2025-03-27 14:57:35", "stars": 89}, "https://github.com/5x00/ComfyUI-PiAPI-Faceswap": {"author_account_age_days": 1339, "last_update": "2025-01-12 14:49:09", "stars": 2}, "https://github.com/5x00/ComfyUI-VLM-Captions": {"author_account_age_days": 1339, "last_update": "2025-01-04 21:27:47", "stars": 6}, "https://github.com/6174/comflowy-nodes": {"author_account_age_days": 4486, "last_update": "2024-12-03 13:31:04", "stars": 14}, "https://github.com/*********/ComfyUI-3D-MeshTool": {"author_account_age_days": 2380, "last_update": "2024-10-18 09:59:54", "stars": 23}, "https://github.com/*********/ComfyUI-WJNodes": {"author_account_age_days": 2380, "last_update": "2025-06-16 03:44:19", "stars": 11}, "https://github.com/*********/ComfyUI_MaskGCT": {"author_account_age_days": 2380, "last_update": "2025-03-05 09:15:32", "stars": 27}, "https://github.com/80sVectorz/ComfyUI-Static-Primitives": {"author_account_age_days": 1838, "last_update": "2025-03-14 11:42:07", "stars": 11}, "https://github.com/834t/ComfyUI_834t_scene_composer": {"author_account_age_days": 565, "last_update": "2025-06-23 10:55:47", "stars": 1}, "https://github.com/852wa/ComfyUI-AAP": {"author_account_age_days": 730, "last_update": "2025-01-29 13:21:59", "stars": 9}, "https://github.com/852wa/ComfyUI-ColorshiftColor": {"author_account_age_days": 730, "last_update": "2025-02-01 12:17:38", "stars": 48}, "https://github.com/A043-studios/ComfyUI-ASDF-Pixel-Sort-Nodes": {"author_account_age_days": 1016, "last_update": "2025-06-12 12:51:33", "stars": 3}, "https://github.com/A043-studios/Comfyui-ascii-generator": {"author_account_age_days": 1016, "last_update": "2025-06-25 14:55:32", "stars": 0}, "https://github.com/A043-studios/comfyui-deforum-x-flux-nodes": {"author_account_age_days": 1016, "last_update": "2025-06-10 14:28:27", "stars": 0}, "https://github.com/A043-studios/comfyui-pixel3dmm": {"author_account_age_days": 1016, "last_update": "2025-06-10 08:11:51", "stars": 3}, "https://github.com/A4P7J1N7M05OT/ComfyUI-AutoColorGimp": {"author_account_age_days": 840, "last_update": "2024-05-23 00:26:10", "stars": 1}, "https://github.com/A4P7J1N7M05OT/ComfyUI-PixelOE-Wrapper": {"author_account_age_days": 840, "last_update": "2025-01-21 22:26:11", "stars": 11}, "https://github.com/AARG-FAN/Image-Vector-for-ComfyUI": {"author_account_age_days": 866, "last_update": "2024-06-23 14:56:16", "stars": 137}, "https://github.com/AEmotionStudio/ComfyUI-ChristmasTheme": {"author_account_age_days": 470, "last_update": "2025-06-03 13:01:58", "stars": 40}, "https://github.com/AEmotionStudio/ComfyUI-DiscordSend": {"author_account_age_days": 470, "last_update": "2025-06-03 13:03:23", "stars": 9}, "https://github.com/AEmotionStudio/ComfyUI-EnhancedLinksandNodes": {"author_account_age_days": 470, "last_update": "2025-06-03 13:02:36", "stars": 38}, "https://github.com/AEmotionStudio/ComfyUI-MagnifyGlass": {"author_account_age_days": 470, "last_update": "2025-06-13 19:26:31", "stars": 12}, "https://github.com/AEmotionStudio/ComfyUI-ShaderNoiseKSampler": {"author_account_age_days": 470, "last_update": "2025-06-20 08:52:02", "stars": 46}, "https://github.com/AI2lab/comfyUI-siliconflow-api-2lab": {"author_account_age_days": 560, "last_update": "2024-08-01 15:13:33", "stars": 7}, "https://github.com/AIDC-AI/ComfyUI-Copilot": {"author_account_age_days": 378, "last_update": "2025-06-26 11:41:59", "stars": 1892}, "https://github.com/AIExplorer25/ComfyUI_AutoDownloadModels": {"author_account_age_days": 3834, "last_update": "2025-04-05 22:05:47", "stars": 14}, "https://github.com/AIExplorer25/ComfyUI_ChatGptHelper": {"author_account_age_days": 3834, "last_update": "2025-05-18 19:11:40", "stars": 0}, "https://github.com/AIExplorer25/ComfyUI_ImageCaptioner": {"author_account_age_days": 3834, "last_update": "2025-06-07 19:49:36", "stars": 0}, "https://github.com/AIFSH/AniTalker-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-08-06 03:08:44", "stars": 5}, "https://github.com/AIFSH/ComfyUI-3d-photo-inpainting": {"author_account_age_days": 599, "last_update": "2024-06-19 13:59:49", "stars": 14}, "https://github.com/AIFSH/ComfyUI-AuraSR": {"author_account_age_days": 599, "last_update": "2024-06-27 14:00:16", "stars": 22}, "https://github.com/AIFSH/ComfyUI-DiffSynth-Studio": {"author_account_age_days": 599, "last_update": "2024-08-05 08:48:03", "stars": 83}, "https://github.com/AIFSH/ComfyUI-FishSpeech": {"author_account_age_days": 599, "last_update": "2024-05-23 01:18:49", "stars": 38}, "https://github.com/AIFSH/ComfyUI-GPT_SoVITS": {"author_account_age_days": 599, "last_update": "2024-08-09 22:00:45", "stars": 236}, "https://github.com/AIFSH/ComfyUI-Hallo": {"author_account_age_days": 599, "last_update": "2024-06-24 06:43:23", "stars": 306}, "https://github.com/AIFSH/ComfyUI-I2V-Adapter": {"author_account_age_days": 599, "last_update": "2024-07-02 01:59:49", "stars": 22}, "https://github.com/AIFSH/ComfyUI-IP_LAP": {"author_account_age_days": 599, "last_update": "2024-06-14 07:05:39", "stars": 34}, "https://github.com/AIFSH/ComfyUI-Live2DViewer": {"author_account_age_days": 599, "last_update": "2024-06-14 07:04:49", "stars": 8}, "https://github.com/AIFSH/ComfyUI-MARS5-TTS": {"author_account_age_days": 599, "last_update": "2024-07-02 02:00:28", "stars": 29}, "https://github.com/AIFSH/ComfyUI-MimicBrush": {"author_account_age_days": 599, "last_update": "2024-06-17 22:26:53", "stars": 113}, "https://github.com/AIFSH/ComfyUI-MimicMotion": {"author_account_age_days": 599, "last_update": "2024-08-06 06:21:16", "stars": 374}, "https://github.com/AIFSH/ComfyUI-MuseTalk_FSH": {"author_account_age_days": 599, "last_update": "2024-06-14 07:05:19", "stars": 21}, "https://github.com/AIFSH/ComfyUI-RVC": {"author_account_age_days": 599, "last_update": "2024-06-14 07:05:25", "stars": 24}, "https://github.com/AIFSH/ComfyUI-UVR5": {"author_account_age_days": 599, "last_update": "2024-06-20 07:31:20", "stars": 95}, "https://github.com/AIFSH/ComfyUI-UniAnimate": {"author_account_age_days": 599, "last_update": "2024-06-30 09:20:25", "stars": 39}, "https://github.com/AIFSH/ComfyUI-WhisperX": {"author_account_age_days": 599, "last_update": "2025-04-01 00:14:44", "stars": 50}, "https://github.com/AIFSH/ComfyUI-XTTS": {"author_account_age_days": 599, "last_update": "2024-06-24 09:45:59", "stars": 59}, "https://github.com/AIFSH/ComfyUI_V-Express": {"author_account_age_days": 599, "last_update": "2024-06-23 09:54:57", "stars": 87}, "https://github.com/AIFSH/CosyVoice-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-09-10 22:21:37", "stars": 264}, "https://github.com/AIFSH/DHLive-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-11-14 01:45:45", "stars": 24}, "https://github.com/AIFSH/DiffMorpher-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-07-17 01:24:59", "stars": 16}, "https://github.com/AIFSH/DiffSynth-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-09-07 12:23:07", "stars": 0}, "https://github.com/AIFSH/EchoMimicV2-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-12-08 08:53:21", "stars": 57}, "https://github.com/AIFSH/EzAudio-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-10-08 05:22:46", "stars": 9}, "https://github.com/AIFSH/F5-TTS-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-11-14 01:43:03", "stars": 36}, "https://github.com/AIFSH/FancyVideo-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-10-12 07:21:51", "stars": 36}, "https://github.com/AIFSH/FireRedTTS-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-10-24 01:18:51", "stars": 12}, "https://github.com/AIFSH/GSTTS-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-08-25 03:23:24", "stars": 40}, "https://github.com/AIFSH/HivisionIDPhotos-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-09-16 14:16:06", "stars": 149}, "https://github.com/AIFSH/IMAGDressing-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-11-14 01:44:02", "stars": 61}, "https://github.com/AIFSH/JoyHallo-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-11-14 01:44:39", "stars": 8}, "https://github.com/AIFSH/MaskGCT-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-11-14 01:40:15", "stars": 61}, "https://github.com/AIFSH/MiniMates-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-11-14 01:36:30", "stars": 29}, "https://github.com/AIFSH/OmniGen-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-11-14 01:37:33", "stars": 209}, "https://github.com/AIFSH/PyramidFlow-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-10-10 13:59:16", "stars": 14}, "https://github.com/AIFSH/RealisDance-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-09-13 14:38:59", "stars": 49}, "https://github.com/AIFSH/SemiChat-ComfyUI": {"author_account_age_days": 599, "last_update": "2025-02-19 23:21:48", "stars": 11}, "https://github.com/AIFSH/SenseVoice-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-07-16 06:41:25", "stars": 14}, "https://github.com/AIFSH/StyleShot-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-08-17 00:25:29", "stars": 4}, "https://github.com/AIFSH/VideoSys-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-09-01 09:11:57", "stars": 6}, "https://github.com/AIFSH/ViewCrafter-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-09-19 11:11:25", "stars": 8}, "https://github.com/AIFSH/VocalSeparation-ComfyUI": {"author_account_age_days": 599, "last_update": "2024-10-24 07:16:37", "stars": 18}, "https://github.com/AIGCTeam/ComfyUI_kkTranslator_nodes": {"author_account_age_days": 577, "last_update": "2024-09-13 07:34:18", "stars": 7}, "https://github.com/AIGODLIKE/AIGODLIKE-COMFYUI-TRANSLATION": {"author_account_age_days": 868, "last_update": "2025-03-24 00:01:12", "stars": 2270}, "https://github.com/AIGODLIKE/AIGODLIKE-ComfyUI-Studio": {"author_account_age_days": 868, "last_update": "2025-01-06 11:31:20", "stars": 341}, "https://github.com/AIGODLIKE/ComfyUI-CUP": {"author_account_age_days": 868, "last_update": "2025-05-12 07:53:11", "stars": 48}, "https://github.com/AIGODLIKE/ComfyUI-ToonCrafter": {"author_account_age_days": 868, "last_update": "2024-07-17 02:28:49", "stars": 366}, "https://github.com/AIPOQUE/ComfyUI-APQNodes": {"author_account_age_days": 250, "last_update": "2024-11-21 08:56:49", "stars": 102}, "https://github.com/AIToldMeTo/comfyui-cache-cleaner": {"author_account_age_days": 197, "last_update": "2025-05-29 10:44:45", "stars": 6}, "https://github.com/AIWarper/ComfyUI-NormalCrafterWrapper": {"author_account_age_days": 245, "last_update": "2025-05-15 17:06:29", "stars": 61}, "https://github.com/AIWarper/ComfyUI-WarperNodes": {"author_account_age_days": 245, "last_update": "2025-06-25 02:11:15", "stars": 10}, "https://github.com/AInseven/ComfyUI-fastblend": {"author_account_age_days": 2050, "last_update": "2024-11-22 03:32:25", "stars": 218}, "https://github.com/AIrjen/OneButtonPrompt": {"author_account_age_days": 810, "last_update": "2025-03-28 09:17:10", "stars": 992}, "https://github.com/AJO-reading/ComfyUI-AjoNodes": {"author_account_age_days": 216, "last_update": "2025-06-16 08:10:10", "stars": 7}, "https://github.com/AKharytonchyk/ComfyUI-telegram-bot-node": {"author_account_age_days": 3249, "last_update": "2025-06-23 22:03:39", "stars": 0}, "https://github.com/ALatentPlace/ComfyUI_yanc": {"author_account_age_days": 1827, "last_update": "2025-01-22 14:44:17", "stars": 63}, "https://github.com/ALatentPlace/YANC_LMStudio": {"author_account_age_days": 1827, "last_update": "2025-04-30 14:57:13", "stars": 10}, "https://github.com/APZmedia/APZmedia-comfy-together-lora": {"author_account_age_days": 2844, "last_update": "2025-02-15 13:14:17", "stars": 0}, "https://github.com/APZmedia/APZmedia-comfyui-fast-image-save": {"author_account_age_days": 2844, "last_update": "2025-04-21 19:22:43", "stars": 4}, "https://github.com/APZmedia/ComfyUI-APZmedia-cleanName-from-string": {"author_account_age_days": 2844, "last_update": "2025-04-21 19:22:10", "stars": 7}, "https://github.com/ARZUMATA/ComfyUI-ARZUMATA": {"author_account_age_days": 2134, "last_update": "2025-04-08 08:00:15", "stars": 4}, "https://github.com/ARZUMATA/ComfyUI-ARZUMATA-PixelIt": {"author_account_age_days": 2134, "last_update": "2025-06-04 13:11:52", "stars": 1}, "https://github.com/ARZUMATA/ComfyUI-ARZUMATA-Qwen2": {"author_account_age_days": 2134, "last_update": "2025-06-04 13:10:22", "stars": 1}, "https://github.com/Aaron-CHM/ComfyUI-z-a1111-sd-webui-DanTagGen": {"author_account_age_days": 1893, "last_update": "2024-07-17 03:55:26", "stars": 4}, "https://github.com/AbdullahAlfaraj/Comfy-Photoshop-SD": {"author_account_age_days": 4035, "last_update": "2024-06-14 07:04:37", "stars": 294}, "https://github.com/AbyssBadger0/ComfyUI_BadgerTools": {"author_account_age_days": 857, "last_update": "2024-11-12 11:10:16", "stars": 8}, "https://github.com/AbyssBadger0/ComfyUI_Kolors_awesome_prompts": {"author_account_age_days": 857, "last_update": "2024-08-29 15:19:06", "stars": 5}, "https://github.com/Acly/comfyui-inpaint-nodes": {"author_account_age_days": 4172, "last_update": "2025-03-31 09:53:40", "stars": 983}, "https://github.com/Acly/comfyui-tooling-nodes": {"author_account_age_days": 4172, "last_update": "2025-06-15 10:32:50", "stars": 511}, "https://github.com/AconexOfficial/ComfyUI_GOAT_Nodes": {"author_account_age_days": 1848, "last_update": "2025-05-14 08:38:12", "stars": 9}, "https://github.com/Aerse/ComfyUI-Seed-Nodes": {"author_account_age_days": 3745, "last_update": "2025-06-10 08:19:10", "stars": 5}, "https://github.com/AgencyMind/ComfyUI-GPU-Preprocessor-Wrapper": {"author_account_age_days": 227, "last_update": "2025-06-13 08:15:25", "stars": 2}, "https://github.com/AgencyMind/ComfyUI-Satori": {"author_account_age_days": 227, "last_update": "2025-06-26 00:58:25", "stars": 0}, "https://github.com/AhBumm/ComfyUI_BillBum_APIset_Nodes": {"author_account_age_days": 1176, "last_update": "2025-06-25 05:15:21", "stars": 10}, "https://github.com/AiMiDi/ComfyUI-Aimidi-nodes": {"author_account_age_days": 1643, "last_update": "2024-06-20 17:26:02", "stars": 0}, "https://github.com/AkashKarnatak/ComfyUI_faishme": {"author_account_age_days": 2120, "last_update": "2025-03-10 20:04:22", "stars": 0}, "https://github.com/Aksaz/comfyui-seamless-clone": {"author_account_age_days": 250, "last_update": "2025-05-20 07:08:24", "stars": 8}, "https://github.com/AlekPet/ComfyUI_Custom_Nodes_AlekPet": {"author_account_age_days": 3067, "last_update": "2025-06-26 20:25:22", "stars": 1256}, "https://github.com/Alexankharin/camera-comfyUI": {"author_account_age_days": 2488, "last_update": "2025-06-21 21:59:12", "stars": 8}, "https://github.com/Aljnk/ComfyUI-JNK-Tiny-Nodes": {"author_account_age_days": 3770, "last_update": "2025-06-04 07:12:02", "stars": 1}, "https://github.com/Altair200333/ComfyUI_Flux_1.1_PRO": {"author_account_age_days": 3077, "last_update": "2025-03-23 19:29:25", "stars": 0}, "https://github.com/Alvaroeai/ComfyUI-Text2Json": {"author_account_age_days": 4069, "last_update": "2024-11-26 16:40:31", "stars": 1}, "https://github.com/Amorano/Jovi_Capture": {"author_account_age_days": 5579, "last_update": "2025-05-31 18:38:22", "stars": 5}, "https://github.com/Amorano/Jovi_Colorizer": {"author_account_age_days": 5579, "last_update": "2025-05-22 20:00:19", "stars": 6}, "https://github.com/Amorano/Jovi_GLSL": {"author_account_age_days": 5579, "last_update": "2025-06-18 02:55:59", "stars": 16}, "https://github.com/Amorano/Jovi_MIDI": {"author_account_age_days": 5579, "last_update": "2025-05-05 04:11:06", "stars": 6}, "https://github.com/Amorano/Jovi_Measure": {"author_account_age_days": 5579, "last_update": "2025-05-05 04:10:36", "stars": 2}, "https://github.com/Amorano/Jovi_Spout": {"author_account_age_days": 5579, "last_update": "2025-05-29 17:34:42", "stars": 6}, "https://github.com/Amorano/Jovimetrix": {"author_account_age_days": 5579, "last_update": "2025-06-18 16:48:38", "stars": 357}, "https://github.com/Andro-Meta/ComfyUI-Ovis2": {"author_account_age_days": 639, "last_update": "2025-03-24 04:27:56", "stars": 5}, "https://github.com/AngelCookies/ComfyUI-Seed-Tracker": {"author_account_age_days": 1189, "last_update": "2025-06-23 23:56:50", "stars": 0}, "https://github.com/Anibaaal/ComfyUI-UX-Nodes": {"author_account_age_days": 3744, "last_update": "2025-01-23 13:35:49", "stars": 2}, "https://github.com/AonekoSS/ComfyUI-LoRA-Tuner": {"author_account_age_days": 4459, "last_update": "2025-03-27 17:07:38", "stars": 9}, "https://github.com/AonekoSS/ComfyUI-SimpleCounter": {"author_account_age_days": 4459, "last_update": "2025-03-27 17:08:39", "stars": 1}, "https://github.com/ArcherFMY/Diffusion360_ComfyUI": {"author_account_age_days": 3757, "last_update": "2025-03-17 06:08:17", "stars": 44}, "https://github.com/ArdeniusAI/ComfyUI-Ardenius": {"author_account_age_days": 494, "last_update": "2024-11-24 09:57:46", "stars": 5}, "https://github.com/Arkanun/ReadCSV_ComfyUI": {"author_account_age_days": 3317, "last_update": "2025-02-05 23:06:48", "stars": 0}, "https://github.com/ArtBot2023/CharacterFaceSwap": {"author_account_age_days": 661, "last_update": "2024-05-22 20:53:09", "stars": 90}, "https://github.com/ArtHommage/HommageTools": {"author_account_age_days": 900, "last_update": "2025-05-20 20:40:25", "stars": 2}, "https://github.com/ArtsticH/ComfyUI_EasyKitHT_NodeAlignPro": {"author_account_age_days": 456, "last_update": "2025-05-01 01:49:07", "stars": 11}, "https://github.com/AshMartian/ComfyUI-DirGir": {"author_account_age_days": 4954, "last_update": "2025-05-04 03:34:19", "stars": 25}, "https://github.com/AstroCorp/ComfyUI-AstroCorp-Nodes": {"author_account_age_days": 3208, "last_update": "2025-06-20 12:27:42", "stars": 0}, "https://github.com/AuroBit/ComfyUI-AnimateAnyone-reproduction": {"author_account_age_days": 757, "last_update": "2024-06-14 09:03:24", "stars": 37}, "https://github.com/AuroBit/ComfyUI-OOTDiffusion": {"author_account_age_days": 757, "last_update": "2024-07-12 03:49:27", "stars": 461}, "https://github.com/AustinMroz/ComfyUI-DynamicOversampling": {"author_account_age_days": 4442, "last_update": "2024-06-14 07:06:51", "stars": 0}, "https://github.com/AustinMroz/ComfyUI-MinCache": {"author_account_age_days": 4442, "last_update": "2024-12-25 18:52:07", "stars": 2}, "https://github.com/AustinMroz/ComfyUI-SpliceTools": {"author_account_age_days": 4442, "last_update": "2024-06-14 07:07:21", "stars": 6}, "https://github.com/AustinMroz/ComfyUI-WorkflowCheckpointing": {"author_account_age_days": 4442, "last_update": "2024-10-17 19:59:40", "stars": 11}, "https://github.com/Auttasak-L/ComfyUI-ImageCropper": {"author_account_age_days": 3016, "last_update": "2024-05-23 05:04:53", "stars": 1}, "https://github.com/BAIS1C/ComfyUI_RSS_Feed_Reader": {"author_account_age_days": 857, "last_update": "2025-04-24 14:09:18", "stars": 4}, "https://github.com/BIMer-99/ComfyUI_FishSpeech_EX": {"author_account_age_days": 1600, "last_update": "2024-12-21 11:35:08", "stars": 7}, "https://github.com/BIMer-99/Comfyui_Hunyuan3D_EX": {"author_account_age_days": 1600, "last_update": "2024-12-09 17:50:23", "stars": 7}, "https://github.com/BNP1111/comfyui_flux_corrector": {"author_account_age_days": 863, "last_update": "2025-04-25 16:47:45", "stars": 4}, "https://github.com/BXYMartin/ComfyUI-InstantIDUtils": {"author_account_age_days": 2810, "last_update": "2024-05-23 00:08:50", "stars": 3}, "https://github.com/BZcreativ/ComfyUI-FLUX-TOGETHER-API": {"author_account_age_days": 3604, "last_update": "2024-11-02 14:45:28", "stars": 3}, "https://github.com/BadCafeCode/masquerade-nodes-comfyui": {"author_account_age_days": 798, "last_update": "2024-06-19 04:16:54", "stars": 425}, "https://github.com/BahaC/ComfyUI-ZonosTTS": {"author_account_age_days": 1673, "last_update": "2025-02-19 06:28:38", "stars": 18}, "https://github.com/Beinsezii/bsz-cui-extras": {"author_account_age_days": 2593, "last_update": "2024-05-22 20:46:45", "stars": 24}, "https://github.com/Bellzs/ComfyUI-LoRA-Assistant": {"author_account_age_days": 3374, "last_update": "2025-01-27 09:47:46", "stars": 15}, "https://github.com/BenNarum/ComfyUI_CAS": {"author_account_age_days": 3440, "last_update": "2024-07-13 12:00:40", "stars": 3}, "https://github.com/BenNarum/SigmaWaveFormNode": {"author_account_age_days": 3440, "last_update": "2024-06-20 15:20:35", "stars": 5}, "https://github.com/BennyKok/comfyui-deploy": {"author_account_age_days": 3363, "last_update": "2025-06-26 21:07:48", "stars": 1362}, "https://github.com/BetaDoggo/ComfyUI-Cloud-APIs": {"author_account_age_days": 1165, "last_update": "2025-05-01 06:24:47", "stars": 36}, "https://github.com/BetaDoggo/ComfyUI-FastSDCPU": {"author_account_age_days": 1165, "last_update": "2024-09-16 05:34:01", "stars": 9}, "https://github.com/BetaDoggo/ComfyUI-Gatcha-Embedding": {"author_account_age_days": 1165, "last_update": "2024-08-28 00:24:01", "stars": 1}, "https://github.com/BetaDoggo/ComfyUI-VideoPlayer": {"author_account_age_days": 1165, "last_update": "2024-08-05 04:45:12", "stars": 17}, "https://github.com/BetaDoggo/ComfyUI-WDV-Nodes": {"author_account_age_days": 1165, "last_update": "2024-08-01 07:59:10", "stars": 1}, "https://github.com/BetaDoggo/ComfyUI-YetAnotherSafetyChecker": {"author_account_age_days": 1165, "last_update": "2024-07-19 18:11:11", "stars": 5}, "https://github.com/Big-Idea-Technology/ComfyUI-Book-Tools": {"author_account_age_days": 1239, "last_update": "2025-04-21 15:40:34", "stars": 26}, "https://github.com/Big-Idea-Technology/ComfyUI_LLM_Node": {"author_account_age_days": 1239, "last_update": "2025-04-19 11:58:55", "stars": 66}, "https://github.com/BigStationW/ComfyUi-RescaleCFGAdvanced": {"author_account_age_days": 53, "last_update": "2025-05-07 18:10:18", "stars": 25}, "https://github.com/BigWhiteFly/ComfyUI-ImageConcat": {"author_account_age_days": 2707, "last_update": "2025-05-21 01:16:27", "stars": 0}, "https://github.com/Billius-AI/ComfyUI-Path-Helper": {"author_account_age_days": 502, "last_update": "2024-05-22 23:25:08", "stars": 18}, "https://github.com/Bin-sam/DynamicPose-ComfyUI": {"author_account_age_days": 301, "last_update": "2024-09-11 12:09:11", "stars": 5}, "https://github.com/Black-Lioness/ComfyUI-PromptUtils": {"author_account_age_days": 1223, "last_update": "2024-11-22 03:05:11", "stars": 2}, "https://github.com/BlackVortexAI/ComfyUI-BVortexNodes": {"author_account_age_days": 323, "last_update": "2024-10-23 09:19:54", "stars": 2}, "https://github.com/BlakeOne/ComfyUI-CustomScheduler": {"author_account_age_days": 2900, "last_update": "2024-05-23 00:23:56", "stars": 17}, "https://github.com/BlakeOne/ComfyUI-NodePresets": {"author_account_age_days": 2900, "last_update": "2024-05-23 00:24:07", "stars": 12}, "https://github.com/BlakeOne/ComfyUI-NodeReset": {"author_account_age_days": 2900, "last_update": "2024-05-23 00:24:18", "stars": 3}, "https://github.com/BlakeOne/ComfyUI-SchedulerMixer": {"author_account_age_days": 2900, "last_update": "2024-05-23 00:23:44", "stars": 10}, "https://github.com/BlenderNeko/ComfyUI_ADV_CLIP_emb": {"author_account_age_days": 845, "last_update": "2024-08-07 15:13:31", "stars": 388}, "https://github.com/BlenderNeko/ComfyUI_Cutoff": {"author_account_age_days": 845, "last_update": "2024-05-22 15:01:45", "stars": 388}, "https://github.com/BlenderNeko/ComfyUI_Noise": {"author_account_age_days": 845, "last_update": "2024-06-10 16:38:48", "stars": 298}, "https://github.com/BlenderNeko/ComfyUI_SeeCoder": {"author_account_age_days": 845, "last_update": "2024-05-22 14:57:04", "stars": 38}, "https://github.com/BlenderNeko/ComfyUI_TiledKSampler": {"author_account_age_days": 845, "last_update": "2024-05-22 14:56:49", "stars": 381}, "https://github.com/Blonicx/ComfyUI-X-Rework": {"author_account_age_days": 1090, "last_update": "2025-05-07 17:02:20", "stars": 1}, "https://github.com/BlueprintCoding/ComfyUI_AIDocsClinicalTools": {"author_account_age_days": 812, "last_update": "2025-02-22 17:07:39", "stars": 4}, "https://github.com/BobRandomNumber/ComfyUI-DiaTTS": {"author_account_age_days": 211, "last_update": "2025-06-02 03:02:19", "stars": 8}, "https://github.com/BobsBlazed/Bobs-Lora-Loader": {"author_account_age_days": 2581, "last_update": "2025-06-27 02:18:59", "stars": 1}, "https://github.com/BobsBlazed/Bobs_Latent_Optimizer": {"author_account_age_days": 2581, "last_update": "2025-06-04 03:00:27", "stars": 38}, "https://github.com/BoyuanJiang/FitDiT-ComfyUI": {"author_account_age_days": 3449, "last_update": "2025-01-21 12:09:05", "stars": 97}, "https://github.com/Bria-AI/ComfyUI-BRIA-API": {"author_account_age_days": 1845, "last_update": "2025-06-16 13:24:17", "stars": 43}, "https://github.com/BuffMcBigHuge/ComfyUI-Zonos": {"author_account_age_days": 3280, "last_update": "2025-04-29 21:48:07", "stars": 71}, "https://github.com/Burgstall-labs/ComfyUI-BETA-Cropnodes": {"author_account_age_days": 158, "last_update": "2025-06-26 11:35:33", "stars": 4}, "https://github.com/Burgstall-labs/ComfyUI-BETA-Helpernodes": {"author_account_age_days": 158, "last_update": "2025-06-26 11:35:33", "stars": 4}, "https://github.com/Burgstall-labs/ComfyUI-BS-Textchop": {"author_account_age_days": 158, "last_update": "2025-04-05 07:45:54", "stars": 0}, "https://github.com/Burgstall-labs/ComfyUI-BS_Kokoro-onnx": {"author_account_age_days": 158, "last_update": "2025-01-19 19:05:24", "stars": 36}, "https://github.com/CC-BryanOttho/ComfyUI_API_Manager": {"author_account_age_days": 852, "last_update": "2024-06-14 07:13:34", "stars": 23}, "https://github.com/CC-SUN6/ccsun_node": {"author_account_age_days": 736, "last_update": "2025-02-12 07:58:41", "stars": 0}, "https://github.com/CHAOSEA/ComfyUI_FaceAlignPaste": {"author_account_age_days": 333, "last_update": "2025-03-27 13:34:40", "stars": 12}, "https://github.com/CY-CHENYUE/ComfyUI-FramePack-HY": {"author_account_age_days": 565, "last_update": "2025-05-08 09:38:09", "stars": 18}, "https://github.com/CY-CHENYUE/ComfyUI-Free-GPU": {"author_account_age_days": 565, "last_update": "2025-02-16 16:30:36", "stars": 9}, "https://github.com/CY-CHENYUE/ComfyUI-GPT-API": {"author_account_age_days": 565, "last_update": "2025-04-17 09:51:35", "stars": 70}, "https://github.com/CY-CHENYUE/ComfyUI-Gemini-API": {"author_account_age_days": 565, "last_update": "2025-05-08 05:52:02", "stars": 217}, "https://github.com/CY-CHENYUE/ComfyUI-InpaintEasy": {"author_account_age_days": 565, "last_update": "2025-01-24 16:09:46", "stars": 74}, "https://github.com/CY-CHENYUE/ComfyUI-Janus-Pro": {"author_account_age_days": 565, "last_update": "2025-01-30 08:08:20", "stars": 608}, "https://github.com/CY-CHENYUE/ComfyUI-MiniCPM-Plus": {"author_account_age_days": 565, "last_update": "2024-10-09 06:56:04", "stars": 23}, "https://github.com/CY-CHENYUE/ComfyUI-MiniCPM-o": {"author_account_age_days": 565, "last_update": "2025-02-16 18:52:28", "stars": 34}, "https://github.com/CY-CHENYUE/ComfyUI-Molmo": {"author_account_age_days": 565, "last_update": "2024-10-14 15:06:36", "stars": 129}, "https://github.com/CY-CHENYUE/ComfyUI-OmniGenX": {"author_account_age_days": 565, "last_update": "2025-01-24 16:13:13", "stars": 6}, "https://github.com/CY-CHENYUE/ComfyUI-Redux-Prompt": {"author_account_age_days": 565, "last_update": "2025-01-24 15:43:29", "stars": 89}, "https://github.com/CYBERLOOM-INC/ComfyUI-nodes-hnmr": {"author_account_age_days": 617, "last_update": "2024-05-22 17:55:41", "stars": 9}, "https://github.com/CavinHuang/comfyui-nodes-docs": {"author_account_age_days": 3093, "last_update": "2025-03-26 02:12:29", "stars": 235}, "https://github.com/Chan-0312/ComfyUI-EasyDeforum": {"author_account_age_days": 2234, "last_update": "2024-05-22 23:22:14", "stars": 11}, "https://github.com/Chan-0312/ComfyUI-IPAnimate": {"author_account_age_days": 2234, "last_update": "2024-05-22 23:22:03", "stars": 73}, "https://github.com/Chan-0312/ComfyUI-Prompt-Preview": {"author_account_age_days": 2234, "last_update": "2024-06-14 09:01:37", "stars": 34}, "https://github.com/Chaoses-Ib/ComfyUI_Ib_CustomNodes": {"author_account_age_days": 2246, "last_update": "2025-02-08 13:11:30", "stars": 38}, "https://github.com/Charlweed/image_transceiver": {"author_account_age_days": 5465, "last_update": "2025-01-06 19:22:50", "stars": 2}, "https://github.com/Charonartist/Comfyui_gemini_tts_node": {"author_account_age_days": 361, "last_update": "2025-05-26 01:17:59", "stars": 0}, "https://github.com/Charonartist/comfyui-auto-lora-v2": {"author_account_age_days": 361, "last_update": "2025-06-17 15:00:30", "stars": 0}, "https://github.com/ChenDarYen/ComfyUI-NAG": {"author_account_age_days": 2268, "last_update": "2025-06-25 23:23:43", "stars": 36}, "https://github.com/ChenDarYen/ComfyUI-TimestepShiftModel": {"author_account_age_days": 2269, "last_update": "2025-01-07 18:22:10", "stars": 8}, "https://github.com/Chengym2023/ComfyUI-DeepSeek_Online": {"author_account_age_days": 767, "last_update": "2025-04-07 01:09:05", "stars": 0}, "https://github.com/ChrisColeTech/ComfyUI-Elegant-Resource-Monitor": {"author_account_age_days": 2780, "last_update": "2024-09-23 21:48:27", "stars": 13}, "https://github.com/ChrisColeTech/ComfyUI-Line-counter": {"author_account_age_days": 2780, "last_update": "2025-03-12 00:07:25", "stars": 2}, "https://github.com/Chrisvenator/ComfyUI-Painting-by-colors-generator": {"author_account_age_days": 2096, "last_update": "2025-06-03 07:56:17", "stars": 0}, "https://github.com/ClownsharkBatwing/RES4LYF": {"author_account_age_days": 397, "last_update": "2025-06-27 02:26:51", "stars": 272}, "https://github.com/Clybius/ComfyUI-ClybsChromaNodes": {"author_account_age_days": 2101, "last_update": "2025-06-18 17:09:18", "stars": 8}, "https://github.com/Clybius/ComfyUI-Extra-Samplers": {"author_account_age_days": 2101, "last_update": "2024-11-15 17:21:45", "stars": 86}, "https://github.com/Clybius/ComfyUI-Latent-Modifiers": {"author_account_age_days": 2101, "last_update": "2024-06-14 09:02:44", "stars": 80}, "https://github.com/CoiiChan/ComfyUI-Depth-Visualization-Advanced": {"author_account_age_days": 2264, "last_update": "2025-06-17 03:43:27", "stars": 4}, "https://github.com/CoiiChan/ComfyUI-FuncAsTexture-CoiiNode": {"author_account_age_days": 2264, "last_update": "2025-06-24 03:34:32", "stars": 1}, "https://github.com/CoiiChan/comfyui-every-person-seg-coii": {"author_account_age_days": 2264, "last_update": "2025-06-20 08:51:56", "stars": 3}, "https://github.com/ComfyAssets/ComfyUI-KikoStats": {"author_account_age_days": 32, "last_update": "2025-06-21 15:03:38", "stars": 0}, "https://github.com/ComfyAssets/ComfyUI-KikoTools": {"author_account_age_days": 32, "last_update": "2025-06-20 15:03:50", "stars": 0}, "https://github.com/ComfyAssets/ComfyUI_PromptManager": {"author_account_age_days": 32, "last_update": "2025-06-19 23:56:57", "stars": 22}, "https://github.com/ComfyAssets/ComfyUI_Selectors": {"author_account_age_days": 32, "last_update": "2025-06-13 16:13:05", "stars": 0}, "https://github.com/ComfyUI-JH/ComfyUI-JH-Misc-Nodes": {"author_account_age_days": 190, "last_update": "2024-12-28 19:44:14", "stars": 1}, "https://github.com/ComfyUI-JH/ComfyUI-JH-XMP-Metadata-Nodes": {"author_account_age_days": 190, "last_update": "2024-12-31 21:44:05", "stars": 2}, "https://github.com/ComplexRobot/ComfyUI-Simple-VFI": {"author_account_age_days": 4791, "last_update": "2025-03-31 15:37:25", "stars": 0}, "https://github.com/Conor-Collins/ComfyUI-CoCoTools_IO": {"author_account_age_days": 541, "last_update": "2025-06-17 22:17:57", "stars": 45}, "https://github.com/CosmicLaca/ComfyUI_Primere_Nodes": {"author_account_age_days": 4022, "last_update": "2025-06-14 14:59:08", "stars": 124}, "https://github.com/CpreForEver/CFE_comfyui": {"author_account_age_days": 320, "last_update": "2024-12-09 01:38:42", "stars": 0}, "https://github.com/Creeper-MZ/comfyui_nai_api": {"author_account_age_days": 1370, "last_update": "2024-10-02 21:30:26", "stars": 0}, "https://github.com/Creepybits/ComfyUI-Creepy_nodes": {"author_account_age_days": 1975, "last_update": "2025-06-26 07:45:13", "stars": 11}, "https://github.com/Cryptyox/anaglyphTool-Comfyui": {"author_account_age_days": 1295, "last_update": "2025-05-13 16:12:27", "stars": 7}, "https://github.com/Curt-Park/human-parser-comfyui-node-in-pure-python": {"author_account_age_days": 3553, "last_update": "2025-03-18 00:51:34", "stars": 3}, "https://github.com/CyanAutumn/ComfyUi_Random_Manage_Cyan": {"author_account_age_days": 1469, "last_update": "2024-12-19 10:54:08", "stars": 3}, "https://github.com/Cyber-BlackCat/ComfyUI-Image-Vector": {"author_account_age_days": 782, "last_update": "2025-04-27 05:40:25", "stars": 2}, "https://github.com/Cyber-BlackCat/ComfyUI-MoneyMaker": {"author_account_age_days": 782, "last_update": "2025-05-29 02:15:11", "stars": 10}, "https://github.com/Cyber-BlackCat/ComfyUI_Auto_Caption": {"author_account_age_days": 782, "last_update": "2025-05-29 02:14:55", "stars": 13}, "https://github.com/Cyberschorsch/ComfyUI-checkpoint-config-loader": {"author_account_age_days": 5525, "last_update": "2024-07-31 13:54:16", "stars": 1}, "https://github.com/DJ-Tribefull/Comfyui_FOCUS_nodes": {"author_account_age_days": 156, "last_update": "2025-02-02 00:46:30", "stars": 5}, "https://github.com/Danand/ComfyUI-ComfyCouple": {"author_account_age_days": 4656, "last_update": "2024-08-10 22:24:01", "stars": 60}, "https://github.com/DanielHabib/ComfyUI-Voxels": {"author_account_age_days": 3951, "last_update": "2024-09-16 15:41:02", "stars": 4}, "https://github.com/Danteday/ComfyUI-NoteManager": {"author_account_age_days": 2684, "last_update": "2025-04-20 19:52:58", "stars": 10}, "https://github.com/DareFail/ComfyUI-Roboflow": {"author_account_age_days": 4955, "last_update": "2024-09-25 18:30:43", "stars": 34}, "https://github.com/DarioFT/ComfyUI-VideoDirCombiner": {"author_account_age_days": 3845, "last_update": "2025-03-08 13:58:12", "stars": 5}, "https://github.com/DataCTE/prompt_injection": {"author_account_age_days": 1145, "last_update": "2024-06-21 12:56:43", "stars": 91}, "https://github.com/Dayuppy/ComfyUI-DiscordWebhook": {"author_account_age_days": 4583, "last_update": "2024-10-12 05:12:07", "stars": 3}, "https://github.com/De-Zoomer/ComfyUI-DeZoomer-Nodes": {"author_account_age_days": 1222, "last_update": "2025-06-20 16:45:43", "stars": 7}, "https://github.com/DeJoker/pipeline-parallel-comfy": {"author_account_age_days": 3358, "last_update": "2024-07-29 06:59:37", "stars": 3}, "https://github.com/DebugPadawan/DebugPadawans-ComfyUI-Essentials": {"author_account_age_days": 168, "last_update": "2025-06-21 12:46:56", "stars": 0}, "https://github.com/Deep-Neko/ComfyUI_ascii_art": {"author_account_age_days": 122, "last_update": "2025-02-24 13:07:36", "stars": 1}, "https://github.com/Derfuu/Derfuu_ComfyUI_ModdedNodes": {"author_account_age_days": 2142, "last_update": "2024-06-22 02:12:19", "stars": 413}, "https://github.com/DesertPixelAi/ComfyUI-Desert-Pixel-Nodes": {"author_account_age_days": 513, "last_update": "2025-06-22 10:33:31", "stars": 16}, "https://github.com/DiaoDaiaChan/ComfyUI_API_Request": {"author_account_age_days": 852, "last_update": "2025-06-02 14:54:47", "stars": 3}, "https://github.com/DiffusionWave/PickResolution_DiffusionWave": {"author_account_age_days": 89, "last_update": "2025-05-19 23:16:22", "stars": 0}, "https://github.com/DigitalIO/ComfyUI-stable-wildcards": {"author_account_age_days": 4406, "last_update": "2025-03-17 17:53:33", "stars": 25}, "https://github.com/DimaChaichan/LAizypainter-Exporter-ComfyUI": {"author_account_age_days": 3440, "last_update": "2024-05-22 23:14:06", "stars": 6}, "https://github.com/Diohim/ComfyUI-Unusual-Tools": {"author_account_age_days": 147, "last_update": "2025-03-17 12:47:19", "stars": 0}, "https://github.com/Dobidop/ComfyStereo": {"author_account_age_days": 1827, "last_update": "2025-03-23 18:45:54", "stars": 23}, "https://github.com/DoctorDiffusion/ComfyUI-BEN": {"author_account_age_days": 709, "last_update": "2024-12-15 18:19:01", "stars": 41}, "https://github.com/DoctorDiffusion/ComfyUI-MediaMixer": {"author_account_age_days": 709, "last_update": "2024-12-05 03:05:44", "stars": 19}, "https://github.com/DoctorDiffusion/ComfyUI-Schedulizer": {"author_account_age_days": 709, "last_update": "2024-11-30 03:13:29", "stars": 6}, "https://github.com/DoctorDiffusion/ComfyUI-SnakeOil": {"author_account_age_days": 709, "last_update": "2024-12-31 00:59:19", "stars": 4}, "https://github.com/DoctorDiffusion/ComfyUI-basic-pitch": {"author_account_age_days": 709, "last_update": "2024-12-25 19:07:11", "stars": 1}, "https://github.com/Dontdrunk/ComfyUI-DD-Nodes": {"author_account_age_days": 3271, "last_update": "2025-06-24 11:10:27", "stars": 58}, "https://github.com/Dontdrunk/ComfyUI-DD-Translation": {"author_account_age_days": 3271, "last_update": "2025-06-25 09:26:31", "stars": 238}, "https://github.com/DrJKL/ComfyUI-Anchors": {"author_account_age_days": 5362, "last_update": "2024-06-20 18:23:00", "stars": 6}, "https://github.com/DrMWeigand/ComfyUI-StereoVision": {"author_account_age_days": 1399, "last_update": "2025-02-04 14:24:46", "stars": 9}, "https://github.com/DrMWeigand/ComfyUI_ColorImageDetection": {"author_account_age_days": 1399, "last_update": "2024-07-15 13:21:10", "stars": 3}, "https://github.com/DrStone71/ComfyUI-Prompt-Translator": {"author_account_age_days": 312, "last_update": "2025-06-17 00:22:24", "stars": 0}, "https://github.com/DraconicDragon/ComfyUI-RyuuNoodles": {"author_account_age_days": 1741, "last_update": "2025-06-25 03:59:19", "stars": 3}, "https://github.com/DraconicDragon/ComfyUI-Venice-API": {"author_account_age_days": 1741, "last_update": "2025-06-16 18:58:57", "stars": 5}, "https://github.com/DragonDiffusionbyBoyo/BoyoSupercoolWrapper": {"author_account_age_days": 179, "last_update": "2025-06-01 16:44:46", "stars": 4}, "https://github.com/DragonDiffusionbyBoyo/Boyonodes": {"author_account_age_days": 179, "last_update": "2025-05-19 22:59:06", "stars": 2}, "https://github.com/Duanyll/duanyll_nodepack": {"author_account_age_days": 3100, "last_update": "2025-03-12 08:41:14", "stars": 0}, "https://github.com/Eagle-CN/ComfyUI-Addoor": {"author_account_age_days": 2994, "last_update": "2025-04-25 01:03:58", "stars": 50}, "https://github.com/Easymode-ai/ComfyUI-BPT": {"author_account_age_days": 1640, "last_update": "2025-02-28 00:32:37", "stars": 8}, "https://github.com/Easymode-ai/ComfyUI-ShadowR": {"author_account_age_days": 1640, "last_update": "2025-02-21 20:53:27", "stars": 10}, "https://github.com/EeroHeikkinen/ComfyUI-eesahesNodes": {"author_account_age_days": 5084, "last_update": "2024-09-01 11:43:02", "stars": 70}, "https://github.com/Elaine-chennn/comfyui-overlay-media": {"author_account_age_days": 1510, "last_update": "2024-10-09 11:07:46", "stars": 0}, "https://github.com/Electrofried/ComfyUI-OpenAINode": {"author_account_age_days": 2987, "last_update": "2024-06-14 09:01:22", "stars": 28}, "https://github.com/EllangoK/ComfyUI-post-processing-nodes": {"author_account_age_days": 3146, "last_update": "2025-01-20 07:16:46", "stars": 224}, "https://github.com/EmAySee/ComfyUI_EmAySee_CustomNodes": {"author_account_age_days": 1956, "last_update": "2025-05-07 19:52:32", "stars": 1}, "https://github.com/EnragedAntelope/ComfyUI-ConstrainResolution": {"author_account_age_days": 337, "last_update": "2025-03-30 13:06:11", "stars": 5}, "https://github.com/EnragedAntelope/ComfyUI-Doubutsu-Describer": {"author_account_age_days": 337, "last_update": "2025-03-30 13:06:28", "stars": 10}, "https://github.com/EnragedAntelope/ComfyUI-EACloudNodes": {"author_account_age_days": 337, "last_update": "2025-04-22 00:44:56", "stars": 6}, "https://github.com/EnragedAntelope/comfyui-relight": {"author_account_age_days": 337, "last_update": "2025-05-16 16:06:28", "stars": 73}, "https://github.com/Erehr/ComfyUI-EreNodes": {"author_account_age_days": 3637, "last_update": "2025-06-23 06:46:10", "stars": 23}, "https://github.com/EvilBT/ComfyUI_SLK_joy_caption_two": {"author_account_age_days": 3966, "last_update": "2025-06-18 23:00:26", "stars": 573}, "https://github.com/Excidos/ComfyUI-Documents": {"author_account_age_days": 374, "last_update": "2024-07-11 20:15:21", "stars": 54}, "https://github.com/Excidos/ComfyUI-Lumina-Next-SFT-DiffusersWrapper": {"author_account_age_days": 374, "last_update": "2024-07-30 10:27:07", "stars": 17}, "https://github.com/ExponentialML/ComfyUI_ModelScopeT2V": {"author_account_age_days": 1991, "last_update": "2024-05-23 00:12:17", "stars": 27}, "https://github.com/ExponentialML/ComfyUI_Native_DynamiCrafter": {"author_account_age_days": 1991, "last_update": "2024-06-08 02:33:02", "stars": 112}, "https://github.com/ExponentialML/ComfyUI_VisualStylePrompting": {"author_account_age_days": 1991, "last_update": "2024-05-23 00:12:41", "stars": 301}, "https://github.com/ExterminanzHS/Gecco-Discord-Autosend": {"author_account_age_days": 3575, "last_update": "2024-09-05 12:33:30", "stars": 1}, "https://github.com/Extraltodeus/ComfyUI-AutomaticCFG": {"author_account_age_days": 3517, "last_update": "2024-09-10 17:44:50", "stars": 413}, "https://github.com/Extraltodeus/DistanceSampler": {"author_account_age_days": 3517, "last_update": "2025-06-19 22:54:08", "stars": 35}, "https://github.com/Extraltodeus/LoadLoraWithTags": {"author_account_age_days": 3517, "last_update": "2025-02-25 18:12:40", "stars": 76}, "https://github.com/Extraltodeus/Negative-attention-for-ComfyUI-": {"author_account_age_days": 3517, "last_update": "2025-03-20 15:10:24", "stars": 9}, "https://github.com/Extraltodeus/Skimmed_CFG": {"author_account_age_days": 3517, "last_update": "2024-10-25 20:59:10", "stars": 194}, "https://github.com/Extraltodeus/Stable-Diffusion-temperature-settings": {"author_account_age_days": 3517, "last_update": "2024-07-10 00:27:51", "stars": 43}, "https://github.com/Extraltodeus/Uncond-Zero-for-ComfyUI": {"author_account_age_days": 3517, "last_update": "2024-07-10 00:27:36", "stars": 48}, "https://github.com/Extraltodeus/Vector_Sculptor_ComfyUI": {"author_account_age_days": 3517, "last_update": "2024-08-28 05:29:07", "stars": 121}, "https://github.com/Extraltodeus/noise_latent_perlinpinpin": {"author_account_age_days": 3517, "last_update": "2024-08-13 14:19:11", "stars": 33}, "https://github.com/Extraltodeus/pre_cfg_comfy_nodes_for_ComfyUI": {"author_account_age_days": 3517, "last_update": "2025-05-24 07:36:22", "stars": 47}, "https://github.com/Extraltodeus/sigmas_tools_and_the_golden_scheduler": {"author_account_age_days": 3517, "last_update": "2024-12-13 00:18:40", "stars": 82}, "https://github.com/FaberVS/MultiModel": {"author_account_age_days": 2138, "last_update": "2025-05-06 14:27:08", "stars": 1}, "https://github.com/Fannovel16/ComfyUI-Frame-Interpolation": {"author_account_age_days": 3499, "last_update": "2025-04-30 11:32:27", "stars": 734}, "https://github.com/Fannovel16/ComfyUI-MagickWand": {"author_account_age_days": 3499, "last_update": "2025-03-31 10:26:14", "stars": 113}, "https://github.com/Fannovel16/ComfyUI-MotionDiff": {"author_account_age_days": 3499, "last_update": "2024-08-01 01:01:53", "stars": 201}, "https://github.com/Fannovel16/ComfyUI-Video-Matting": {"author_account_age_days": 3499, "last_update": "2024-08-14 01:28:50", "stars": 208}, "https://github.com/Fannovel16/comfyui_controlnet_aux": {"author_account_age_days": 3499, "last_update": "2025-06-20 08:57:29", "stars": 3127}, "https://github.com/Fantaxico/ComfyUI-GCP-Storage": {"author_account_age_days": 903, "last_update": "2024-06-14 09:05:52", "stars": 3}, "https://github.com/Feidorian/feidorian-ComfyNodes": {"author_account_age_days": 3122, "last_update": "2024-06-20 11:31:37", "stars": 5}, "https://github.com/FewBox/fewbox-outfit-comfyui": {"author_account_age_days": 2982, "last_update": "2025-04-27 01:02:28", "stars": 0}, "https://github.com/Fictiverse/ComfyUI_Fictiverse": {"author_account_age_days": 1039, "last_update": "2024-12-02 16:48:03", "stars": 14}, "https://github.com/Fihade/IC-Light-ComfyUI-Node": {"author_account_age_days": 3115, "last_update": "2024-07-02 03:47:17", "stars": 8}, "https://github.com/FinetunersAI/ComfyUI_Finetuners_Suite": {"author_account_age_days": 388, "last_update": "2025-01-30 08:30:13", "stars": 2}, "https://github.com/FizzleDorf/ComfyUI-AIT": {"author_account_age_days": 2347, "last_update": "2024-06-22 03:13:05", "stars": 52}, "https://github.com/FizzleDorf/ComfyUI_FizzNodes": {"author_account_age_days": 2347, "last_update": "2024-10-29 01:51:46", "stars": 444}, "https://github.com/Flow-two/ComfyUI-WanStartEndFramesNative": {"author_account_age_days": 1875, "last_update": "2025-03-28 04:58:45", "stars": 77}, "https://github.com/FlyingFireCo/tiled_ksampler": {"author_account_age_days": 1000, "last_update": "2024-05-22 23:15:17", "stars": 86}, "https://github.com/ForeignGods/ComfyUI-Mana-Nodes": {"author_account_age_days": 1611, "last_update": "2024-05-29 18:29:05", "stars": 234}, "https://github.com/Franck-Demongin/NX_HuggingFace_Flux": {"author_account_age_days": 2138, "last_update": "2024-08-14 02:17:21", "stars": 4}, "https://github.com/Franck-Demongin/NX_PromptStyler": {"author_account_age_days": 2138, "last_update": "2024-05-22 23:25:21", "stars": 9}, "https://github.com/Franck-Demongin/NX_Translator": {"author_account_age_days": 2138, "last_update": "2024-08-14 02:17:01", "stars": 1}, "https://github.com/FredBill1/comfyui-fb-utils": {"author_account_age_days": 2684, "last_update": "2025-03-14 08:09:14", "stars": 1}, "https://github.com/FunnyFinger/ComfyUi-RadarWeightNode": {"author_account_age_days": 943, "last_update": "2025-04-22 09:12:55", "stars": 1}, "https://github.com/FunnyFinger/Dynamic_Sliders_stack": {"author_account_age_days": 943, "last_update": "2025-04-22 10:00:31", "stars": 2}, "https://github.com/FuouM/ComfyUI-EbSynth": {"author_account_age_days": 2049, "last_update": "2025-03-30 06:30:52", "stars": 91}, "https://github.com/FuouM/ComfyUI-FirstOrderMM": {"author_account_age_days": 2049, "last_update": "2025-03-27 12:22:31", "stars": 5}, "https://github.com/FuouM/ComfyUI-MatAnyone": {"author_account_age_days": 2049, "last_update": "2025-03-24 03:43:48", "stars": 9}, "https://github.com/FuouM/ComfyUI-StyleTransferPlus": {"author_account_age_days": 2049, "last_update": "2025-03-27 12:15:58", "stars": 11}, "https://github.com/FuouM/FM_nodes": {"author_account_age_days": 2049, "last_update": "2025-03-27 12:16:55", "stars": 5}, "https://github.com/Fuwuffyi/ComfyUI-VisualArea-Nodes": {"author_account_age_days": 1530, "last_update": "2024-11-05 17:00:49", "stars": 71}, "https://github.com/G-370/ComfyUI-SD3-Powerlab": {"author_account_age_days": 1887, "last_update": "2024-06-22 19:17:18", "stars": 20}, "https://github.com/GACLove/ComfyUI-Lightx2vWrapper": {"author_account_age_days": 3984, "last_update": "2025-06-10 09:30:17", "stars": 5}, "https://github.com/GHOSTLXH/ComfyUI-Counternodes": {"author_account_age_days": 2536, "last_update": "2025-02-20 12:58:43", "stars": 10}, "https://github.com/GTSuya-Studio/ComfyUI-Gtsuya-Nodes": {"author_account_age_days": 2924, "last_update": "2024-05-22 21:31:52", "stars": 12}, "https://github.com/GadzoinksOfficial/comfyui_gprompts": {"author_account_age_days": 523, "last_update": "2025-05-16 05:25:09", "stars": 0}, "https://github.com/GadzoinksOfficial/gadzoinks_ComfyUI": {"author_account_age_days": 523, "last_update": "2025-05-12 09:51:17", "stars": 0}, "https://github.com/GamingDaveUk/daves_nodes": {"author_account_age_days": 799, "last_update": "2025-02-22 06:22:19", "stars": 0}, "https://github.com/Gary-yeh/ComfyUI-WebPrompter": {"author_account_age_days": 854, "last_update": "2025-06-26 08:24:16", "stars": 0}, "https://github.com/Gary-yeh/comfyui-super-captioner": {"author_account_age_days": 854, "last_update": "2025-06-26 03:47:55", "stars": 0}, "https://github.com/GavChap/ComfyUI-SD3LatentSelectRes": {"author_account_age_days": 4935, "last_update": "2025-03-07 14:22:14", "stars": 13}, "https://github.com/GeekyGhost/ComfyUI-Geeky-Kokoro-TTS": {"author_account_age_days": 1037, "last_update": "2025-03-21 11:44:13", "stars": 31}, "https://github.com/GeekyGhost/ComfyUI-GeekyRemB": {"author_account_age_days": 1037, "last_update": "2025-04-09 05:27:46", "stars": 45}, "https://github.com/GentlemanHu/ComfyUI-SunoAI": {"author_account_age_days": 2751, "last_update": "2024-12-17 11:46:33", "stars": 19}, "https://github.com/GiusTex/ComfyUI-DiffusersImageOutpaint": {"author_account_age_days": 1033, "last_update": "2025-05-20 10:59:38", "stars": 86}, "https://github.com/Goktug/comfyui-saveimage-plus": {"author_account_age_days": 5299, "last_update": "2024-11-13 06:03:10", "stars": 12}, "https://github.com/Goshe-nite/comfyui-gps-supplements": {"author_account_age_days": 1028, "last_update": "2025-05-14 20:52:22", "stars": 2}, "https://github.com/Gourieff/ComfyUI-ReActor": {"author_account_age_days": 1487, "last_update": "2025-05-26 16:30:58", "stars": 624}, "https://github.com/GraftingRayman/ComfyUI-PuLID-Flux-GR": {"author_account_age_days": 530, "last_update": "2025-02-24 07:15:35", "stars": 54}, "https://github.com/GraftingRayman/ComfyUI_GraftingRayman": {"author_account_age_days": 530, "last_update": "2025-04-22 06:50:24", "stars": 61}, "https://github.com/GraftingRayman/ComfyUI_QueueTube": {"author_account_age_days": 530, "last_update": "2025-01-08 20:59:13", "stars": 0}, "https://github.com/GrailGreg/images_base64": {"author_account_age_days": 113, "last_update": "2025-05-13 07:12:00", "stars": 1}, "https://github.com/GreenLandisaLie/AuraSR-ComfyUI": {"author_account_age_days": 1565, "last_update": "2024-09-04 10:58:03", "stars": 186}, "https://github.com/GrenKain/PixelArt-Processing-Nodes-for-ComfyUI": {"author_account_age_days": 2777, "last_update": "2024-09-06 11:37:05", "stars": 7}, "https://github.com/GroxicTinch/EasyUI-ComfyUI": {"author_account_age_days": 3312, "last_update": "2025-05-16 07:54:32", "stars": 5}, "https://github.com/GrvBdgr/comfyui-negativewildcardsprocessor": {"author_account_age_days": 240, "last_update": "2024-11-15 19:46:39", "stars": 1}, "https://github.com/Gue-e/ComfyUI-PanoCard": {"author_account_age_days": 2451, "last_update": "2025-06-23 08:57:03", "stars": 11}, "https://github.com/Guillaume-Fgt/ComfyUI_StableCascadeLatentRatio": {"author_account_age_days": 1847, "last_update": "2024-06-14 08:59:42", "stars": 3}, "https://github.com/HAL41/ComfyUI-aichemy-nodes": {"author_account_age_days": 3213, "last_update": "2024-05-22 23:10:19", "stars": 4}, "https://github.com/HECer/ComfyUI-FilePathCreator": {"author_account_age_days": 3348, "last_update": "2025-04-17 16:32:12", "stars": 7}, "https://github.com/HJH-AILab/ComfyUI_CosyVoice2": {"author_account_age_days": 136, "last_update": "2025-05-21 08:36:14", "stars": 3}, "https://github.com/HJH-AILab/ComfyUI_StableAnimator": {"author_account_age_days": 136, "last_update": "2025-04-24 02:45:32", "stars": 16}, "https://github.com/HM-RunningHub/ComfyUI_RH_APICall": {"author_account_age_days": 196, "last_update": "2025-05-04 16:35:02", "stars": 64}, "https://github.com/HM-RunningHub/ComfyUI_RH_FramePack": {"author_account_age_days": 196, "last_update": "2025-05-05 18:32:28", "stars": 179}, "https://github.com/HM-RunningHub/ComfyUI_RH_OminiControl": {"author_account_age_days": 196, "last_update": "2024-12-20 08:41:09", "stars": 136}, "https://github.com/HM-RunningHub/ComfyUI_RH_Step1XEdit": {"author_account_age_days": 196, "last_update": "2025-04-30 17:12:58", "stars": 24}, "https://github.com/HM-RunningHub/ComfyUI_RH_UNO": {"author_account_age_days": 196, "last_update": "2025-04-15 17:12:25", "stars": 51}, "https://github.com/Haiper-ai/ComfyUI-HaiperAI-API": {"author_account_age_days": 1367, "last_update": "2024-12-06 18:08:50", "stars": 13}, "https://github.com/HannibalP/comfyui-HannibalPack": {"author_account_age_days": 2972, "last_update": "2025-03-11 23:36:33", "stars": 1}, "https://github.com/Haoming02/comfyui-clear-screen": {"author_account_age_days": 1698, "last_update": "2025-03-14 06:47:03", "stars": 1}, "https://github.com/Haoming02/comfyui-diffusion-cg": {"author_account_age_days": 1698, "last_update": "2024-10-12 13:39:00", "stars": 99}, "https://github.com/Haoming02/comfyui-floodgate": {"author_account_age_days": 1698, "last_update": "2025-03-14 06:46:50", "stars": 31}, "https://github.com/Haoming02/comfyui-menu-anchor": {"author_account_age_days": 1698, "last_update": "2024-10-19 11:42:51", "stars": 3}, "https://github.com/Haoming02/comfyui-node-beautify": {"author_account_age_days": 1698, "last_update": "2025-03-14 06:46:56", "stars": 8}, "https://github.com/Haoming02/comfyui-old-photo-restoration": {"author_account_age_days": 1698, "last_update": "2025-05-14 05:36:27", "stars": 47}, "https://github.com/Haoming02/comfyui-prompt-format": {"author_account_age_days": 1698, "last_update": "2024-09-20 04:29:03", "stars": 34}, "https://github.com/Haoming02/comfyui-resharpen": {"author_account_age_days": 1698, "last_update": "2024-08-20 05:21:20", "stars": 48}, "https://github.com/Haoming02/comfyui-tab-handler": {"author_account_age_days": 1698, "last_update": "2024-09-09 09:20:58", "stars": 4}, "https://github.com/HavocsCall/comfyui_HavocsCall_Custom_Nodes": {"author_account_age_days": 2282, "last_update": "2025-06-07 18:56:34", "stars": 3}, "https://github.com/HaydenReeve/ComfyUI-Better-Strings": {"author_account_age_days": 2609, "last_update": "2025-03-27 12:41:28", "stars": 2}, "https://github.com/HeadshotPro/ComfyUI-HeadshotPro": {"author_account_age_days": 721, "last_update": "2024-08-14 04:00:34", "stars": 1}, "https://github.com/HebelHuber/comfyui-enhanced-save-node": {"author_account_age_days": 2691, "last_update": "2024-06-14 08:59:28", "stars": 2}, "https://github.com/HellerCommaA/ComfyUI-VideoResolutions": {"author_account_age_days": 4695, "last_update": "2025-03-28 14:51:23", "stars": 1}, "https://github.com/Hellfiredragon/comfyui-image-manipulation": {"author_account_age_days": 2102, "last_update": "2025-02-17 23:25:53", "stars": 0}, "https://github.com/HelloVision/ComfyUI_HelloMeme": {"author_account_age_days": 280, "last_update": "2025-06-26 15:56:29", "stars": 365}, "https://github.com/Hellrunner2k/ComfyUI-HellrunnersMagicalNodes": {"author_account_age_days": 3452, "last_update": "2025-05-21 02:17:36", "stars": 2}, "https://github.com/Hiero207/ComfyUI-Hiero-Nodes": {"author_account_age_days": 2066, "last_update": "2024-08-14 01:25:26", "stars": 6}, "https://github.com/HighDoping/ComfyUI_ASSSSA": {"author_account_age_days": 2537, "last_update": "2025-06-21 02:05:12", "stars": 0}, "https://github.com/Holasyb918/Ghost2_Comfyui": {"author_account_age_days": 1003, "last_update": "2025-03-14 02:41:21", "stars": 3}, "https://github.com/Hopping-Mad-Games/ComfyUI_LiteLLM": {"author_account_age_days": 556, "last_update": "2025-06-25 23:22:58", "stars": 5}, "https://github.com/HowToSD/ComfyUI-Data-Analysis": {"author_account_age_days": 542, "last_update": "2025-06-11 04:28:54", "stars": 16}, "https://github.com/HowToSD/ComfyUI-Pt-Wrapper": {"author_account_age_days": 542, "last_update": "2025-06-11 04:48:46", "stars": 6}, "https://github.com/Hullabalo/ComfyUI-Loop": {"author_account_age_days": 978, "last_update": "2025-05-01 15:26:44", "stars": 8}, "https://github.com/IDGallagher/ComfyUI-IG-Motion-I2V": {"author_account_age_days": 5848, "last_update": "2024-09-30 10:38:22", "stars": 38}, "https://github.com/IDGallagher/ComfyUI-IG-Nodes": {"author_account_age_days": 5848, "last_update": "2025-05-13 08:37:57", "stars": 2}, "https://github.com/IDGallagher/MotionVideoSearch": {"author_account_age_days": 5848, "last_update": "2025-01-13 09:37:08", "stars": 12}, "https://github.com/IIs-fanta/ComfyUI-FANTA-GameBox": {"author_account_age_days": 691, "last_update": "2025-06-04 09:43:26", "stars": 2}, "https://github.com/INuBq8/ComfyUI-NotificationBridge": {"author_account_age_days": 248, "last_update": "2025-06-09 04:11:29", "stars": 0}, "https://github.com/ITurchenko/ComfyUI-SizeFromArray": {"author_account_age_days": 4076, "last_update": "2024-08-01 08:45:43", "stars": 0}, "https://github.com/IamCreateAI/Ruyi-Models": {"author_account_age_days": 199, "last_update": "2025-01-20 12:21:40", "stars": 521}, "https://github.com/IcelandicCenterArtificialIntelligence/ComfyUI-SamplerSchedulerMetricsTester": {"author_account_age_days": 395, "last_update": "2025-05-19 15:04:38", "stars": 1}, "https://github.com/Iemand005/ComfyUI-Touch-Gestures": {"author_account_age_days": 1865, "last_update": "2025-02-03 00:25:14", "stars": 3}, "https://github.com/Iemand005/ComfyUI-Touchpad-Gestures": {"author_account_age_days": 1865, "last_update": "2025-02-03 00:21:47", "stars": 2}, "https://github.com/IgalOgonov/ComfyUI_Simple_String_Repository": {"author_account_age_days": 2597, "last_update": "2024-12-28 20:21:22", "stars": 3}, "https://github.com/ImagineerNL/ComfyUI-IMGNR-Utils": {"author_account_age_days": 1920, "last_update": "2025-05-05 21:36:48", "stars": 0}, "https://github.com/ImagineerNL/ComfyUI-ToSVG-Potracer": {"author_account_age_days": 1920, "last_update": "2025-05-08 21:56:04", "stars": 10}, "https://github.com/Immac/ComfyUI-CoreVideoMocks": {"author_account_age_days": 4546, "last_update": "2025-03-17 20:21:25", "stars": 1}, "https://github.com/ImmortalPie/ComfyUI-PonySwitch": {"author_account_age_days": 4190, "last_update": "2025-03-27 12:49:04", "stars": 10}, "https://github.com/InceptionsAI/ComfyUI-RunComfy-Helper": {"author_account_age_days": 897, "last_update": "2025-05-06 04:03:58", "stars": 2}, "https://github.com/InstantStudioAI/ComfyUI-InstantStudio": {"author_account_age_days": 197, "last_update": "2025-03-25 06:19:54", "stars": 4}, "https://github.com/Intersection98/ComfyUI_MX_post_processing-nodes": {"author_account_age_days": 2997, "last_update": "2024-05-23 01:12:46", "stars": 13}, "https://github.com/Inzaniak/comfyui-ranbooru": {"author_account_age_days": 4277, "last_update": "2024-05-22 23:12:23", "stars": 19}, "https://github.com/Irsalistic/comfyui-dam-object-extractor": {"author_account_age_days": 687, "last_update": "2025-05-13 11:10:44", "stars": 5}, "https://github.com/IsItDanOrAi/ComfyUI-Stereopsis": {"author_account_age_days": 479, "last_update": "2024-09-21 21:39:11", "stars": 9}, "https://github.com/Isi-dev/ComfyUI-Animation_Nodes_and_Workflows": {"author_account_age_days": 1459, "last_update": "2025-06-11 15:26:03", "stars": 28}, "https://github.com/Isi-dev/ComfyUI-Img2DrawingAssistants": {"author_account_age_days": 1459, "last_update": "2024-12-15 10:03:55", "stars": 18}, "https://github.com/Isi-dev/ComfyUI-Img2PaintingAssistant": {"author_account_age_days": 1459, "last_update": "2024-12-15 11:00:51", "stars": 11}, "https://github.com/Isi-dev/ComfyUI-UniAnimate-W": {"author_account_age_days": 1459, "last_update": "2025-03-11 10:32:39", "stars": 175}, "https://github.com/Isi-dev/ComfyUI_Animation_Nodes_and_Workflows": {"author_account_age_days": 1459, "last_update": "2025-06-11 15:26:03", "stars": 28}, "https://github.com/Isulion/ComfyUI_Isulion": {"author_account_age_days": 725, "last_update": "2025-05-03 12:21:05", "stars": 38}, "https://github.com/IuvenisSapiens/ComfyUI_MiniCPM-V-2_6-int4": {"author_account_age_days": 778, "last_update": "2025-04-02 16:32:54", "stars": 184}, "https://github.com/IuvenisSapiens/ComfyUI_Qwen2-Audio-7B-Instruct-Int4": {"author_account_age_days": 778, "last_update": "2025-04-02 16:35:52", "stars": 10}, "https://github.com/IuvenisSapiens/ComfyUI_Qwen2-VL-Instruct": {"author_account_age_days": 778, "last_update": "2025-04-02 16:22:22", "stars": 98}, "https://github.com/JEONG-JIWOO/ComfyUI_Eugene_Nodes": {"author_account_age_days": 2934, "last_update": "2025-01-27 19:09:46", "stars": 2}, "https://github.com/JPS-GER/ComfyUI_JPS-Nodes": {"author_account_age_days": 683, "last_update": "2024-05-22 20:39:14", "stars": 75}, "https://github.com/JPrevots/ComfyUI-PhyCV": {"author_account_age_days": 926, "last_update": "2025-02-21 11:36:11", "stars": 1}, "https://github.com/JTriggerFish/ComfyLatentTools": {"author_account_age_days": 4366, "last_update": "2025-05-06 21:07:17", "stars": 2}, "https://github.com/JackEllie/ComfyUI_AI_Assistant": {"author_account_age_days": 938, "last_update": "2024-09-05 03:42:14", "stars": 24}, "https://github.com/Jacky-MYQ/comfyui-DataCleaning": {"author_account_age_days": 710, "last_update": "2025-05-10 12:26:38", "stars": 5}, "https://github.com/Jacky-MYQ/comfyui-rgb2cmyk": {"author_account_age_days": 710, "last_update": "2025-04-28 02:05:19", "stars": 2}, "https://github.com/Jaminanim/ComfyUI-Random-Int-Divisor-Node": {"author_account_age_days": 1929, "last_update": "2025-01-07 06:50:58", "stars": 0}, "https://github.com/Jannchie/ComfyUI-J": {"author_account_age_days": 2921, "last_update": "2025-04-07 09:03:24", "stars": 99}, "https://github.com/Jannled/owl-vit-comfyui": {"author_account_age_days": 4045, "last_update": "2025-05-20 00:41:53", "stars": 0}, "https://github.com/JaredTherriault/ComfyUI-JNodes": {"author_account_age_days": 3939, "last_update": "2025-06-14 20:09:47", "stars": 69}, "https://github.com/Jash-Vora/ComfyUI-GarmentDiT": {"author_account_age_days": 778, "last_update": "2025-01-04 08:22:14", "stars": 3}, "https://github.com/JcandZero/ComfyUI_GLM4Node": {"author_account_age_days": 1057, "last_update": "2024-05-22 23:12:46", "stars": 26}, "https://github.com/Jcd1230/rembg-comfyui-node": {"author_account_age_days": 5254, "last_update": "2024-05-22 17:58:34", "stars": 168}, "https://github.com/JerryOrbachJr/ComfyUI-RandomSize": {"author_account_age_days": 523, "last_update": "2024-08-25 18:35:55", "stars": 5}, "https://github.com/JettHu/ComfyUI-TCD": {"author_account_age_days": 2725, "last_update": "2024-07-31 13:50:21", "stars": 131}, "https://github.com/JettHu/ComfyUI_TGate": {"author_account_age_days": 2725, "last_update": "2024-09-24 02:15:59", "stars": 93}, "https://github.com/JiSenHua/ComfyUI-TD": {"author_account_age_days": 1111, "last_update": "2025-04-20 16:24:26", "stars": 62}, "https://github.com/Jint8888/Comfyui_JTnodes": {"author_account_age_days": 418, "last_update": "2025-04-22 16:23:53", "stars": 1}, "https://github.com/JoeNavark/comfyui_custom_sigma_editor": {"author_account_age_days": 1216, "last_update": "2025-05-11 18:00:22", "stars": 7}, "https://github.com/JohanK66/ComfyUI-WebhookImage": {"author_account_age_days": 118, "last_update": "2025-03-10 19:38:53", "stars": 1}, "https://github.com/JohnDoeSmithee/ComfyUI-SoX-Mixdown": {"author_account_age_days": 151, "last_update": "2025-01-26 22:42:52", "stars": 1}, "https://github.com/Jokimbe/ComfyUI-DrawThings-gRPC": {"author_account_age_days": 4748, "last_update": "2025-06-27 01:24:07", "stars": 9}, "https://github.com/Jonseed/ComfyUI-Detail-Daemon": {"author_account_age_days": 2547, "last_update": "2025-03-14 16:47:41", "stars": 748}, "https://github.com/Jordach/comfy-plasma": {"author_account_age_days": 4880, "last_update": "2024-05-22 18:08:28", "stars": 75}, "https://github.com/JosefKuchar/ComfyUI-AdvancedTiling": {"author_account_age_days": 3722, "last_update": "2024-08-02 15:16:12", "stars": 12}, "https://github.com/JosephThomasParker/ComfyUI-DrawThingsWrapper": {"author_account_age_days": 3537, "last_update": "2025-02-04 21:14:38", "stars": 30}, "https://github.com/Julian-adv/WildDivide": {"author_account_age_days": 706, "last_update": "2025-05-27 13:24:07", "stars": 17}, "https://github.com/JustLateNightAI/KeywordImageBlocker": {"author_account_age_days": 248, "last_update": "2025-05-07 17:25:44", "stars": 0}, "https://github.com/JustinMatters/comfyUI-JMNodes": {"author_account_age_days": 3138, "last_update": "2025-01-04 14:57:58", "stars": 0}, "https://github.com/KAVVATARE/ComfyUI-Light-N-Color": {"author_account_age_days": 4498, "last_update": "2025-03-02 16:56:41", "stars": 1}, "https://github.com/KAVVATARE/ComfyUI_RightEyeDisparity": {"author_account_age_days": 4498, "last_update": "2025-05-23 19:32:04", "stars": 2}, "https://github.com/KERRY-YUAN/ComfyUI_Float_Animator": {"author_account_age_days": 1621, "last_update": "2025-06-16 06:49:23", "stars": 3}, "https://github.com/KERRY-YUAN/ComfyUI_Simple_Executor": {"author_account_age_days": 1621, "last_update": "2025-04-09 03:25:32", "stars": 2}, "https://github.com/KERRY-YUAN/ComfyUI_Spark_TTS": {"author_account_age_days": 1621, "last_update": "2025-06-10 06:16:34", "stars": 2}, "https://github.com/KLL535/ComfyUI_PNGInfo_Sidebar": {"author_account_age_days": 217, "last_update": "2025-02-16 13:11:48", "stars": 17}, "https://github.com/KLL535/ComfyUI_SimpleButcher": {"author_account_age_days": 217, "last_update": "2025-03-09 21:53:41", "stars": 7}, "https://github.com/Kangkang625/ComfyUI-paint-by-example": {"author_account_age_days": 1281, "last_update": "2024-05-22 22:20:27", "stars": 16}, "https://github.com/Kartel-ai/ComfyUI-8iPlayer": {"author_account_age_days": 57, "last_update": "2025-06-20 07:54:00", "stars": 48}, "https://github.com/Kayarte/AudioDriven-Latent-Space-Tools-for-ComfyUI": {"author_account_age_days": 421, "last_update": "2025-06-15 22:39:14", "stars": 3}, "https://github.com/Kesin11/ComfyUI-list-filter": {"author_account_age_days": 4914, "last_update": "2025-03-28 04:00:03", "stars": 0}, "https://github.com/KewkLW/ComfyUI-kewky_tools": {"author_account_age_days": 2054, "last_update": "2025-05-11 21:55:10", "stars": 8}, "https://github.com/Kidev/ComfyUI-Fisheye-effects": {"author_account_age_days": 4969, "last_update": "2025-04-03 19:00:30", "stars": 16}, "https://github.com/Kinglord/ComfyUI_LoRA_Sidebar": {"author_account_age_days": 5258, "last_update": "2025-04-27 08:48:53", "stars": 82}, "https://github.com/Kinglord/ComfyUI_Prompt_Gallery": {"author_account_age_days": 5258, "last_update": "2024-09-24 21:58:55", "stars": 54}, "https://github.com/Kinglord/ComfyUI_Slider_Sidebar": {"author_account_age_days": 5258, "last_update": "2024-09-26 02:40:30", "stars": 40}, "https://github.com/KohakuBlueleaf/z-tipo-extension": {"author_account_age_days": 1995, "last_update": "2025-06-10 16:57:54", "stars": 481}, "https://github.com/Koishi-Star/Euler-Smea-Dyn-Sampler": {"author_account_age_days": 1853, "last_update": "2024-09-01 03:57:22", "stars": 209}, "https://github.com/Koishi-Star/Pyramid_Noise_For_Inference": {"author_account_age_days": 1853, "last_update": "2024-09-27 17:58:43", "stars": 6}, "https://github.com/KoreTeknology/ComfyUI-Nai-Production-Nodes-Pack": {"author_account_age_days": 3557, "last_update": "2024-11-24 15:55:30", "stars": 10}, "https://github.com/KoreTeknology/ComfyUI-Universal-Styler": {"author_account_age_days": 3557, "last_update": "2025-03-01 05:37:40", "stars": 62}, "https://github.com/Kosinkadink/ComfyUI-Advanced-ControlNet": {"author_account_age_days": 4084, "last_update": "2025-03-05 03:01:28", "stars": 816}, "https://github.com/Kosinkadink/ComfyUI-AnimateDiff-Evolved": {"author_account_age_days": 4084, "last_update": "2025-04-09 21:22:11", "stars": 3185}, "https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite": {"author_account_age_days": 4084, "last_update": "2025-04-26 20:27:20", "stars": 1066}, "https://github.com/Koushakur/ComfyUI-DenoiseChooser": {"author_account_age_days": 1488, "last_update": "2025-03-14 09:52:02", "stars": 4}, "https://github.com/KunmyonChoi/ComfyUI_S3_direct": {"author_account_age_days": 5935, "last_update": "2025-01-07 01:22:23", "stars": 0}, "https://github.com/Kurdknight/Kurdknight_comfycheck": {"author_account_age_days": 879, "last_update": "2025-01-15 16:47:23", "stars": 4}, "https://github.com/KwaiVGI/ComfyUI-KLingAI-API": {"author_account_age_days": 426, "last_update": "2025-05-06 06:25:51", "stars": 130}, "https://github.com/Ky11le/draw_tools": {"author_account_age_days": 846, "last_update": "2025-05-14 05:35:47", "stars": 0}, "https://github.com/Ky11le/ygo_tools": {"author_account_age_days": 846, "last_update": "2025-05-14 05:35:47", "stars": 0}, "https://github.com/KytraScript/ComfyUI_KytraWebhookHTTP": {"author_account_age_days": 2147, "last_update": "2024-05-23 00:21:43", "stars": 5}, "https://github.com/KytraScript/ComfyUI_MatAnyone_Kytra": {"author_account_age_days": 2147, "last_update": "2025-03-16 18:58:58", "stars": 124}, "https://github.com/LAOGOU-666/ComfyUI-LG_HotReload": {"author_account_age_days": 462, "last_update": "2025-06-21 16:06:56", "stars": 199}, "https://github.com/LAOGOU-666/ComfyUI_LG_FFT": {"author_account_age_days": 462, "last_update": "2024-10-10 04:45:57", "stars": 8}, "https://github.com/LAOGOU-666/Comfyui-LG_GroupExecutor": {"author_account_age_days": 462, "last_update": "2025-05-31 17:36:04", "stars": 143}, "https://github.com/LAOGOU-666/Comfyui-LG_Relight": {"author_account_age_days": 462, "last_update": "2025-06-16 13:28:22", "stars": 185}, "https://github.com/LAOGOU-666/Comfyui-Memory_Cleanup": {"author_account_age_days": 462, "last_update": "2025-04-09 16:45:10", "stars": 136}, "https://github.com/LAOGOU-666/Comfyui_LG_Tools": {"author_account_age_days": 462, "last_update": "2025-06-12 09:20:36", "stars": 142}, "https://github.com/LEv145/images-grid-comfy-plugin": {"author_account_age_days": 2575, "last_update": "2024-05-30 17:54:32", "stars": 183}, "https://github.com/LKbaba/ComfyUI-TuZi-Flux-Kontext": {"author_account_age_days": 2855, "last_update": "2025-06-19 03:37:53", "stars": 14}, "https://github.com/LaVie024/comfyui-lopi999-nodes": {"author_account_age_days": 1925, "last_update": "2025-06-25 23:41:44", "stars": 3}, "https://github.com/LamEmil/ComfyUI_ASCIIArtNode": {"author_account_age_days": 268, "last_update": "2025-05-18 09:22:38", "stars": 1}, "https://github.com/LargeModGames/comfyui-smart-lora-downloader": {"author_account_age_days": 1499, "last_update": "2025-06-12 08:40:31", "stars": 1}, "https://github.com/LarryJane491/Image-Captioning-in-ComfyUI": {"author_account_age_days": 531, "last_update": "2024-06-06 20:45:43", "stars": 65}, "https://github.com/LarryJane491/Lora-Training-in-Comfy": {"author_account_age_days": 531, "last_update": "2024-08-05 11:32:30", "stars": 477}, "https://github.com/LatentRat/comfy_remote_run": {"author_account_age_days": 1112, "last_update": "2024-09-08 04:06:09", "stars": 6}, "https://github.com/LatentSpaceDirective/ComfyUI-Texturaizer": {"author_account_age_days": 227, "last_update": "2025-01-19 14:21:04", "stars": 15}, "https://github.com/Laurent2916/comfyui-piq": {"author_account_age_days": 3235, "last_update": "2025-03-17 13:50:16", "stars": 0}, "https://github.com/Layer-norm/comfyui-lama-remover": {"author_account_age_days": 699, "last_update": "2024-08-03 04:18:39", "stars": 129}, "https://github.com/Legorobotdude/ComfyUI-VariationLab": {"author_account_age_days": 3737, "last_update": "2025-03-02 04:59:28", "stars": 1}, "https://github.com/Lerc/canvas_tab": {"author_account_age_days": 5735, "last_update": "2024-05-22 20:48:45", "stars": 189}, "https://github.com/LevelPixel/ComfyUI-LevelPixel": {"author_account_age_days": 350, "last_update": "2025-06-22 02:10:16", "stars": 11}, "https://github.com/LevelPixel/ComfyUI-LevelPixel-Advanced": {"author_account_age_days": 350, "last_update": "2025-06-09 13:45:18", "stars": 5}, "https://github.com/Lhyejin/ComfyUI-Fill-Image-for-Outpainting": {"author_account_age_days": 2970, "last_update": "2024-08-26 00:40:09", "stars": 9}, "https://github.com/LiJT/ComfyUI-Gemini-Prompt-Generator-JT": {"author_account_age_days": 3769, "last_update": "2025-04-06 12:33:08", "stars": 4}, "https://github.com/Light-x02/ComfyUI-FluxSettingsNode": {"author_account_age_days": 1145, "last_update": "2025-04-28 21:45:01", "stars": 6}, "https://github.com/Light-x02/ComfyUI-Image-Metadata-Nodes": {"author_account_age_days": 1145, "last_update": "2025-05-01 18:14:59", "stars": 6}, "https://github.com/LightSketch-ai/ComfyUI-LivePortraitNode": {"author_account_age_days": 351, "last_update": "2024-07-17 01:24:53", "stars": 2}, "https://github.com/Lightricks/ComfyUI-LTXVideo": {"author_account_age_days": 4558, "last_update": "2025-06-20 14:01:36", "stars": 2097}, "https://github.com/Limbicnation/ComfyUI-TransparencyBackgroundRemover": {"author_account_age_days": 4196, "last_update": "2025-06-04 00:28:38", "stars": 5}, "https://github.com/Limbicnation/ComfyUIDepthEstimation": {"author_account_age_days": 4196, "last_update": "2025-05-14 22:31:24", "stars": 13}, "https://github.com/Limbicnation/ComfyUI_FaceDetectionNode": {"author_account_age_days": 4196, "last_update": "2025-06-23 01:32:50", "stars": 1}, "https://github.com/Limitex/ComfyUI-Calculation": {"author_account_age_days": 1642, "last_update": "2024-05-22 22:18:40", "stars": 0}, "https://github.com/Limitex/ComfyUI-Diffusers": {"author_account_age_days": 1642, "last_update": "2025-03-10 19:04:32", "stars": 170}, "https://github.com/Ling-APE/ComfyUI-PixelResolutionCalculator": {"author_account_age_days": 754, "last_update": "2025-06-02 02:45:04", "stars": 8}, "https://github.com/LingSss9/comfyui-merge": {"author_account_age_days": 628, "last_update": "2025-06-01 17:27:49", "stars": 7}, "https://github.com/Loewen-Hob/rembg-comfyui-node-better": {"author_account_age_days": 838, "last_update": "2025-04-07 09:09:34", "stars": 64}, "https://github.com/LonicaMewinsky/ComfyUI-MakeFrame": {"author_account_age_days": 1343, "last_update": "2024-05-22 21:29:02", "stars": 29}, "https://github.com/LonicaMewinsky/ComfyUI-RawSaver": {"author_account_age_days": 1343, "last_update": "2024-05-22 21:31:28", "stars": 3}, "https://github.com/LoveEatCandy/COMFYUI-ReplacePartOfImage": {"author_account_age_days": 2809, "last_update": "2025-06-17 05:53:17", "stars": 0}, "https://github.com/LucipherDev/ComfyUI-AniDoc": {"author_account_age_days": 1864, "last_update": "2025-03-28 18:39:10", "stars": 53}, "https://github.com/LucipherDev/ComfyUI-Golden-Noise": {"author_account_age_days": 1864, "last_update": "2025-03-28 18:38:24", "stars": 23}, "https://github.com/LucipherDev/ComfyUI-TangoFlux": {"author_account_age_days": 1864, "last_update": "2025-03-28 18:39:16", "stars": 93}, "https://github.com/Ludobico/ComfyUI-ScenarioPrompt": {"author_account_age_days": 1400, "last_update": "2025-03-12 09:07:07", "stars": 17}, "https://github.com/LyazS/comfyui-anime-seg": {"author_account_age_days": 3224, "last_update": "2024-05-22 23:21:49", "stars": 10}, "https://github.com/LyazS/comfyui-nettools": {"author_account_age_days": 3224, "last_update": "2024-09-23 12:52:44", "stars": 5}, "https://github.com/M1kep/ComfyLiterals": {"author_account_age_days": 4625, "last_update": "2024-05-22 20:31:38", "stars": 53}, "https://github.com/M1kep/ComfyUI-KepOpenAI": {"author_account_age_days": 4625, "last_update": "2024-08-20 16:33:57", "stars": 30}, "https://github.com/M1kep/ComfyUI-OtherVAEs": {"author_account_age_days": 4625, "last_update": "2024-05-22 20:33:41", "stars": 2}, "https://github.com/M1kep/Comfy_KepKitchenSink": {"author_account_age_days": 4625, "last_update": "2024-05-22 20:33:29", "stars": 0}, "https://github.com/M1kep/Comfy_KepListStuff": {"author_account_age_days": 4625, "last_update": "2024-06-22 00:51:28", "stars": 45}, "https://github.com/M1kep/Comfy_KepMatteAnything": {"author_account_age_days": 4625, "last_update": "2024-05-22 20:33:16", "stars": 11}, "https://github.com/M1kep/KepPromptLang": {"author_account_age_days": 4625, "last_update": "2024-05-22 20:32:56", "stars": 6}, "https://github.com/MDMAchine/ComfyUI_MD_Nodes": {"author_account_age_days": 2017, "last_update": "2025-06-18 03:28:39", "stars": 3}, "https://github.com/MNeMoNiCuZ/ComfyUI-mnemic-nodes": {"author_account_age_days": 1972, "last_update": "2025-06-07 15:59:38", "stars": 62}, "https://github.com/Makeezi/ComfyUI-promptLAB": {"author_account_age_days": 2149, "last_update": "2024-05-23 01:24:51", "stars": 0}, "https://github.com/MakkiShizu/ComfyUI-Prompt-Wildcards": {"author_account_age_days": 680, "last_update": "2025-06-14 10:33:54", "stars": 7}, "https://github.com/MakkiShizu/ComfyUI-Qwen2_5-VL": {"author_account_age_days": 680, "last_update": "2025-05-30 16:50:42", "stars": 3}, "https://github.com/MakkiShizu/comfyui_reimgsize": {"author_account_age_days": 680, "last_update": "2025-04-27 15:34:57", "stars": 5}, "https://github.com/Mamaaaamooooo/batchImg-rembg-ComfyUI-nodes": {"author_account_age_days": 749, "last_update": "2024-06-14 10:24:17", "stars": 28}, "https://github.com/ManglerFTW/ComfyI2I": {"author_account_age_days": 1023, "last_update": "2024-06-14 11:01:01", "stars": 174}, "https://github.com/MaraScott/ComfyUI_MaraScott_Nodes": {"author_account_age_days": 5337, "last_update": "2025-04-26 23:44:45", "stars": 151}, "https://github.com/MarcusNyne/m9-prompts-comfyui": {"author_account_age_days": 1790, "last_update": "2024-08-24 16:56:53", "stars": 1}, "https://github.com/MariusKM/ComfyUI-BadmanNodes": {"author_account_age_days": 2598, "last_update": "2024-12-30 15:36:09", "stars": 2}, "https://github.com/MarkoCa1/ComfyUI-Text": {"author_account_age_days": 2001, "last_update": "2024-12-16 09:48:49", "stars": 7}, "https://github.com/MarkoCa1/ComfyUI_Segment_Mask": {"author_account_age_days": 2001, "last_update": "2024-05-23 00:15:51", "stars": 22}, "https://github.com/Marksusu/ComfyUI_MTCLIPEncode": {"author_account_age_days": 1075, "last_update": "2025-05-07 13:56:23", "stars": 7}, "https://github.com/MaruPelkar/comfyui-conditioning-resizer": {"author_account_age_days": 3948, "last_update": "2025-04-21 20:57:33", "stars": 1}, "https://github.com/Mason-McGough/ComfyUI-Mosaica": {"author_account_age_days": 3573, "last_update": "2024-08-26 20:42:35", "stars": 6}, "https://github.com/Mattabyte/ComfyUI-SecureApiCall": {"author_account_age_days": 1975, "last_update": "2025-03-07 23:55:55", "stars": 0}, "https://github.com/Maxed-Out-99/ComfyUI-MaxedOut": {"author_account_age_days": 47, "last_update": "2025-06-20 17:31:54", "stars": 2}, "https://github.com/McKlinton2/comfyui-mcklinton-pack": {"author_account_age_days": 1146, "last_update": "2025-05-31 18:41:13", "stars": 1}, "https://github.com/Mcmillian/ComfyUI-SimpleToolsNodes": {"author_account_age_days": 3263, "last_update": "2024-09-29 14:18:23", "stars": 0}, "https://github.com/MeeeyoAI/ComfyUI_StringOps": {"author_account_age_days": 124, "last_update": "2025-06-04 17:47:55", "stars": 78}, "https://github.com/Meettya/ComfyUI-OneForOne": {"author_account_age_days": 5699, "last_update": "2025-01-07 22:49:30", "stars": 2}, "https://github.com/MetaGLM/ComfyUI-ZhipuAI-Platform": {"author_account_age_days": 646, "last_update": "2024-09-16 16:11:59", "stars": 5}, "https://github.com/MicheleGuidi/ComfyUI-Contextual-SAM2": {"author_account_age_days": 1616, "last_update": "2025-05-01 16:09:43", "stars": 5}, "https://github.com/MiddleKD/ComfyUI-denoise-mask-scheduler": {"author_account_age_days": 898, "last_update": "2024-11-07 12:35:00", "stars": 6}, "https://github.com/MiddleKD/ComfyUI-mem-safe-wrapper": {"author_account_age_days": 898, "last_update": "2024-08-01 06:47:24", "stars": 4}, "https://github.com/MiddleKD/ComfyUI-productfix": {"author_account_age_days": 898, "last_update": "2025-05-12 05:00:24", "stars": 10}, "https://github.com/MieMieeeee/ComfyUI-CaptionThis": {"author_account_age_days": 1924, "last_update": "2025-04-22 05:54:42", "stars": 57}, "https://github.com/MieMieeeee/ComfyUI-MieNodes": {"author_account_age_days": 1924, "last_update": "2025-04-17 07:37:04", "stars": 49}, "https://github.com/MieMieeeee/ComfyUI-MinioConnector": {"author_account_age_days": 1924, "last_update": "2025-03-21 09:09:09", "stars": 3}, "https://github.com/MijnSpam/ComfyUI_SwapAndScale": {"author_account_age_days": 916, "last_update": "2025-05-31 09:27:10", "stars": 0}, "https://github.com/MijnSpam/UploadToPushOver": {"author_account_age_days": 916, "last_update": "2025-05-31 09:32:38", "stars": 1}, "https://github.com/MilitantHitchhiker/MilitantHitchhiker-SwitchbladePack": {"author_account_age_days": 427, "last_update": "2024-10-06 07:46:05", "stars": 3}, "https://github.com/Mintbeer96/ComfyUI-KerasOCR": {"author_account_age_days": 3545, "last_update": "2024-07-24 16:39:41", "stars": 3}, "https://github.com/MinusZoneAI/ComfyUI-CogVideoX-MZ": {"author_account_age_days": 433, "last_update": "2024-10-30 10:52:42", "stars": 105}, "https://github.com/MinusZoneAI/ComfyUI-Flux1Quantize-MZ": {"author_account_age_days": 433, "last_update": "2024-08-14 04:01:10", "stars": 11}, "https://github.com/MinusZoneAI/ComfyUI-FluxExt-MZ": {"author_account_age_days": 433, "last_update": "2024-08-16 18:57:07", "stars": 294}, "https://github.com/MinusZoneAI/ComfyUI-Kolors-MZ": {"author_account_age_days": 433, "last_update": "2025-03-31 02:51:00", "stars": 588}, "https://github.com/MinusZoneAI/ComfyUI-Prompt-MZ": {"author_account_age_days": 433, "last_update": "2025-03-14 06:36:29", "stars": 115}, "https://github.com/MinusZoneAI/ComfyUI-StylizePhoto-MZ": {"author_account_age_days": 433, "last_update": "2024-05-23 01:13:32", "stars": 18}, "https://github.com/MinusZoneAI/ComfyUI-TrainTools-MZ": {"author_account_age_days": 433, "last_update": "2025-02-24 06:08:49", "stars": 58}, "https://github.com/Miosp/ComfyUI-FBCNN": {"author_account_age_days": 2883, "last_update": "2025-02-24 20:53:32", "stars": 26}, "https://github.com/MitoshiroPJ/comfyui_slothful_attention": {"author_account_age_days": 4335, "last_update": "2024-05-22 22:09:15", "stars": 7}, "https://github.com/Miyuutsu/comfyui-save-vpred": {"author_account_age_days": 3292, "last_update": "2024-12-15 22:29:47", "stars": 4}, "https://github.com/MohammadAboulEla/ComfyUI-iTools": {"author_account_age_days": 1394, "last_update": "2025-05-08 14:47:04", "stars": 158}, "https://github.com/MokkaBoss1/ComfyUI_Mokkaboss1": {"author_account_age_days": 747, "last_update": "2025-06-08 11:06:37", "stars": 16}, "https://github.com/MontagenAI/ComfyUI-Montagen": {"author_account_age_days": 197, "last_update": "2025-05-29 10:43:37", "stars": 21}, "https://github.com/MoonGoblinDev/Civicomfy": {"author_account_age_days": 3170, "last_update": "2025-06-10 15:38:45", "stars": 34}, "https://github.com/MoonHugo/ComfyUI-BAGEL-Hugo": {"author_account_age_days": 296, "last_update": "2025-06-04 08:48:11", "stars": 3}, "https://github.com/MoonHugo/ComfyUI-BiRefNet-Hugo": {"author_account_age_days": 296, "last_update": "2025-05-25 15:37:49", "stars": 304}, "https://github.com/MoonHugo/ComfyUI-FFmpeg": {"author_account_age_days": 296, "last_update": "2025-06-04 07:30:29", "stars": 83}, "https://github.com/MoonHugo/ComfyUI-StableAudioOpen": {"author_account_age_days": 296, "last_update": "2024-10-18 04:12:04", "stars": 27}, "https://github.com/Moooonet/ComfyUI-Align": {"author_account_age_days": 354, "last_update": "2025-05-12 09:40:03", "stars": 137}, "https://github.com/MrForExample/ComfyUI-3D-Pack": {"author_account_age_days": 1928, "last_update": "2025-06-19 11:10:43", "stars": 3199}, "https://github.com/MrForExample/ComfyUI-AnimateAnyone-Evolved": {"author_account_age_days": 1928, "last_update": "2024-06-14 12:02:47", "stars": 547}, "https://github.com/MrSamSeen/ComfyUI_SSBeforeAfterNode": {"author_account_age_days": 3958, "last_update": "2025-05-25 01:55:29", "stars": 1}, "https://github.com/MrSamSeen/ComfyUI_SSStereoscope": {"author_account_age_days": 3958, "last_update": "2025-04-27 11:44:51", "stars": 24}, "https://github.com/Munkyfoot/ComfyUI-TextOverlay": {"author_account_age_days": 3412, "last_update": "2025-06-07 04:46:39", "stars": 34}, "https://github.com/MuziekMagie/ComfyUI-Matchering": {"author_account_age_days": 339, "last_update": "2024-07-23 14:39:52", "stars": 48}, "https://github.com/MzMaXaM/ComfyUi-MzMaXaM": {"author_account_age_days": 2106, "last_update": "2025-06-01 16:34:55", "stars": 10}, "https://github.com/N3rd00d/ComfyUI-Paint3D-Nodes": {"author_account_age_days": 444, "last_update": "2024-08-19 12:52:20", "stars": 70}, "https://github.com/NMWave/ComfyUI-Nader-Tagging": {"author_account_age_days": 946, "last_update": "2025-04-09 01:07:33", "stars": 2}, "https://github.com/NVIDIAGameWorks/ComfyUI-RTX-Remix": {"author_account_age_days": 4048, "last_update": "2025-06-04 21:48:12", "stars": 41}, "https://github.com/NakamuraShippo/ComfyUI-NS-ManySliders": {"author_account_age_days": 730, "last_update": "2024-11-03 02:48:52", "stars": 4}, "https://github.com/NakamuraShippo/ComfyUI-NS-PromptList": {"author_account_age_days": 730, "last_update": "2025-06-21 08:50:06", "stars": 8}, "https://github.com/NeoDroleDeGueule/comfyui-image-mixer": {"author_account_age_days": 490, "last_update": "2025-06-11 16:54:15", "stars": 0}, "https://github.com/NeoGriever/ComfyUI-NeoGriever": {"author_account_age_days": 4656, "last_update": "2024-12-12 02:55:40", "stars": 2}, "https://github.com/NeonLightning/neonllama": {"author_account_age_days": 4583, "last_update": "2025-06-20 22:49:39", "stars": 1}, "https://github.com/Nestorchik/NStor-ComfyUI-Translation": {"author_account_age_days": 1692, "last_update": "2024-06-14 10:25:32", "stars": 2}, "https://github.com/NeuralSamurAI/ComfyUI-Dimensional-Latent-Perlin": {"author_account_age_days": 461, "last_update": "2024-08-06 19:59:25", "stars": 34}, "https://github.com/NeuralSamurAI/ComfyUI-FluxPseudoNegativePrompt": {"author_account_age_days": 461, "last_update": "2024-08-14 02:16:43", "stars": 7}, "https://github.com/NeuralSamurAI/ComfyUI-PromptJSON": {"author_account_age_days": 461, "last_update": "2024-08-11 18:10:36", "stars": 2}, "https://github.com/NeuralSamurAI/Comfyui-Superprompt-Unofficial": {"author_account_age_days": 461, "last_update": "2024-05-23 00:22:08", "stars": 68}, "https://github.com/Nevysha/ComfyUI-nevysha-top-menu": {"author_account_age_days": 894, "last_update": "2024-05-23 00:17:31", "stars": 5}, "https://github.com/NguynHungNguyen/Segment-Bedroom-Interior": {"author_account_age_days": 1025, "last_update": "2024-10-17 13:22:19", "stars": 7}, "https://github.com/NicholasMcCarthy/ComfyUI_TravelSuite": {"author_account_age_days": 5517, "last_update": "2024-05-22 20:34:46", "stars": 15}, "https://github.com/Nikosis/ComfyUI-Nikosis-Nodes": {"author_account_age_days": 2644, "last_update": "2025-04-10 00:32:13", "stars": 1}, "https://github.com/Nikosis/ComfyUI-Nikosis-Preprocessors": {"author_account_age_days": 2644, "last_update": "2025-04-08 12:28:17", "stars": 2}, "https://github.com/NimaNzrii/comfyui-photoshop": {"author_account_age_days": 612, "last_update": "2025-05-17 18:02:00", "stars": 1128}, "https://github.com/NimaNzrii/comfyui-popup_preview": {"author_account_age_days": 612, "last_update": "2024-05-22 22:12:04", "stars": 35}, "https://github.com/Niutonian/ComfyUi-NoodleWebcam": {"author_account_age_days": 1409, "last_update": "2024-05-22 21:30:40", "stars": 32}, "https://github.com/Njbx/ComfyUI-LTX13B-Blockswap": {"author_account_age_days": 1571, "last_update": "2025-05-07 18:47:45", "stars": 6}, "https://github.com/Nlar/ComfyUI_CartoonSegmentation": {"author_account_age_days": 4199, "last_update": "2024-05-22 23:15:37", "stars": 16}, "https://github.com/Nojahhh/ComfyUI_GLM4_Wrapper": {"author_account_age_days": 3187, "last_update": "2025-05-08 10:35:48", "stars": 18}, "https://github.com/NotHarroweD/Harronode": {"author_account_age_days": 2351, "last_update": "2024-05-22 22:18:29", "stars": 5}, "https://github.com/Nourepide/ComfyUI-Allor": {"author_account_age_days": 3213, "last_update": "2024-05-22 18:11:17", "stars": 260}, "https://github.com/Nuked88/ComfyUI-N-Nodes": {"author_account_age_days": 4847, "last_update": "2024-08-15 21:07:32", "stars": 222}, "https://github.com/Nuked88/ComfyUI-N-Sidebar": {"author_account_age_days": 4847, "last_update": "2025-06-04 18:29:26", "stars": 557}, "https://github.com/NyaamZ/ComfyUI-ImageGallery-ED": {"author_account_age_days": 2484, "last_update": "2025-05-28 15:22:04", "stars": 6}, "https://github.com/NyaamZ/efficiency-nodes-ED": {"author_account_age_days": 2484, "last_update": "2025-05-28 16:54:38", "stars": 25}, "https://github.com/Off-Live/ComfyUI-off-suite": {"author_account_age_days": 1539, "last_update": "2024-06-14 12:02:25", "stars": 0}, "https://github.com/OgreLemonSoup/ComfyUI-Load-Image-Gallery": {"author_account_age_days": 320, "last_update": "2025-06-07 02:47:12", "stars": 34}, "https://github.com/OliverCrosby/Comfyui-Minimap": {"author_account_age_days": 2493, "last_update": "2024-08-24 14:10:43", "stars": 97}, "https://github.com/OpalSky-AI/OpalSky_Nodes": {"author_account_age_days": 2103, "last_update": "2024-10-27 20:13:40", "stars": 2}, "https://github.com/OpenArt-AI/ComfyUI-Assistant": {"author_account_age_days": 1142, "last_update": "2024-05-22 22:16:57", "stars": 21}, "https://github.com/OuticNZ/ComfyUI-Simple-Of-Complex": {"author_account_age_days": 2900, "last_update": "2024-08-14 04:00:49", "stars": 0}, "https://github.com/PCMonsterx/ComfyUI-CSV-Loader": {"author_account_age_days": 2032, "last_update": "2025-03-14 12:21:40", "stars": 16}, "https://github.com/Pablerdo/ComfyUI-MultiCutAndDrag": {"author_account_age_days": 3168, "last_update": "2025-03-22 01:25:55", "stars": 3}, "https://github.com/Pablerdo/ComfyUI-ResizeZeptaPayload": {"author_account_age_days": 3168, "last_update": "2025-03-29 00:39:01", "stars": 1}, "https://github.com/Pablerdo/ComfyUI-StableVirtualCameraWrapper": {"author_account_age_days": 3168, "last_update": "2025-04-19 09:40:38", "stars": 1}, "https://github.com/Pablerdo/ComfyUI-ZeptaframePromptMerger": {"author_account_age_days": 3168, "last_update": "2025-03-21 17:42:55", "stars": 1}, "https://github.com/PanicTitan/ComfyUI-Fooocus-V2-Expansion": {"author_account_age_days": 1853, "last_update": "2025-05-09 22:51:17", "stars": 8}, "https://github.com/PanicTitan/ComfyUI-Gallery": {"author_account_age_days": 1853, "last_update": "2025-06-24 02:36:08", "stars": 31}, "https://github.com/Parameshvadivel/ComfyUI-SVGview": {"author_account_age_days": 3195, "last_update": "2024-07-31 13:40:33", "stars": 1}, "https://github.com/ParisNeo/lollms_nodes_suite": {"author_account_age_days": 5137, "last_update": "2025-03-12 07:36:41", "stars": 11}, "https://github.com/ParmanBabra/ComfyUI-Malefish-Custom-Scripts": {"author_account_age_days": 4010, "last_update": "2024-05-22 21:26:35", "stars": 0}, "https://github.com/PauldeLavallaz/comfyui_claude_prompt_generator": {"author_account_age_days": 2211, "last_update": "2025-03-18 17:38:28", "stars": 0}, "https://github.com/PenguinTeo/Comfyui-TextEditor-Penguin": {"author_account_age_days": 341, "last_update": "2025-06-04 14:38:13", "stars": 1}, "https://github.com/Pfaeff/pfaeff-comfyui": {"author_account_age_days": 3575, "last_update": "2024-05-22 18:21:10", "stars": 22}, "https://github.com/Phando/ComfyUI-PhandoNodes": {"author_account_age_days": 5593, "last_update": "2024-09-05 16:12:24", "stars": 0}, "https://github.com/Pheat-AI/Remade_nodes": {"author_account_age_days": 401, "last_update": "2024-10-18 00:04:58", "stars": 3}, "https://github.com/PiggyDance/ComfyUI_OpenCV": {"author_account_age_days": 2754, "last_update": "2025-03-24 10:02:49", "stars": 0}, "https://github.com/Pigidiy/ComfyUI-LikeSpiderAI-SaveMP3": {"author_account_age_days": 256, "last_update": "2025-06-01 16:35:20", "stars": 0}, "https://github.com/Pigidiy/ComfyUI-LikeSpiderAI-UI": {"author_account_age_days": 256, "last_update": "2025-06-05 19:20:04", "stars": 0}, "https://github.com/PixelFunAI/ComfyUI_PixelFun": {"author_account_age_days": 158, "last_update": "2025-01-20 05:44:54", "stars": 3}, "https://github.com/PixelML/ComfyUI-PixelML-CustomNodes": {"author_account_age_days": 478, "last_update": "2025-01-20 06:40:21", "stars": 0}, "https://github.com/PnthrLeo/comfyUI-PL-data-tools": {"author_account_age_days": 2919, "last_update": "2024-12-03 13:39:28", "stars": 1}, "https://github.com/Poseidon-fan/ComfyUI-RabbitMQ-Publisher": {"author_account_age_days": 949, "last_update": "2024-11-07 08:59:23", "stars": 2}, "https://github.com/Positliver/comfyui-zegr": {"author_account_age_days": 3759, "last_update": "2025-01-26 11:51:59", "stars": 1}, "https://github.com/PowerHouseMan/ComfyUI-AdvancedLivePortrait": {"author_account_age_days": 331, "last_update": "2024-08-21 06:14:24", "stars": 2420}, "https://github.com/PressWagon/ComfyUI-StringsAndThings": {"author_account_age_days": 192, "last_update": "2025-05-18 12:01:37", "stars": 2}, "https://github.com/ProGamerGov/ComfyUI_preview360panorama": {"author_account_age_days": 3809, "last_update": "2025-05-25 19:26:43", "stars": 55}, "https://github.com/ProGamerGov/ComfyUI_pytorch360convert": {"author_account_age_days": 3809, "last_update": "2025-02-27 20:23:27", "stars": 12}, "https://github.com/PrunaAI/ComfyUI_pruna": {"author_account_age_days": 1016, "last_update": "2025-06-06 09:08:03", "stars": 61}, "https://github.com/Pseudotools/Pseudocomfy": {"author_account_age_days": 638, "last_update": "2025-06-09 04:22:06", "stars": 1}, "https://github.com/Q-Bug4/Comfyui-Qb-DateNodes": {"author_account_age_days": 2301, "last_update": "2024-11-03 01:52:39", "stars": 1}, "https://github.com/Q-Bug4/Comfyui-Simple-Json-Node": {"author_account_age_days": 2301, "last_update": "2025-03-27 12:51:03", "stars": 5}, "https://github.com/Q-Bug4/comfyui-qbug-batch": {"author_account_age_days": 2301, "last_update": "2025-04-13 03:05:36", "stars": 2}, "https://github.com/QaisMalkawi/ComfyUI-QaisHelper": {"author_account_age_days": 1618, "last_update": "2024-05-23 20:29:30", "stars": 2}, "https://github.com/QijiTec/ComfyUI-RED-UNO": {"author_account_age_days": 831, "last_update": "2025-04-21 01:07:24", "stars": 25}, "https://github.com/R5-Revo/llm-node-comfyui": {"author_account_age_days": 165, "last_update": "2025-05-24 03:55:35", "stars": 6}, "https://github.com/Raapys/ComfyUI-LatentGC_Aggressive": {"author_account_age_days": 4299, "last_update": "2024-08-12 15:55:42", "stars": 4}, "https://github.com/Ravenmelt/ComfyUI-Rodin": {"author_account_age_days": 2442, "last_update": "2025-05-07 13:29:25", "stars": 25}, "https://github.com/Raykosan/ComfyUI_RS-SaturationNode": {"author_account_age_days": 1747, "last_update": "2025-04-12 10:38:46", "stars": 8}, "https://github.com/Raykosan/ComfyUI_RaykoStudio": {"author_account_age_days": 1747, "last_update": "2025-04-12 10:21:00", "stars": 7}, "https://github.com/RaymondProduction/comfyui-zerna-pack": {"author_account_age_days": 3293, "last_update": "2025-03-26 16:10:15", "stars": 0}, "https://github.com/ReBeating/ComfyUI-Artist-Selector": {"author_account_age_days": 1745, "last_update": "2025-02-10 15:39:41", "stars": 1}, "https://github.com/Reithan/negative_rejection_steering": {"author_account_age_days": 4072, "last_update": "2025-04-14 05:14:35", "stars": 7}, "https://github.com/RenderRift/ComfyUI-RenderRiftNodes": {"author_account_age_days": 554, "last_update": "2024-05-22 22:16:41", "stars": 7}, "https://github.com/RhizoNymph/ComfyUI-CLIPSlider": {"author_account_age_days": 1535, "last_update": "2024-09-07 19:47:02", "stars": 9}, "https://github.com/RhizoNymph/ComfyUI-ColorWheel": {"author_account_age_days": 1535, "last_update": "2024-10-13 06:26:51", "stars": 1}, "https://github.com/RhizoNymph/ComfyUI-Latte": {"author_account_age_days": 1535, "last_update": "2024-08-11 07:25:04", "stars": 3}, "https://github.com/RiceRound/ComfyUI_CryptoCat": {"author_account_age_days": 272, "last_update": "2025-06-19 04:14:31", "stars": 89}, "https://github.com/RiceRound/ComfyUI_RiceRound": {"author_account_age_days": 272, "last_update": "2025-03-18 07:31:16", "stars": 17}, "https://github.com/Rinsanga1/comfyui-florence2xy": {"author_account_age_days": 518, "last_update": "2025-06-25 05:42:46", "stars": 0}, "https://github.com/RodrigoSKohl/ComfyUI-Panoramic-ImgStitcher": {"author_account_age_days": 1115, "last_update": "2025-06-09 23:34:07", "stars": 6}, "https://github.com/RodrigoSKohl/InteriorDesign-for-ComfyUI": {"author_account_age_days": 1115, "last_update": "2025-05-14 04:26:55", "stars": 8}, "https://github.com/RodrigoSKohl/comfyui-tryoff-anyone": {"author_account_age_days": 1115, "last_update": "2025-04-14 03:36:22", "stars": 21}, "https://github.com/RomanKuschanow/ComfyUI-Advanced-Latent-Control": {"author_account_age_days": 1749, "last_update": "2025-03-27 17:57:44", "stars": 21}, "https://github.com/Ron-Digital/ComfyUI-SceneGenerator": {"author_account_age_days": 1296, "last_update": "2024-06-28 19:36:30", "stars": 3}, "https://github.com/Runware/ComfyUI-Runware": {"author_account_age_days": 567, "last_update": "2025-06-18 12:05:09", "stars": 92}, "https://github.com/Ryuukeisyou/ComfyUI-SyncTalk": {"author_account_age_days": 2780, "last_update": "2024-09-12 11:54:59", "stars": 39}, "https://github.com/Ryuukeisyou/comfyui_face_parsing": {"author_account_age_days": 2780, "last_update": "2025-02-18 09:22:52", "stars": 168}, "https://github.com/Ryuukeisyou/comfyui_io_helpers": {"author_account_age_days": 2780, "last_update": "2024-07-13 13:10:10", "stars": 1}, "https://github.com/S4MUEL-404/ComfyUI-Image-Position-Blend": {"author_account_age_days": 3454, "last_update": "2025-03-06 14:05:23", "stars": 0}, "https://github.com/S4MUEL-404/ComfyUI-Prompts-Selector": {"author_account_age_days": 3454, "last_update": "2025-03-07 03:24:19", "stars": 0}, "https://github.com/S4MUEL-404/ComfyUI-S4Tool-Image-Overlay": {"author_account_age_days": 3454, "last_update": "2025-03-10 14:16:35", "stars": 0}, "https://github.com/S4MUEL-404/ComfyUI-Text-On-Image": {"author_account_age_days": 3454, "last_update": "2025-03-10 11:48:19", "stars": 7}, "https://github.com/SEkINVR/ComfyUI-SaveAs": {"author_account_age_days": 1025, "last_update": "2024-08-19 01:06:16", "stars": 6}, "https://github.com/SKBv0/ComfyUI_SKBundle": {"author_account_age_days": 1923, "last_update": "2025-06-26 21:46:14", "stars": 39}, "https://github.com/SLAPaper/ComfyUI-Image-Selector": {"author_account_age_days": 4065, "last_update": "2025-03-16 12:13:46", "stars": 96}, "https://github.com/SLAPaper/StableDiffusion-dpmpp_2m_alt-Sampler": {"author_account_age_days": 4065, "last_update": "2025-03-16 12:13:59", "stars": 12}, "https://github.com/SOELexicon/ComfyUI-LexMSDBNodes": {"author_account_age_days": 4451, "last_update": "2025-03-12 00:17:50", "stars": 4}, "https://github.com/SOELexicon/ComfyUI-LexTools": {"author_account_age_days": 4451, "last_update": "2025-03-28 10:50:35", "stars": 30}, "https://github.com/SS-snap/ComfyUI-Ad_scheduler": {"author_account_age_days": 667, "last_update": "2025-04-25 04:53:31", "stars": 6}, "https://github.com/SS-snap/ComfyUI-LBW_flux": {"author_account_age_days": 667, "last_update": "2025-04-25 04:47:47", "stars": 4}, "https://github.com/SS-snap/ComfyUI-Snap_Processing": {"author_account_age_days": 667, "last_update": "2025-04-25 04:54:44", "stars": 61}, "https://github.com/SS-snap/Comfyui_SSsnap_pose-Remapping": {"author_account_age_days": 667, "last_update": "2025-06-18 07:25:25", "stars": 21}, "https://github.com/SXQBW/ComfyUI-Qwen": {"author_account_age_days": 3159, "last_update": "2025-05-26 05:01:41", "stars": 6}, "https://github.com/SXQBW/ComfyUI-Qwen-Omni": {"author_account_age_days": 3159, "last_update": "2025-06-08 07:53:11", "stars": 20}, "https://github.com/SXQBW/ComfyUI-Qwen-VL": {"author_account_age_days": 3159, "last_update": "2025-05-26 06:11:20", "stars": 7}, "https://github.com/SamKhoze/ComfyUI-DeepFuze": {"author_account_age_days": 1811, "last_update": "2024-11-22 19:28:20", "stars": 408}, "https://github.com/SamTyurenkov/comfyui_chatgpt": {"author_account_age_days": 3328, "last_update": "2025-06-16 12:18:20", "stars": 0}, "https://github.com/San4itos/ComfyUI-Save-Images-as-Video": {"author_account_age_days": 1948, "last_update": "2025-05-18 12:37:15", "stars": 1}, "https://github.com/SanDiegoDude/ComfyUI-DeepStereo": {"author_account_age_days": 999, "last_update": "2025-05-26 22:46:39", "stars": 2}, "https://github.com/SanDiegoDude/ComfyUI-Kontext-API": {"author_account_age_days": 999, "last_update": "2025-06-18 16:41:48", "stars": 7}, "https://github.com/SanDiegoDude/ComfyUI-SaveAudioMP3": {"author_account_age_days": 999, "last_update": "2025-05-07 23:48:49", "stars": 3}, "https://github.com/Santodan/santodan-custom-nodes-comfyui": {"author_account_age_days": 3062, "last_update": "2025-06-11 11:43:21", "stars": 0}, "https://github.com/SayanoAI/Comfy-RVC": {"author_account_age_days": 2971, "last_update": "2024-10-09 04:08:31", "stars": 22}, "https://github.com/Sayene/comfyui-base64-to-image-size": {"author_account_age_days": 4047, "last_update": "2025-05-15 12:33:33", "stars": 0}, "https://github.com/Scholar01/ComfyUI-Keyframe": {"author_account_age_days": 3563, "last_update": "2025-01-22 04:09:29", "stars": 16}, "https://github.com/Scorpinaus/ComfyUI-DiffusersLoader": {"author_account_age_days": 1477, "last_update": "2024-08-26 14:51:47", "stars": 16}, "https://github.com/ScreamingHawk/comfyui-ollama-prompt-encode": {"author_account_age_days": 4873, "last_update": "2024-11-29 21:51:05", "stars": 11}, "https://github.com/SeaArtLab/ComfyUI-Long-CLIP": {"author_account_age_days": 444, "last_update": "2025-03-08 04:16:32", "stars": 151}, "https://github.com/SeanScripts/ComfyUI-PixtralLlamaMolmoVision": {"author_account_age_days": 1888, "last_update": "2025-01-31 09:01:23", "stars": 75}, "https://github.com/SeanScripts/ComfyUI-Unload-Model": {"author_account_age_days": 1888, "last_update": "2025-06-13 04:22:23", "stars": 50}, "https://github.com/SeargeDP/ComfyUI_Searge_LLM": {"author_account_age_days": 4539, "last_update": "2024-09-04 09:04:18", "stars": 105}, "https://github.com/SeargeDP/SeargeSDXL": {"author_account_age_days": 4539, "last_update": "2024-05-22 00:28:26", "stars": 847}, "https://github.com/Seedsa/Fooocus_Nodes": {"author_account_age_days": 2975, "last_update": "2025-01-08 07:57:28", "stars": 97}, "https://github.com/Sekiun/ComfyUI-WebpToPNGSequence": {"author_account_age_days": 1839, "last_update": "2025-04-15 12:40:47", "stars": 3}, "https://github.com/Semper-Sursum/HF-Flux-ComfyUI": {"author_account_age_days": 157, "last_update": "2025-03-29 17:35:11", "stars": 2}, "https://github.com/ServiceStack/comfy-asset-downloader": {"author_account_age_days": 5253, "last_update": "2025-05-08 16:21:02", "stars": 7}, "https://github.com/Shadetail/ComfyUI_Eagleshadow": {"author_account_age_days": 3765, "last_update": "2025-03-08 20:09:28", "stars": 4}, "https://github.com/Shakker-Labs/ComfyUI-IPAdapter-Flux": {"author_account_age_days": 216, "last_update": "2025-06-22 08:50:25", "stars": 405}, "https://github.com/Shannooty/ComfyUI-Timer-Nodes": {"author_account_age_days": 1664, "last_update": "2024-12-17 09:20:49", "stars": 3}, "https://github.com/SherryXieYuchen/ComfyUI-Image-Inpainting": {"author_account_age_days": 482, "last_update": "2024-07-03 03:39:49", "stars": 4}, "https://github.com/Shiba-2-shiba/ComfyUI-Magcache-for-SDXL": {"author_account_age_days": 753, "last_update": "2025-06-24 03:54:19", "stars": 2}, "https://github.com/Shiba-2-shiba/ComfyUI_DiffusionModel_fp8_converter": {"author_account_age_days": 754, "last_update": "2025-02-18 07:36:09", "stars": 23}, "https://github.com/Shiba-2-shiba/ComfyUI_FreeU_V2_timestepadd": {"author_account_age_days": 754, "last_update": "2025-03-02 00:15:45", "stars": 0}, "https://github.com/Shiba-2-shiba/comfyui-color-ascii-art-node": {"author_account_age_days": 754, "last_update": "2025-06-13 08:51:55", "stars": 3}, "https://github.com/Shibiko-AI/ShibikoAI-ComfyUI-Tools": {"author_account_age_days": 769, "last_update": "2025-04-23 04:49:00", "stars": 10}, "https://github.com/ShinChven/sc-comfy-nodes": {"author_account_age_days": 4537, "last_update": "2025-05-21 03:07:18", "stars": 1}, "https://github.com/ShmuelRonen/ComfyUI-Apply_Style_Model_Adjust": {"author_account_age_days": 1572, "last_update": "2024-11-23 03:57:20", "stars": 9}, "https://github.com/ShmuelRonen/ComfyUI-AstralAnimator": {"author_account_age_days": 1572, "last_update": "2024-07-18 12:41:22", "stars": 18}, "https://github.com/ShmuelRonen/ComfyUI-Audio_Quality_Enhancer": {"author_account_age_days": 1572, "last_update": "2025-05-11 20:53:31", "stars": 10}, "https://github.com/ShmuelRonen/ComfyUI-CohernetVideoSampler": {"author_account_age_days": 1572, "last_update": "2024-12-23 10:54:08", "stars": 17}, "https://github.com/ShmuelRonen/ComfyUI-DeepSeek_R1-Chat": {"author_account_age_days": 1572, "last_update": "2025-01-27 17:14:24", "stars": 19}, "https://github.com/ShmuelRonen/ComfyUI-EmptyHunyuanLatent": {"author_account_age_days": 1572, "last_update": "2024-12-29 05:30:57", "stars": 8}, "https://github.com/ShmuelRonen/ComfyUI-FramePackWrapper_Plus": {"author_account_age_days": 1572, "last_update": "2025-05-19 21:10:06", "stars": 94}, "https://github.com/ShmuelRonen/ComfyUI-FreeMemory": {"author_account_age_days": 1572, "last_update": "2025-03-20 11:25:12", "stars": 108}, "https://github.com/ShmuelRonen/ComfyUI-FreeVC_wrapper": {"author_account_age_days": 1572, "last_update": "2025-04-03 13:49:04", "stars": 63}, "https://github.com/ShmuelRonen/ComfyUI-Gemini_Flash_2.0_Exp": {"author_account_age_days": 1572, "last_update": "2025-04-22 17:30:51", "stars": 302}, "https://github.com/ShmuelRonen/ComfyUI-Gemini_TTS": {"author_account_age_days": 1572, "last_update": "2025-05-23 14:21:58", "stars": 14}, "https://github.com/ShmuelRonen/ComfyUI-HunyuanVideoSamplerSave": {"author_account_age_days": 1572, "last_update": "2025-02-05 19:26:18", "stars": 19}, "https://github.com/ShmuelRonen/ComfyUI-HunyuanVideoStyler": {"author_account_age_days": 1572, "last_update": "2024-12-31 19:19:42", "stars": 43}, "https://github.com/ShmuelRonen/ComfyUI-ImageMotionGuider": {"author_account_age_days": 1572, "last_update": "2024-12-27 11:19:59", "stars": 42}, "https://github.com/ShmuelRonen/ComfyUI-Janus_pro_vision": {"author_account_age_days": 1572, "last_update": "2025-03-20 11:20:56", "stars": 26}, "https://github.com/ShmuelRonen/ComfyUI-JoyHallo_wrapper": {"author_account_age_days": 1572, "last_update": "2025-03-20 11:24:21", "stars": 8}, "https://github.com/ShmuelRonen/ComfyUI-LatentSyncWrapper": {"author_account_age_days": 1572, "last_update": "2025-06-14 12:30:27", "stars": 829}, "https://github.com/ShmuelRonen/ComfyUI-Orpheus-TTS": {"author_account_age_days": 1572, "last_update": "2025-05-03 22:06:22", "stars": 4}, "https://github.com/ShmuelRonen/ComfyUI-PS_Flatten_Image": {"author_account_age_days": 1572, "last_update": "2025-04-02 10:58:27", "stars": 6}, "https://github.com/ShmuelRonen/ComfyUI-PixArt_XL": {"author_account_age_days": 1572, "last_update": "2025-03-20 11:23:20", "stars": 2}, "https://github.com/ShmuelRonen/ComfyUI-SVDResizer": {"author_account_age_days": 1572, "last_update": "2025-03-09 04:33:26", "stars": 3}, "https://github.com/ShmuelRonen/ComfyUI-Veo2-Experimental": {"author_account_age_days": 1572, "last_update": "2025-04-12 04:25:55", "stars": 26}, "https://github.com/ShmuelRonen/ComfyUI-VideoUpscale_WithModel": {"author_account_age_days": 1572, "last_update": "2025-05-02 20:13:08", "stars": 65}, "https://github.com/ShmuelRonen/ComfyUI-WanVideoKsampler": {"author_account_age_days": 1572, "last_update": "2025-02-27 13:48:05", "stars": 34}, "https://github.com/ShmuelRonen/ComfyUI_ChatterBox_Voice": {"author_account_age_days": 1572, "last_update": "2025-06-04 18:50:40", "stars": 14}, "https://github.com/ShmuelRonen/ComfyUI_Flux_1.1_RAW_API": {"author_account_age_days": 1572, "last_update": "2025-03-20 11:21:27", "stars": 58}, "https://github.com/ShmuelRonen/ComfyUI_Gemini_Flash": {"author_account_age_days": 1572, "last_update": "2025-03-20 04:42:59", "stars": 31}, "https://github.com/ShmuelRonen/ComfyUI_Hedra": {"author_account_age_days": 1572, "last_update": "2025-05-04 16:41:02", "stars": 2}, "https://github.com/ShmuelRonen/ComfyUI_pixtral_large": {"author_account_age_days": 1572, "last_update": "2025-01-08 10:59:35", "stars": 15}, "https://github.com/ShmuelRonen/ComfyUI_pixtral_vision": {"author_account_age_days": 1572, "last_update": "2024-11-20 12:58:30", "stars": 16}, "https://github.com/ShmuelRonen/ComfyUI_wav2lip": {"author_account_age_days": 1572, "last_update": "2024-09-18 13:17:42", "stars": 136}, "https://github.com/ShmuelRonen/DJ_VideoAudioMixer": {"author_account_age_days": 1572, "last_update": "2025-04-04 16:06:49", "stars": 1}, "https://github.com/ShmuelRonen/FluxKontextCreator": {"author_account_age_days": 1572, "last_update": "2025-06-10 17:07:05", "stars": 13}, "https://github.com/ShmuelRonen/comfyui-openai_fm": {"author_account_age_days": 1572, "last_update": "2025-04-03 14:25:24", "stars": 2}, "https://github.com/ShmuelRonen/google_moogle": {"author_account_age_days": 1572, "last_update": "2025-03-27 19:59:35", "stars": 5}, "https://github.com/Shraknard/ComfyUI-Remover": {"author_account_age_days": 2684, "last_update": "2024-07-24 08:42:48", "stars": 5}, "https://github.com/ShunL12324/comfy-portal-endpoint": {"author_account_age_days": 2905, "last_update": "2025-05-17 05:43:21", "stars": 0}, "https://github.com/Siberpone/lazy-pony-prompter": {"author_account_age_days": 820, "last_update": "2025-03-28 05:54:45", "stars": 40}, "https://github.com/Siempreflaco/ComfyUI-NCNodes": {"author_account_age_days": 1023, "last_update": "2025-05-02 20:04:43", "stars": 0}, "https://github.com/Sieyalixnet/ComfyUI_Textarea_Loaders": {"author_account_age_days": 2050, "last_update": "2024-08-30 01:19:54", "stars": 3}, "https://github.com/SignalCha1n/comfyui-ComfySnap": {"author_account_age_days": 77, "last_update": "2025-04-27 15:24:25", "stars": 1}, "https://github.com/SijieMei/ComfyUI-promptHistory": {"author_account_age_days": 2279, "last_update": "2025-03-24 03:32:05", "stars": 0}, "https://github.com/Sinphaltimus/comfyui_fedcoms_node_pack": {"author_account_age_days": 2646, "last_update": "2025-05-10 15:54:59", "stars": 0}, "https://github.com/SipherAGI/comfyui-animatediff": {"author_account_age_days": 772, "last_update": "2024-05-22 18:16:43", "stars": 736}, "https://github.com/SirWillance/FoW_Suite_LIGHT": {"author_account_age_days": 145, "last_update": "2025-04-15 08:48:46", "stars": 2}, "https://github.com/SlackinJack/asyncdiff_comfyui": {"author_account_age_days": 2543, "last_update": "2025-04-03 03:17:56", "stars": 0}, "https://github.com/SlackinJack/distrifuser_comfyui": {"author_account_age_days": 2543, "last_update": "2025-04-03 03:18:17", "stars": 0}, "https://github.com/SleeeepyZhou/ComfyUI-CNtranslator": {"author_account_age_days": 1565, "last_update": "2025-03-29 04:35:17", "stars": 5}, "https://github.com/Slickytail/ComfyUI-InstantX-IPAdapter-SD3": {"author_account_age_days": 3929, "last_update": "2025-03-27 12:47:27", "stars": 60}, "https://github.com/Slickytail/ComfyUI-RegionalAdaptiveSampling": {"author_account_age_days": 3929, "last_update": "2025-04-07 09:20:23", "stars": 19}, "https://github.com/Smirnov75/ComfyUI-mxToolkit": {"author_account_age_days": 1879, "last_update": "2025-05-07 11:44:27", "stars": 231}, "https://github.com/Smuzzies/comfyui_meme_maker": {"author_account_age_days": 1059, "last_update": "2024-07-05 22:01:41", "stars": 1}, "https://github.com/SoftMeng/ComfyUI-DeepCache-Fix": {"author_account_age_days": 3891, "last_update": "2024-07-25 13:09:00", "stars": 13}, "https://github.com/SoftMeng/ComfyUI-PIL": {"author_account_age_days": 3891, "last_update": "2024-10-13 10:02:17", "stars": 6}, "https://github.com/SoftMeng/ComfyUI_ImageToText": {"author_account_age_days": 3891, "last_update": "2024-06-14 08:08:36", "stars": 14}, "https://github.com/SoftMeng/ComfyUI_Mexx_Poster": {"author_account_age_days": 3891, "last_update": "2024-06-14 07:06:27", "stars": 25}, "https://github.com/SoftMeng/ComfyUI_Mexx_Styler": {"author_account_age_days": 3891, "last_update": "2024-06-14 07:09:03", "stars": 23}, "https://github.com/SongGuo11/ComfyUI-SaveAnything-SG11": {"author_account_age_days": 204, "last_update": "2025-03-18 08:59:39", "stars": 0}, "https://github.com/Sorcerio/MBM-Music-Visualizer": {"author_account_age_days": 4615, "last_update": "2024-05-23 01:09:18", "stars": 23}, "https://github.com/SozeInc/ComfyUI-Mobile": {"author_account_age_days": 447, "last_update": "2024-08-22 03:12:11", "stars": 0}, "https://github.com/SozeInc/ComfyUI_Soze": {"author_account_age_days": 447, "last_update": "2025-06-14 17:26:59", "stars": 5}, "https://github.com/SparknightLLC/ComfyUI-ConditionalInterrupt": {"author_account_age_days": 320, "last_update": "2025-04-15 20:36:37", "stars": 3}, "https://github.com/SparknightLLC/ComfyUI-GPENO": {"author_account_age_days": 320, "last_update": "2025-04-15 20:29:05", "stars": 65}, "https://github.com/SparknightLLC/ComfyUI-ImageAutosize": {"author_account_age_days": 320, "last_update": "2025-05-23 19:44:54", "stars": 0}, "https://github.com/SparknightLLC/ComfyUI-ImageAutotone": {"author_account_age_days": 320, "last_update": "2025-04-15 20:35:55", "stars": 13}, "https://github.com/SparknightLLC/ComfyUI-LatentClamp": {"author_account_age_days": 320, "last_update": "2025-04-15 20:36:15", "stars": 2}, "https://github.com/SparknightLLC/ComfyUI-MaskArbiter": {"author_account_age_days": 320, "last_update": "2025-04-15 20:35:34", "stars": 4}, "https://github.com/SparknightLLC/ComfyUI-WeightedRandomChoice": {"author_account_age_days": 320, "last_update": "2025-04-22 00:31:50", "stars": 0}, "https://github.com/SpenserCai/ComfyUI-FunAudioLLM": {"author_account_age_days": 3082, "last_update": "2024-11-27 09:22:05", "stars": 82}, "https://github.com/SshunWang/ComfyUI_CosyVoice": {"author_account_age_days": 2309, "last_update": "2025-02-05 23:48:10", "stars": 12}, "https://github.com/Stability-AI/ComfyUI-SAI_API": {"author_account_age_days": 1210, "last_update": "2025-03-04 12:11:12", "stars": 61}, "https://github.com/Stability-AI/stability-ComfyUI-nodes": {"author_account_age_days": 1210, "last_update": "2024-05-22 15:30:47", "stars": 229}, "https://github.com/StableLlama/ComfyUI-basic_data_handling": {"author_account_age_days": 546, "last_update": "2025-06-15 19:50:31", "stars": 6}, "https://github.com/StarAsh042/ComfyUI_RollingArtist": {"author_account_age_days": 3376, "last_update": "2025-05-05 21:26:43", "stars": 0}, "https://github.com/StarMagicAI/comfyui_tagger": {"author_account_age_days": 3914, "last_update": "2024-09-03 02:01:59", "stars": 5}, "https://github.com/Starnodes2024/ComfyUI_StarNodes": {"author_account_age_days": 370, "last_update": "2025-06-14 07:54:01", "stars": 40}, "https://github.com/StartHua/ComfyUI_OOTDiffusion_CXH": {"author_account_age_days": 3203, "last_update": "2024-06-14 08:12:12", "stars": 122}, "https://github.com/StartHua/ComfyUI_PCDMs": {"author_account_age_days": 3203, "last_update": "2024-05-22 23:21:14", "stars": 7}, "https://github.com/StartHua/ComfyUI_Seg_VITON": {"author_account_age_days": 3203, "last_update": "2024-05-22 23:20:17", "stars": 215}, "https://github.com/StartHua/Comfyui_CXH_DeepLX": {"author_account_age_days": 3203, "last_update": "2024-09-21 02:38:08", "stars": 8}, "https://github.com/StartHua/Comfyui_CXH_FluxLoraMerge": {"author_account_age_days": 3203, "last_update": "2024-12-26 06:56:07", "stars": 24}, "https://github.com/StartHua/Comfyui_CXH_Phi_3.5": {"author_account_age_days": 3203, "last_update": "2024-08-22 04:45:39", "stars": 16}, "https://github.com/StartHua/Comfyui_Gemini2": {"author_account_age_days": 3203, "last_update": "2024-12-12 09:42:42", "stars": 16}, "https://github.com/StartHua/Comfyui_joytag": {"author_account_age_days": 3203, "last_update": "2024-05-22 23:20:28", "stars": 55}, "https://github.com/StartHua/Comfyui_segformer_b2_clothes": {"author_account_age_days": 3203, "last_update": "2024-07-24 14:45:58", "stars": 89}, "https://github.com/Steudio/ComfyUI_Steudio": {"author_account_age_days": 504, "last_update": "2025-05-22 23:05:15", "stars": 67}, "https://github.com/Style-Mosaic/dino-x-comfyui-node": {"author_account_age_days": 235, "last_update": "2025-01-28 21:40:18", "stars": 1}, "https://github.com/SuperBeastsAI/ComfyUI-SuperBeasts": {"author_account_age_days": 457, "last_update": "2024-07-31 02:48:34", "stars": 165}, "https://github.com/SuperMasterBlasterLaser/ComfyUI_YOLO_Classifiers": {"author_account_age_days": 3945, "last_update": "2025-03-29 13:16:05", "stars": 1}, "https://github.com/Suzie1/ComfyUI_Comfyroll_CustomNodes": {"author_account_age_days": 2517, "last_update": "2024-07-24 11:16:13", "stars": 916}, "https://github.com/Sxela/ComfyWarp": {"author_account_age_days": 3739, "last_update": "2025-04-01 22:18:02", "stars": 47}, "https://github.com/SykkoAtHome/ComfyUI_FaceProcessor": {"author_account_age_days": 763, "last_update": "2025-06-15 16:24:45", "stars": 8}, "https://github.com/T-Ph525/ComfyUI-Underage-Filter": {"author_account_age_days": 1303, "last_update": "2025-05-30 12:10:57", "stars": 0}, "https://github.com/TFL-TFL/ComfyUI_Text_Translation": {"author_account_age_days": 1908, "last_update": "2025-05-25 02:27:01", "stars": 61}, "https://github.com/THtianhao/ComfyUI-FaceChain": {"author_account_age_days": 4058, "last_update": "2025-04-28 07:00:45", "stars": 135}, "https://github.com/THtianhao/ComfyUI-Portrait-Maker": {"author_account_age_days": 4058, "last_update": "2024-05-22 21:18:05", "stars": 194}, "https://github.com/TJ16th/comfyUI_TJ_NormalLighting": {"author_account_age_days": 2902, "last_update": "2024-05-23 00:25:37", "stars": 149}, "https://github.com/TKRLAB/ComfyUI_Prompt_List_JSON": {"author_account_age_days": 487, "last_update": "2024-12-23 05:26:14", "stars": 1}, "https://github.com/TMElyralab/Comfyui-MusePose": {"author_account_age_days": 464, "last_update": "2024-07-31 06:21:52", "stars": 409}, "https://github.com/TRI3D-LC/ComfyUI-MiroBoard": {"author_account_age_days": 806, "last_update": "2024-11-21 07:15:20", "stars": 4}, "https://github.com/TRI3D-LC/tri3d-comfyui-nodes": {"author_account_age_days": 806, "last_update": "2025-06-19 08:44:57", "stars": 27}, "https://github.com/TTPlanetPig/Comfyui_Hunyuan3D": {"author_account_age_days": 570, "last_update": "2024-11-10 16:59:42", "stars": 28}, "https://github.com/TTPlanetPig/Comfyui_JC2": {"author_account_age_days": 570, "last_update": "2025-05-21 16:25:36", "stars": 199}, "https://github.com/TTPlanetPig/Comfyui_Object_Detect_QWen_VL": {"author_account_age_days": 570, "last_update": "2025-06-24 15:16:20", "stars": 92}, "https://github.com/TTPlanetPig/Comfyui_Object_Migration": {"author_account_age_days": 570, "last_update": "2024-11-20 16:51:57", "stars": 743}, "https://github.com/TTPlanetPig/Comfyui_TTP_CN_Preprocessor": {"author_account_age_days": 570, "last_update": "2024-08-21 17:52:56", "stars": 29}, "https://github.com/TTPlanetPig/Comfyui_TTP_Toolset": {"author_account_age_days": 570, "last_update": "2025-06-15 11:21:35", "stars": 691}, "https://github.com/TTPlanetPig/TTP_Comfyui_FramePack_SE": {"author_account_age_days": 570, "last_update": "2025-04-25 11:36:15", "stars": 43}, "https://github.com/TW-CUI/TW-CUI-Util": {"author_account_age_days": 398, "last_update": "2024-08-14 01:49:13", "stars": 1}, "https://github.com/TZOOTZ/ComfyUI-TZOOTZ_VHS": {"author_account_age_days": 3417, "last_update": "2025-06-04 10:19:49", "stars": 2}, "https://github.com/TaiTair/comfyui-simswap": {"author_account_age_days": 3934, "last_update": "2024-07-31 18:28:38", "stars": 14}, "https://github.com/Taithrah/ComfyUI_Fens_Simple_Nodes": {"author_account_age_days": 4874, "last_update": "2025-06-20 22:03:36", "stars": 0}, "https://github.com/Taremin/comfyui-keep-multiple-tabs": {"author_account_age_days": 2583, "last_update": "2025-02-25 15:53:35", "stars": 5}, "https://github.com/Taremin/comfyui-prompt-config": {"author_account_age_days": 2583, "last_update": "2025-02-28 03:53:16", "stars": 0}, "https://github.com/Taremin/comfyui-prompt-extranetworks": {"author_account_age_days": 2583, "last_update": "2025-03-04 07:49:21", "stars": 7}, "https://github.com/Taremin/comfyui-string-tools": {"author_account_age_days": 2583, "last_update": "2025-02-26 13:22:39", "stars": 1}, "https://github.com/Taremin/webui-monaco-prompt": {"author_account_age_days": 2583, "last_update": "2025-03-06 08:57:58", "stars": 27}, "https://github.com/TashaSkyUp/EternalKernelPytorchNodes": {"author_account_age_days": 3531, "last_update": "2025-06-22 19:16:21", "stars": 1}, "https://github.com/TeaCrab/ComfyUI-TeaNodes": {"author_account_age_days": 3585, "last_update": "2024-05-22 20:44:05", "stars": 5}, "https://github.com/TechnoByteJS/ComfyUI-TechNodes": {"author_account_age_days": 2043, "last_update": "2024-09-20 23:26:02", "stars": 14}, "https://github.com/TemryL/ComfyS3": {"author_account_age_days": 1236, "last_update": "2024-11-05 14:56:04", "stars": 47}, "https://github.com/TemryL/ComfyUI-IDM-VTON": {"author_account_age_days": 1236, "last_update": "2024-08-20 02:44:02", "stars": 525}, "https://github.com/Temult/TWanSigmaGraph": {"author_account_age_days": 633, "last_update": "2025-04-17 09:39:00", "stars": 8}, "https://github.com/TencentQQGYLab/ComfyUI-ELLA": {"author_account_age_days": 465, "last_update": "2024-08-16 11:21:10", "stars": 380}, "https://github.com/Tenney95/ComfyUI-NodeAligner": {"author_account_age_days": 293, "last_update": "2025-05-09 07:48:08", "stars": 128}, "https://github.com/Tensor-Art/ComfyUI_TENSOR_ART": {"author_account_age_days": 767, "last_update": "2025-04-02 08:31:41", "stars": 9}, "https://github.com/TensorKaze/ComfyUI-TkNodes": {"author_account_age_days": 103, "last_update": "2025-05-26 01:36:34", "stars": 0}, "https://github.com/TheBarret/ZSuite": {"author_account_age_days": 3080, "last_update": "2024-08-10 13:31:03", "stars": 9}, "https://github.com/TheBill2001/ComfyUI-Save-Image-Caption": {"author_account_age_days": 1809, "last_update": "2025-04-04 12:21:18", "stars": 8}, "https://github.com/TheBill2001/comfyui-upscale-by-model": {"author_account_age_days": 1809, "last_update": "2024-06-18 17:57:06", "stars": 9}, "https://github.com/TheLustriVA/ComfyUI-Image-Size-Tools": {"author_account_age_days": 1446, "last_update": "2025-06-21 15:09:46", "stars": 0}, "https://github.com/TheMistoAI/ComfyUI-Anyline": {"author_account_age_days": 532, "last_update": "2024-08-30 09:50:34", "stars": 467}, "https://github.com/TheWhykiki/Whykiki-ComfyUIToolset": {"author_account_age_days": 3685, "last_update": "2025-03-02 22:17:54", "stars": 0}, "https://github.com/ThepExcel/aiangelgallery-comfyui": {"author_account_age_days": 1929, "last_update": "2025-01-15 07:53:09", "stars": 2}, "https://github.com/ThereforeGames/ComfyUI-Unprompted": {"author_account_age_days": 1302, "last_update": "2024-11-13 20:46:08", "stars": 10}, "https://github.com/TiamaTiramisu/risutools": {"author_account_age_days": 368, "last_update": "2025-04-20 22:51:50", "stars": 1}, "https://github.com/TinyTerra/ComfyUI_tinyterraNodes": {"author_account_age_days": 988, "last_update": "2025-03-14 08:21:19", "stars": 529}, "https://github.com/Tlant/ComfyUI-OllamaPromptsGeneratorTlant": {"author_account_age_days": 3037, "last_update": "2025-06-20 16:23:14", "stars": 2}, "https://github.com/ToTheBeginning/ComfyUI-DreamO": {"author_account_age_days": 3756, "last_update": "2025-06-24 14:59:29", "stars": 142}, "https://github.com/Tr1dae/ComfyUI-Dequality": {"author_account_age_days": 906, "last_update": "2025-02-13 16:41:59", "stars": 0}, "https://github.com/Trgtuan10/ComfyUI_YoloSegment_Mask": {"author_account_age_days": 939, "last_update": "2024-09-26 01:46:02", "stars": 1}, "https://github.com/TripleHeadedMonkey/ComfyUI_MileHighStyler": {"author_account_age_days": 1228, "last_update": "2025-06-18 09:32:23", "stars": 55}, "https://github.com/Tropfchen/ComfyUI-Embedding_Picker": {"author_account_age_days": 4221, "last_update": "2024-08-26 16:33:49", "stars": 44}, "https://github.com/Tropfchen/ComfyUI-yaResolutionSelector": {"author_account_age_days": 4221, "last_update": "2024-11-10 20:44:23", "stars": 14}, "https://github.com/TrophiHunter/ComfyUI_Photography_Nodes": {"author_account_age_days": 1085, "last_update": "2025-05-22 07:41:32", "stars": 2}, "https://github.com/Trung0246/ComfyUI-0246": {"author_account_age_days": 3747, "last_update": "2025-03-15 03:39:33", "stars": 127}, "https://github.com/Ttl/ComfyUi_NNLatentUpscale": {"author_account_age_days": 5289, "last_update": "2024-12-01 16:34:24", "stars": 241}, "https://github.com/TylerZoro/SD3-Scaling": {"author_account_age_days": 1653, "last_update": "2024-06-15 16:59:22", "stars": 1}, "https://github.com/Umikaze-job/select_folder_path_easy": {"author_account_age_days": 586, "last_update": "2024-05-22 21:30:13", "stars": 6}, "https://github.com/VAST-AI-Research/ComfyUI-Tripo": {"author_account_age_days": 609, "last_update": "2025-06-26 07:50:36", "stars": 283}, "https://github.com/VK/vk-nodes": {"author_account_age_days": 5713, "last_update": "2025-05-07 19:59:57", "stars": 0}, "https://github.com/Vaibhavs10/ComfyUI-DDUF": {"author_account_age_days": 3348, "last_update": "2025-01-03 15:10:44", "stars": 5}, "https://github.com/VangengLab/ComfyUI-LivePortrait_v2": {"author_account_age_days": 646, "last_update": "2024-11-09 08:00:22", "stars": 6}, "https://github.com/VangengLab/ComfyUI-LivePortrait_v3": {"author_account_age_days": 646, "last_update": "2024-11-09 07:59:42", "stars": 22}, "https://github.com/Vaporbook/ComfyUI-SaveImage-PP": {"author_account_age_days": 5206, "last_update": "2025-05-08 15:04:17", "stars": 0}, "https://github.com/VertexAnomaly/ComfyUI_ImageSentinel": {"author_account_age_days": 1023, "last_update": "2025-04-04 13:50:16", "stars": 1}, "https://github.com/VertexStudio/roblox-comfyui-nodes": {"author_account_age_days": 3346, "last_update": "2024-10-08 16:35:54", "stars": 0}, "https://github.com/VikramxD/VEnhancer-ComfyUI-Wrapper": {"author_account_age_days": 1723, "last_update": "2025-01-14 07:35:00", "stars": 12}, "https://github.com/Visionatrix/ComfyUI-Gemini": {"author_account_age_days": 483, "last_update": "2025-06-26 14:45:14", "stars": 7}, "https://github.com/Visionatrix/ComfyUI-RemoteVAE": {"author_account_age_days": 484, "last_update": "2025-03-12 05:57:35", "stars": 2}, "https://github.com/Visionatrix/ComfyUI-Visionatrix": {"author_account_age_days": 484, "last_update": "2025-06-22 07:38:06", "stars": 1}, "https://github.com/VrchStudio/comfyui-web-viewer": {"author_account_age_days": 1287, "last_update": "2025-06-18 01:35:41", "stars": 219}, "https://github.com/VykosX/ControlFlowUtils": {"author_account_age_days": 2278, "last_update": "2024-12-09 17:24:48", "stars": 118}, "https://github.com/WASasquatch/ComfyUI_Preset_Merger": {"author_account_age_days": 4993, "last_update": "2025-03-27 14:52:46", "stars": 33}, "https://github.com/WASasquatch/FreeU_Advanced": {"author_account_age_days": 4993, "last_update": "2024-10-27 01:49:14", "stars": 119}, "https://github.com/WASasquatch/PPF_Noise_ComfyUI": {"author_account_age_days": 4993, "last_update": "2024-06-14 10:27:23", "stars": 24}, "https://github.com/WASasquatch/PowerNoiseSuite": {"author_account_age_days": 4993, "last_update": "2024-07-31 13:48:33", "stars": 76}, "https://github.com/WASasquatch/WAS_Extras": {"author_account_age_days": 4993, "last_update": "2024-06-17 04:08:37", "stars": 34}, "https://github.com/WUYUDING2583/ComfyUI-Save-Image-Callback": {"author_account_age_days": 2579, "last_update": "2025-01-21 08:19:52", "stars": 2}, "https://github.com/WX-NPS1598/ComfyUI-Auto_Crop_By_NPS": {"author_account_age_days": 343, "last_update": "2024-07-30 04:43:14", "stars": 5}, "https://github.com/WaddingtonHoldings/ComfyUI-InstaSD": {"author_account_age_days": 990, "last_update": "2025-05-30 04:07:24", "stars": 3}, "https://github.com/WainWong/ComfyUI-Loop-image": {"author_account_age_days": 2990, "last_update": "2025-03-28 03:09:27", "stars": 34}, "https://github.com/Wakfull33/ComfyUI-SaveImageCivitAI": {"author_account_age_days": 3327, "last_update": "2024-10-29 11:03:23", "stars": 1}, "https://github.com/WangPengxing/ComfyUI_WPX_Node": {"author_account_age_days": 694, "last_update": "2025-01-20 08:31:55", "stars": 0}, "https://github.com/WarpedAnimation/ComfyUI-WarpedToolset": {"author_account_age_days": 108, "last_update": "2025-06-01 05:35:02", "stars": 2}, "https://github.com/WaveSpeedAI/wavespeed-comfyui": {"author_account_age_days": 156, "last_update": "2025-06-26 03:49:36", "stars": 14}, "https://github.com/WebDev9000/WebDev9000-Nodes": {"author_account_age_days": 4120, "last_update": "2024-06-14 10:28:22", "stars": 1}, "https://github.com/Wenaka2004/ComfyUI-TagClassifier": {"author_account_age_days": 906, "last_update": "2025-01-31 04:28:34", "stars": 23}, "https://github.com/Wicloz/ComfyUI-Simply-Nodes": {"author_account_age_days": 4011, "last_update": "2025-01-05 01:44:38", "stars": 1}, "https://github.com/X-School-Academy/X-FluxAgent": {"author_account_age_days": 85, "last_update": "2025-06-05 08:28:11", "stars": 26}, "https://github.com/X-T-E-R/ComfyUI-EasyCivitai-XTNodes": {"author_account_age_days": 1531, "last_update": "2024-09-04 11:37:04", "stars": 44}, "https://github.com/XLabs-AI/x-flux-comfyui": {"author_account_age_days": 325, "last_update": "2024-10-30 12:51:21", "stars": 1574}, "https://github.com/XWAVEart/comfyui-xwave-xlitch-nodes": {"author_account_age_days": 603, "last_update": "2025-06-04 20:33:17", "stars": 1}, "https://github.com/XchanBik/ComfyUI_SimpleBridgeNode": {"author_account_age_days": 49, "last_update": "2025-05-15 22:10:43", "stars": 0}, "https://github.com/Xclbr7/ComfyUI-Merlin": {"author_account_age_days": 304, "last_update": "2024-09-02 19:36:05", "stars": 29}, "https://github.com/Xiangyu-CAS/HandFixer": {"author_account_age_days": 3755, "last_update": "2025-02-10 02:02:01", "stars": 184}, "https://github.com/XieJunchen/comfyUI_LLM": {"author_account_age_days": 2141, "last_update": "2025-06-07 08:34:02", "stars": 2}, "https://github.com/Xkipper/ComfyUI_SkipperNodes": {"author_account_age_days": 3847, "last_update": "2025-04-26 20:13:45", "stars": 0}, "https://github.com/XmYx/deforum-comfy-nodes": {"author_account_age_days": 2973, "last_update": "2025-05-26 19:50:55", "stars": 190}, "https://github.com/Xyem/Xycuno-Oobabooga": {"author_account_age_days": 4692, "last_update": "2024-05-23 00:14:14", "stars": 4}, "https://github.com/YMC-GitHub/comfyui_node_ymc_effect_shatter": {"author_account_age_days": 3062, "last_update": "2025-04-12 15:00:21", "stars": 0}, "https://github.com/YMC-GitHub/ymc-node-as-x-type": {"author_account_age_days": 3062, "last_update": "2025-06-06 12:23:11", "stars": 0}, "https://github.com/YMC-GitHub/ymc-node-suite-comfyui": {"author_account_age_days": 3062, "last_update": "2025-06-09 08:07:23", "stars": 20}, "https://github.com/YMC-GitHub/ymc_node_joy": {"author_account_age_days": 3062, "last_update": "2025-06-19 07:24:28", "stars": 0}, "https://github.com/YOUR-WORST-TACO/ComfyUI-TacoNodes": {"author_account_age_days": 4123, "last_update": "2024-05-22 20:48:23", "stars": 15}, "https://github.com/YRIKKA/ComfyUI-InferenceTimeScaling": {"author_account_age_days": 367, "last_update": "2025-02-27 21:13:18", "stars": 20}, "https://github.com/Yahweasel/ComfyUI-MinDalle": {"author_account_age_days": 3017, "last_update": "2025-05-26 20:42:34", "stars": 0}, "https://github.com/Yanick112/ComfyUI-ToSVG": {"author_account_age_days": 1181, "last_update": "2025-06-20 14:10:06", "stars": 198}, "https://github.com/YaroslavIv/comfyui_swd": {"author_account_age_days": 1944, "last_update": "2025-06-23 04:10:43", "stars": 2}, "https://github.com/YarvixPA/ComfyUI-NeuralMedia": {"author_account_age_days": 574, "last_update": "2025-06-26 08:25:44", "stars": 4}, "https://github.com/YinBailiang/MergeBlockWeighted_fo_ComfyUI": {"author_account_age_days": 1153, "last_update": "2025-01-03 03:58:20", "stars": 16}, "https://github.com/Yuan-ManX/ComfyUI-AniSora": {"author_account_age_days": 1807, "last_update": "2025-05-27 04:11:59", "stars": 23}, "https://github.com/Yuan-ManX/ComfyUI-AudioX": {"author_account_age_days": 1807, "last_update": "2025-05-27 04:14:59", "stars": 10}, "https://github.com/Yuan-ManX/ComfyUI-Bagel": {"author_account_age_days": 1807, "last_update": "2025-05-28 03:00:53", "stars": 29}, "https://github.com/Yuan-ManX/ComfyUI-ChatterboxTTS": {"author_account_age_days": 1807, "last_update": "2025-05-30 08:13:06", "stars": 8}, "https://github.com/Yuan-ManX/ComfyUI-Cobra": {"author_account_age_days": 1807, "last_update": "2025-04-18 02:06:26", "stars": 5}, "https://github.com/Yuan-ManX/ComfyUI-Dia": {"author_account_age_days": 1807, "last_update": "2025-04-24 06:58:05", "stars": 3}, "https://github.com/Yuan-ManX/ComfyUI-Direct3D-S2": {"author_account_age_days": 1807, "last_update": "2025-06-10 03:24:25", "stars": 4}, "https://github.com/Yuan-ManX/ComfyUI-HiDream-I1": {"author_account_age_days": 1807, "last_update": "2025-04-14 02:56:22", "stars": 8}, "https://github.com/Yuan-ManX/ComfyUI-Hunyuan3D-2.1": {"author_account_age_days": 1807, "last_update": "2025-06-16 07:03:54", "stars": 12}, "https://github.com/Yuan-ManX/ComfyUI-HunyuanPortrait": {"author_account_age_days": 1807, "last_update": "2025-05-28 09:47:34", "stars": 7}, "https://github.com/Yuan-ManX/ComfyUI-HunyuanVideo-Avatar": {"author_account_age_days": 1807, "last_update": "2025-05-29 07:49:15", "stars": 24}, "https://github.com/Yuan-ManX/ComfyUI-Kimi-VL": {"author_account_age_days": 1807, "last_update": "2025-04-17 06:55:14", "stars": 1}, "https://github.com/Yuan-ManX/ComfyUI-LLaMA-Mesh": {"author_account_age_days": 1807, "last_update": "2024-11-29 09:52:04", "stars": 5}, "https://github.com/Yuan-ManX/ComfyUI-LayerAnimate": {"author_account_age_days": 1807, "last_update": "2025-04-01 03:16:53", "stars": 4}, "https://github.com/Yuan-ManX/ComfyUI-LiveCC": {"author_account_age_days": 1807, "last_update": "2025-05-27 04:14:30", "stars": 4}, "https://github.com/Yuan-ManX/ComfyUI-Matrix-Game": {"author_account_age_days": 1807, "last_update": "2025-05-13 08:05:00", "stars": 3}, "https://github.com/Yuan-ManX/ComfyUI-MoviiGen": {"author_account_age_days": 1807, "last_update": "2025-05-27 04:12:30", "stars": 9}, "https://github.com/Yuan-ManX/ComfyUI-Multiverse": {"author_account_age_days": 1807, "last_update": "2025-05-09 06:51:35", "stars": 1}, "https://github.com/Yuan-ManX/ComfyUI-Muyan-TTS": {"author_account_age_days": 1807, "last_update": "2025-05-08 08:21:24", "stars": 2}, "https://github.com/Yuan-ManX/ComfyUI-OmniGen2": {"author_account_age_days": 1807, "last_update": "2025-06-26 02:46:07", "stars": 94}, "https://github.com/Yuan-ManX/ComfyUI-OrpheusTTS": {"author_account_age_days": 1807, "last_update": "2025-03-24 02:47:23", "stars": 6}, "https://github.com/Yuan-ManX/ComfyUI-PhotoDoodle": {"author_account_age_days": 1807, "last_update": "2025-02-28 03:47:54", "stars": 3}, "https://github.com/Yuan-ManX/ComfyUI-PosterCraft": {"author_account_age_days": 1807, "last_update": "2025-06-26 10:00:28", "stars": 1}, "https://github.com/Yuan-ManX/ComfyUI-SkyReels-A2": {"author_account_age_days": 1807, "last_update": "2025-05-27 04:14:03", "stars": 26}, "https://github.com/Yuan-ManX/ComfyUI-SoundHub": {"author_account_age_days": 1807, "last_update": "2024-11-27 08:00:48", "stars": 1}, "https://github.com/Yuan-ManX/ComfyUI-Step1X-3D": {"author_account_age_days": 1807, "last_update": "2025-05-16 02:36:06", "stars": 12}, "https://github.com/Yuan-ManX/ComfyUI-Step1X-Edit": {"author_account_age_days": 1807, "last_update": "2025-04-29 07:36:52", "stars": 11}, "https://github.com/Yuan-ManX/ComfyUI-StyleStudio": {"author_account_age_days": 1807, "last_update": "2025-03-10 09:38:08", "stars": 4}, "https://github.com/Yuan-ManX/ComfyUI-UNO": {"author_account_age_days": 1807, "last_update": "2025-04-11 07:37:33", "stars": 10}, "https://github.com/Yuan-ManX/ComfyUI-Vui": {"author_account_age_days": 1807, "last_update": "2025-06-12 03:55:32", "stars": 3}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-APISR": {"author_account_age_days": 707, "last_update": "2024-05-22 14:14:46", "stars": 376}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Animated-optical-illusions": {"author_account_age_days": 707, "last_update": "2024-06-14 07:06:15", "stars": 21}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-ArtGallery": {"author_account_age_days": 707, "last_update": "2024-06-12 04:40:50", "stars": 509}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-BRIA_AI-RMBG": {"author_account_age_days": 707, "last_update": "2024-05-22 14:14:18", "stars": 806}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-DeepSeek-JanusPro": {"author_account_age_days": 707, "last_update": "2025-02-21 09:45:54", "stars": 103}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-DepthFM": {"author_account_age_days": 707, "last_update": "2024-05-22 14:14:03", "stars": 74}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Gemini": {"author_account_age_days": 707, "last_update": "2024-05-22 14:15:11", "stars": 762}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-InstantID": {"author_account_age_days": 707, "last_update": "2024-05-22 13:57:55", "stars": 1418}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Phi-3-mini": {"author_account_age_days": 707, "last_update": "2024-06-30 08:41:40", "stars": 205}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-PhotoMaker-ZHO": {"author_account_age_days": 707, "last_update": "2024-05-22 14:13:49", "stars": 816}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-PixArt-alpha-Diffusers": {"author_account_age_days": 707, "last_update": "2024-05-22 13:40:58", "stars": 50}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Q-Align": {"author_account_age_days": 707, "last_update": "2024-05-22 14:15:52", "stars": 5}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Qwen-VL-API": {"author_account_age_days": 707, "last_update": "2024-05-22 14:14:57", "stars": 207}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-SVD-ZHO": {"author_account_age_days": 707, "last_update": "2024-05-22 13:40:44", "stars": 107}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-SegMoE": {"author_account_age_days": 707, "last_update": "2024-05-22 13:41:14", "stars": 79}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-Text_Image-Composite": {"author_account_age_days": 707, "last_update": "2024-05-31 12:03:55", "stars": 110}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-UltraEdit-ZHO": {"author_account_age_days": 707, "last_update": "2024-07-11 14:59:07", "stars": 147}, "https://github.com/ZHO-ZHO-ZHO/ComfyUI-YoloWorld-EfficientSAM": {"author_account_age_days": 707, "last_update": "2024-05-22 13:01:07", "stars": 744}, "https://github.com/ZHO-ZHO-ZHO/comfyui-portrait-master-zh-cn": {"author_account_age_days": 707, "last_update": "2024-06-14 09:00:04", "stars": 1745}, "https://github.com/ZZXYWQ/ComfyUI-ZZXYWQ": {"author_account_age_days": 1640, "last_update": "2024-07-19 06:38:39", "stars": 23}, "https://github.com/Zachary116699/ComfyUI-LoadImageWithMetaDataEx": {"author_account_age_days": 1728, "last_update": "2025-06-10 07:10:28", "stars": 1}, "https://github.com/ZaneA/ComfyUI-ImageReward": {"author_account_age_days": 5907, "last_update": "2025-02-24 19:55:45", "stars": 31}, "https://github.com/Zar4X/ComfyUI-Batch-Process": {"author_account_age_days": 798, "last_update": "2025-06-25 14:40:23", "stars": 2}, "https://github.com/Zar4X/ComfyUI-Image-Resizing": {"author_account_age_days": 798, "last_update": "2025-06-25 14:43:35", "stars": 0}, "https://github.com/Zch6111/AI_Text_Comfyui": {"author_account_age_days": 440, "last_update": "2025-06-05 03:22:47", "stars": 1}, "https://github.com/ZeDarkAdam/ComfyUI-Embeddings-Tools": {"author_account_age_days": 1561, "last_update": "2024-06-23 19:19:40", "stars": 2}, "https://github.com/Zehong-Ma/ComfyUI-MagCache": {"author_account_age_days": 1569, "last_update": "2025-06-21 03:29:12", "stars": 140}, "https://github.com/Zeks/comfyui-rapidfire": {"author_account_age_days": 4988, "last_update": "2025-01-14 18:28:43", "stars": 0}, "https://github.com/Zuellni/ComfyUI-Custom-Nodes": {"author_account_age_days": 890, "last_update": "2023-09-19 12:11:26", "stars": 44}, "https://github.com/Zuellni/ComfyUI-ExLlama-Nodes": {"author_account_age_days": 890, "last_update": "2024-12-06 14:22:11", "stars": 118}, "https://github.com/Zuellni/ComfyUI-PickScore-Nodes": {"author_account_age_days": 890, "last_update": "2024-09-08 09:17:04", "stars": 37}, "https://github.com/a-und-b/ComfyUI_Delay": {"author_account_age_days": 807, "last_update": "2025-01-10 11:20:35", "stars": 4}, "https://github.com/a-und-b/ComfyUI_IC-Light-v2_fal": {"author_account_age_days": 807, "last_update": "2025-05-05 08:34:47", "stars": 36}, "https://github.com/a-und-b/ComfyUI_JSON_Helper": {"author_account_age_days": 807, "last_update": "2025-01-09 15:54:55", "stars": 3}, "https://github.com/a-und-b/ComfyUI_LoRA_from_URL": {"author_account_age_days": 807, "last_update": "2025-01-16 13:40:26", "stars": 3}, "https://github.com/a-und-b/ComfyUI_MaskAreaCondition": {"author_account_age_days": 807, "last_update": "2025-04-28 08:23:36", "stars": 2}, "https://github.com/a1lazydog/ComfyUI-AudioScheduler": {"author_account_age_days": 5169, "last_update": "2024-08-08 03:04:19", "stars": 103}, "https://github.com/abdozmantar/ComfyUI-DeepExtract": {"author_account_age_days": 520, "last_update": "2025-04-26 15:13:57", "stars": 37}, "https://github.com/aburahamu/ComfyUI-IsNiceParts": {"author_account_age_days": 440, "last_update": "2024-06-14 12:01:40", "stars": 3}, "https://github.com/aburahamu/ComfyUI-RequestsPoster": {"author_account_age_days": 440, "last_update": "2024-06-14 13:59:24", "stars": 2}, "https://github.com/abyz22/image_control": {"author_account_age_days": 530, "last_update": "2024-08-31 08:39:44", "stars": 16}, "https://github.com/acorderob/sd-webui-prompt-postprocessor": {"author_account_age_days": 4195, "last_update": "2025-05-31 10:32:11", "stars": 36}, "https://github.com/adbrasi/ComfyUI-TrashNodes-DownloadHuggingface": {"author_account_age_days": 1071, "last_update": "2024-05-22 23:24:45", "stars": 6}, "https://github.com/adieyal/comfyui-dynamicprompts": {"author_account_age_days": 5384, "last_update": "2024-07-09 14:21:09", "stars": 320}, "https://github.com/adigayung/ComfyUI-Translator": {"author_account_age_days": 581, "last_update": "2024-09-09 03:36:52", "stars": 10}, "https://github.com/adriflex/ComfyUI_Blender_Texdiff": {"author_account_age_days": 2589, "last_update": "2024-05-22 23:14:18", "stars": 2}, "https://github.com/aegis72/aegisflow_utility_nodes": {"author_account_age_days": 951, "last_update": "2024-10-03 11:11:39", "stars": 31}, "https://github.com/aegis72/comfyui-styles-all": {"author_account_age_days": 951, "last_update": "2024-05-22 22:10:41", "stars": 52}, "https://github.com/agilly1989/ComfyUI_agilly1989_motorway": {"author_account_age_days": 2252, "last_update": "2025-05-02 22:23:32", "stars": 7}, "https://github.com/ahernandezmiro/ComfyUI-GCP_Storage_tools": {"author_account_age_days": 4363, "last_update": "2025-01-03 18:48:03", "stars": 2}, "https://github.com/ai-liam/comfyui-liam": {"author_account_age_days": 1888, "last_update": "2024-06-22 03:27:52", "stars": 2}, "https://github.com/ai-liam/comfyui_liam_util": {"author_account_age_days": 1888, "last_update": "2024-05-22 22:21:23", "stars": 1}, "https://github.com/ai-shizuka/ComfyUI-tbox": {"author_account_age_days": 379, "last_update": "2025-04-22 10:21:03", "stars": 15}, "https://github.com/aiaiaikkk/ComfyUI-Curve": {"author_account_age_days": 271, "last_update": "2025-06-26 01:21:51", "stars": 103}, "https://github.com/aianimation55/ComfyUI-FatLabels": {"author_account_age_days": 641, "last_update": "2024-05-22 21:26:01", "stars": 5}, "https://github.com/aiartvn/A2V_Multi_Image_Composite": {"author_account_age_days": 152, "last_update": "2025-02-02 04:14:06", "stars": 1}, "https://github.com/aicoder-max/Pillar_For_ComfyUI": {"author_account_age_days": 37, "last_update": "2025-06-05 09:40:09", "stars": 2}, "https://github.com/aicuai/aicu-comfyui-stability-ai-api": {"author_account_age_days": 710, "last_update": "2025-02-21 13:31:14", "stars": 1}, "https://github.com/aidec/Comfyui_TextBatch_aidec": {"author_account_age_days": 4270, "last_update": "2025-04-09 20:26:38", "stars": 8}, "https://github.com/aidenli/ComfyUI_NYJY": {"author_account_age_days": 4915, "last_update": "2025-06-16 06:06:12", "stars": 126}, "https://github.com/aigc-apps/EasyAnimate": {"author_account_age_days": 686, "last_update": "2025-03-06 11:41:28", "stars": 2171}, "https://github.com/aigc-apps/VideoX-Fun": {"author_account_age_days": 686, "last_update": "2025-06-26 08:17:03", "stars": 1140}, "https://github.com/aimerib/ComfyUI_HigherBitDepthSaveImage": {"author_account_age_days": 3038, "last_update": "2024-09-14 03:03:01", "stars": 2}, "https://github.com/ainewsto/Comfyui-chatgpt-api": {"author_account_age_days": 1021, "last_update": "2025-04-30 04:08:25", "stars": 50}, "https://github.com/ainewsto/Comfyui-google-veo2-api": {"author_account_age_days": 1021, "last_update": "2025-05-06 06:43:48", "stars": 5}, "https://github.com/ainewsto/Comfyui_Comfly_v2": {"author_account_age_days": 1021, "last_update": "2025-06-19 05:59:08", "stars": 40}, "https://github.com/ainewsto/comfyui-labs-google": {"author_account_age_days": 1021, "last_update": "2025-06-18 02:12:39", "stars": 80}, "https://github.com/aisabervisionlab/ComfyUI_merge_ASVL": {"author_account_age_days": 357, "last_update": "2024-07-31 13:39:36", "stars": 2}, "https://github.com/ajbergh/comfyui-ethnicity_hairstyle_clip_encoder": {"author_account_age_days": 2150, "last_update": "2025-02-28 22:07:11", "stars": 2}, "https://github.com/akatz-ai/ComfyUI-AKatz-Nodes": {"author_account_age_days": 403, "last_update": "2025-04-05 00:47:00", "stars": 25}, "https://github.com/akatz-ai/ComfyUI-Basic-Math": {"author_account_age_days": 403, "last_update": "2025-06-18 01:49:05", "stars": 0}, "https://github.com/akatz-ai/ComfyUI-DepthCrafter-Nodes": {"author_account_age_days": 403, "last_update": "2025-05-05 04:23:55", "stars": 226}, "https://github.com/akatz-ai/ComfyUI-Depthflow-Nodes": {"author_account_age_days": 403, "last_update": "2025-06-24 22:27:31", "stars": 284}, "https://github.com/akatz-ai/ComfyUI-X-Portrait-Nodes": {"author_account_age_days": 403, "last_update": "2025-04-20 05:29:13", "stars": 85}, "https://github.com/akierson/ComfyUI-textnodes": {"author_account_age_days": 2702, "last_update": "2024-10-20 20:12:15", "stars": 0}, "https://github.com/akierson/comfyui-colornodes": {"author_account_age_days": 2702, "last_update": "2024-10-20 20:14:09", "stars": 1}, "https://github.com/akspa0/ComfyUI-FapMixPlus": {"author_account_age_days": 498, "last_update": "2024-11-11 02:59:10", "stars": 1}, "https://github.com/al-swaiti/All-IN-ONE-style": {"author_account_age_days": 1297, "last_update": "2024-07-30 05:59:49", "stars": 5}, "https://github.com/al-swaiti/ComfyUI-CascadeResolutions": {"author_account_age_days": 1297, "last_update": "2024-07-31 13:48:47", "stars": 5}, "https://github.com/al-swaiti/ComfyUI-OllamaGemini": {"author_account_age_days": 1297, "last_update": "2025-05-17 14:07:02", "stars": 102}, "https://github.com/alanhuang67/ComfyUI-FAI-Node": {"author_account_age_days": 3857, "last_update": "2024-08-02 03:35:41", "stars": 14}, "https://github.com/alastor-666-1933/caching_to_not_waste": {"author_account_age_days": 4417, "last_update": "2025-05-29 19:57:12", "stars": 5}, "https://github.com/aleolidev/comfy_kaizen_package": {"author_account_age_days": 2945, "last_update": "2025-06-21 11:10:52", "stars": 0}, "https://github.com/alessandroperilli/APW_Nodes": {"author_account_age_days": 3875, "last_update": "2025-06-19 14:34:41", "stars": 4}, "https://github.com/alessandroperilli/apw_nodes": {"author_account_age_days": 3876, "last_update": "2025-06-19 14:34:41", "stars": 4}, "https://github.com/alessandrozonta/ComfyUI-CenterNode": {"author_account_age_days": 1564, "last_update": "2024-11-14 12:20:40", "stars": 7}, "https://github.com/alessandrozonta/ComfyUI-Layers": {"author_account_age_days": 1564, "last_update": "2024-07-31 13:46:32", "stars": 51}, "https://github.com/alessandrozonta/ComfyUI-OpenPose": {"author_account_age_days": 1564, "last_update": "2024-07-31 13:51:14", "stars": 26}, "https://github.com/alessandrozonta/ComfyUI-PoseDirection": {"author_account_age_days": 1564, "last_update": "2025-06-03 10:12:26", "stars": 1}, "https://github.com/alessandrozonta/Comfyui-LoopLoader": {"author_account_age_days": 1564, "last_update": "2025-02-21 13:28:39", "stars": 2}, "https://github.com/alexcong/ComfyUI_QwenVL": {"author_account_age_days": 3954, "last_update": "2025-06-07 21:58:57", "stars": 70}, "https://github.com/alexgenovese/ComfyUI-UNO-Flux": {"author_account_age_days": 5382, "last_update": "2025-06-16 17:27:27", "stars": 0}, "https://github.com/alexgenovese/ComfyUI_HF_Servelress_Inference": {"author_account_age_days": 5382, "last_update": "2024-09-01 13:04:48", "stars": 12}, "https://github.com/alexisrolland/ComfyUI-Phi": {"author_account_age_days": 3653, "last_update": "2025-06-02 16:03:13", "stars": 9}, "https://github.com/alexopus/ComfyUI-Image-Saver": {"author_account_age_days": 3047, "last_update": "2025-06-15 13:48:54", "stars": 102}, "https://github.com/ali-vilab/ACE_plus": {"author_account_age_days": 856, "last_update": "2025-04-21 06:36:02", "stars": 1196}, "https://github.com/ali1234/comfyui-job-iterator": {"author_account_age_days": 5228, "last_update": "2024-11-16 07:51:07", "stars": 116}, "https://github.com/alisson-anjos/ComfyUI-Ollama-Describer": {"author_account_age_days": 968, "last_update": "2025-06-09 05:43:18", "stars": 72}, "https://github.com/alpertunga-bile/image-caption-comfyui": {"author_account_age_days": 1640, "last_update": "2025-05-21 20:09:00", "stars": 9}, "https://github.com/alpertunga-bile/prompt-generator-comfyui": {"author_account_age_days": 1640, "last_update": "2025-05-21 20:05:48", "stars": 102}, "https://github.com/alsritter/asymmetric-tiling-comfyui": {"author_account_age_days": 2361, "last_update": "2024-05-22 20:43:07", "stars": 17}, "https://github.com/alt-key-project/comfyui-dream-project": {"author_account_age_days": 1027, "last_update": "2025-02-16 14:45:43", "stars": 100}, "https://github.com/alt-key-project/comfyui-dream-video-batches": {"author_account_age_days": 1027, "last_update": "2025-02-23 10:28:40", "stars": 72}, "https://github.com/an90ray/ComfyUI_RErouter_CustomNodes": {"author_account_age_days": 556, "last_update": "2024-05-22 22:21:00", "stars": 0}, "https://github.com/andersxa/comfyui-PromptAttention": {"author_account_age_days": 3320, "last_update": "2024-06-20 11:09:25", "stars": 22}, "https://github.com/andygill/comfyui-sunflower-nodes": {"author_account_age_days": 6160, "last_update": "2025-01-02 04:23:22", "stars": 1}, "https://github.com/angeloshredder/StableCascadeResizer": {"author_account_age_days": 2196, "last_update": "2024-05-23 00:12:55", "stars": 2}, "https://github.com/angree/ComfyUI-Q_GLB_Material_Modifier": {"author_account_age_days": 3088, "last_update": "2025-05-30 22:51:59", "stars": 1}, "https://github.com/angree/ComfyUI-Q_find-mask-size": {"author_account_age_days": 3088, "last_update": "2025-05-30 22:53:04", "stars": 0}, "https://github.com/anhkhoatranle30/Handy-Nodes-ComfyUI": {"author_account_age_days": 2117, "last_update": "2025-03-27 14:09:26", "stars": 1}, "https://github.com/antrobot1234/antrobots-comfyUI-nodepack": {"author_account_age_days": 3230, "last_update": "2025-04-02 21:40:49", "stars": 23}, "https://github.com/arcum42/ComfyUI_SageUtils": {"author_account_age_days": 6115, "last_update": "2025-06-25 21:51:09", "stars": 6}, "https://github.com/aria1th/ComfyUI-LogicUtils": {"author_account_age_days": 2712, "last_update": "2025-05-22 16:16:37", "stars": 53}, "https://github.com/asaddi/ComfyUI-YALLM-node": {"author_account_age_days": 3902, "last_update": "2025-03-27 14:39:38", "stars": 4}, "https://github.com/asaddi/YALLM-LlamaVision": {"author_account_age_days": 3902, "last_update": "2025-03-27 14:42:04", "stars": 5}, "https://github.com/asagi4/ComfyUI-Adaptive-Guidance": {"author_account_age_days": 808, "last_update": "2025-05-03 18:12:38", "stars": 59}, "https://github.com/asagi4/ComfyUI-CADS": {"author_account_age_days": 808, "last_update": "2025-06-23 17:58:56", "stars": 42}, "https://github.com/asagi4/ComfyUI-NPNet": {"author_account_age_days": 808, "last_update": "2024-12-10 17:20:10", "stars": 17}, "https://github.com/asagi4/comfyui-prompt-control": {"author_account_age_days": 808, "last_update": "2025-06-21 10:17:42", "stars": 300}, "https://github.com/asagi4/comfyui-utility-nodes": {"author_account_age_days": 808, "last_update": "2025-01-30 23:01:52", "stars": 8}, "https://github.com/asdrabael/Hunyuan-Multi-Lora-Loader": {"author_account_age_days": 382, "last_update": "2025-02-09 02:50:51", "stars": 4}, "https://github.com/asutermo/ComfyUI-Flux-TryOff": {"author_account_age_days": 5279, "last_update": "2025-03-10 21:05:14", "stars": 42}, "https://github.com/aszc-dev/ComfyUI-CoreMLSuite": {"author_account_age_days": 3094, "last_update": "2025-04-01 21:45:31", "stars": 160}, "https://github.com/atluslin/comfyui_arcane_style_trans": {"author_account_age_days": 3394, "last_update": "2025-03-14 01:25:41", "stars": 0}, "https://github.com/atmaranto/ComfyUI-SaveAsScript": {"author_account_age_days": 2716, "last_update": "2024-10-09 08:44:54", "stars": 144}, "https://github.com/attashe/ComfyUI-FluxRegionAttention": {"author_account_age_days": 3991, "last_update": "2025-03-02 16:37:39", "stars": 131}, "https://github.com/audioscavenger/ComfyUI-Thumbnails": {"author_account_age_days": 4488, "last_update": "2025-01-06 23:41:08", "stars": 30}, "https://github.com/audioscavenger/save-image-extended-comfyui": {"author_account_age_days": 4488, "last_update": "2025-06-02 02:01:15", "stars": 98}, "https://github.com/austinbrown34/ComfyUI-IO-Helpers": {"author_account_age_days": 4465, "last_update": "2025-02-13 14:29:22", "stars": 1}, "https://github.com/avatechai/avatar-graph-comfyui": {"author_account_age_days": 1221, "last_update": "2024-05-22 21:14:14", "stars": 262}, "https://github.com/avenstack/ComfyUI-AV-FunASR": {"author_account_age_days": 71, "last_update": "2025-06-13 05:53:11", "stars": 9}, "https://github.com/avenstack/ComfyUI-AV-LatentSync": {"author_account_age_days": 71, "last_update": "2025-05-28 14:27:42", "stars": 2}, "https://github.com/avenstack/ComfyUI-AV-MegaTTS3": {"author_account_age_days": 71, "last_update": "2025-05-25 13:35:03", "stars": 0}, "https://github.com/avocadori/ComfyUI-load-image-prompt-lora": {"author_account_age_days": 440, "last_update": "2025-06-02 20:35:37", "stars": 0}, "https://github.com/aws-samples/comfyui-llm-node-for-amazon-bedrock": {"author_account_age_days": 3926, "last_update": "2025-03-07 08:09:46", "stars": 26}, "https://github.com/azazeal04/Azazeal_Anime_Characters_ComfyUI": {"author_account_age_days": 785, "last_update": "2025-06-21 18:26:40", "stars": 1}, "https://github.com/azure-dragon-ai/ComfyUI-ClipScore-Nodes": {"author_account_age_days": 668, "last_update": "2024-05-22 23:16:28", "stars": 4}, "https://github.com/azure-dragon-ai/ComfyUI-HPSv2-Nodes": {"author_account_age_days": 668, "last_update": "2025-05-11 05:18:07", "stars": 6}, "https://github.com/babe-and-spencer-enterprises/base-comfyui-node": {"author_account_age_days": 44, "last_update": "2025-06-23 16:12:19", "stars": 3}, "https://github.com/bablueza/ComfyUI-Vaja-Ai4thai": {"author_account_age_days": 2112, "last_update": "2025-04-23 04:14:55", "stars": 0}, "https://github.com/babydjac/comfyui-grok-prompts": {"author_account_age_days": 772, "last_update": "2025-06-06 09:47:19", "stars": 1}, "https://github.com/babydjac/comfyui-smart-scaler": {"author_account_age_days": 772, "last_update": "2025-05-16 07:28:19", "stars": 0}, "https://github.com/badayvedat/ComfyUI-fal-Connector": {"author_account_age_days": 2138, "last_update": "2025-06-20 15:50:56", "stars": 42}, "https://github.com/badjeff/comfyui_lora_tag_loader": {"author_account_age_days": 5730, "last_update": "2024-05-22 20:40:03", "stars": 83}, "https://github.com/badxprogramm/ComfyUI-GradientBlur": {"author_account_age_days": 639, "last_update": "2025-04-10 03:47:51", "stars": 1}, "https://github.com/baicai99/ComfyUI-FrameSkipping": {"author_account_age_days": 1198, "last_update": "2025-06-23 02:50:12", "stars": 10}, "https://github.com/bananasss00/ComfyUI-SP-Nodes": {"author_account_age_days": 2901, "last_update": "2025-02-22 18:17:31", "stars": 14}, "https://github.com/bananasss00/ComfyUI-flux_fill_patcher": {"author_account_age_days": 2901, "last_update": "2024-11-25 20:04:20", "stars": 7}, "https://github.com/banodoco/steerable-motion": {"author_account_age_days": 769, "last_update": "2025-06-23 14:13:04", "stars": 904}, "https://github.com/banqingyuan/ComfyUI-text-replace": {"author_account_age_days": 2666, "last_update": "2024-09-22 16:14:22", "stars": 0}, "https://github.com/bartly/Comfyui_babel_removebg_api": {"author_account_age_days": 4494, "last_update": "2024-10-14 00:48:34", "stars": 6}, "https://github.com/bash-j/mikey_nodes": {"author_account_age_days": 4555, "last_update": "2025-03-22 01:52:20", "stars": 155}, "https://github.com/bbtaivi/ComfyUI-Aiv-Param": {"author_account_age_days": 821, "last_update": "2025-02-16 03:01:20", "stars": 1}, "https://github.com/bear2b/comfyui-argo-nodes": {"author_account_age_days": 3332, "last_update": "2025-01-16 11:11:38", "stars": 0}, "https://github.com/bedovyy/ComfyUI_NAIDGenerator": {"author_account_age_days": 730, "last_update": "2025-05-30 14:03:50", "stars": 67}, "https://github.com/bemoregt/ComfyUI_CustomNode_Image2Spectrum": {"author_account_age_days": 3327, "last_update": "2025-03-28 12:13:20", "stars": 1}, "https://github.com/benda1989/CosyVoice2_ComfyUI": {"author_account_age_days": 2480, "last_update": "2025-04-16 08:39:57", "stars": 19}, "https://github.com/benda1989/Sonic_ComfyUI": {"author_account_age_days": 2480, "last_update": "2025-02-24 10:04:56", "stars": 3}, "https://github.com/benjamin-bertram/Comfyui_OIDN_Denoiser": {"author_account_age_days": 2133, "last_update": "2025-06-12 22:37:05", "stars": 0}, "https://github.com/benjiyaya/ComfyUI-HunyuanVideoImagesGuider": {"author_account_age_days": 480, "last_update": "2025-01-14 10:42:44", "stars": 29}, "https://github.com/benjiyaya/ComfyUI-KokoroTTS": {"author_account_age_days": 480, "last_update": "2025-03-18 20:13:52", "stars": 56}, "https://github.com/bentoml/comfy-pack": {"author_account_age_days": 2278, "last_update": "2025-06-23 08:13:37", "stars": 160}, "https://github.com/big-mon/ComfyUI-ResolutionPresets": {"author_account_age_days": 3043, "last_update": "2025-04-12 17:05:21", "stars": 1}, "https://github.com/bikiam/ComfyUI_WhisperSRT": {"author_account_age_days": 517, "last_update": "2025-06-01 13:56:23", "stars": 0}, "https://github.com/bilal-arikan/ComfyUI_TextAssets": {"author_account_age_days": 3875, "last_update": "2024-05-22 23:23:50", "stars": 2}, "https://github.com/billwuhao/ComfyUI_ACE-Step": {"author_account_age_days": 2303, "last_update": "2025-05-28 08:39:13", "stars": 147}, "https://github.com/billwuhao/ComfyUI_AudioTools": {"author_account_age_days": 2303, "last_update": "2025-06-03 17:11:26", "stars": 50}, "https://github.com/billwuhao/ComfyUI_CSM": {"author_account_age_days": 2303, "last_update": "2025-06-02 14:00:17", "stars": 5}, "https://github.com/billwuhao/ComfyUI_DiffRhythm": {"author_account_age_days": 2303, "last_update": "2025-05-30 12:12:57", "stars": 110}, "https://github.com/billwuhao/ComfyUI_EraX-WoW-Turbo": {"author_account_age_days": 2303, "last_update": "2025-05-23 09:41:43", "stars": 14}, "https://github.com/billwuhao/ComfyUI_IndexTTS": {"author_account_age_days": 2303, "last_update": "2025-06-02 13:54:19", "stars": 75}, "https://github.com/billwuhao/ComfyUI_KokoroTTS_MW": {"author_account_age_days": 2303, "last_update": "2025-06-02 14:03:36", "stars": 24}, "https://github.com/billwuhao/ComfyUI_MegaTTS3": {"author_account_age_days": 2303, "last_update": "2025-06-11 01:01:40", "stars": 102}, "https://github.com/billwuhao/ComfyUI_NotaGen": {"author_account_age_days": 2303, "last_update": "2025-06-06 02:58:28", "stars": 48}, "https://github.com/billwuhao/ComfyUI_OneButtonPrompt": {"author_account_age_days": 2303, "last_update": "2025-05-06 01:07:39", "stars": 20}, "https://github.com/billwuhao/ComfyUI_OuteTTS": {"author_account_age_days": 2303, "last_update": "2025-06-11 06:14:07", "stars": 9}, "https://github.com/billwuhao/ComfyUI_PortraitTools": {"author_account_age_days": 2303, "last_update": "2025-06-15 13:31:45", "stars": 15}, "https://github.com/billwuhao/ComfyUI_SOME": {"author_account_age_days": 2303, "last_update": "2025-06-10 08:08:17", "stars": 5}, "https://github.com/billwuhao/ComfyUI_SparkTTS": {"author_account_age_days": 2303, "last_update": "2025-05-23 09:45:08", "stars": 44}, "https://github.com/billwuhao/ComfyUI_StepAudioTTS": {"author_account_age_days": 2303, "last_update": "2025-05-23 09:45:26", "stars": 127}, "https://github.com/billwuhao/ComfyUI_gemmax": {"author_account_age_days": 2303, "last_update": "2025-05-30 12:17:42", "stars": 21}, "https://github.com/billwuhao/ComfyUI_parakeet-tdt": {"author_account_age_days": 2303, "last_update": "2025-06-15 13:24:58", "stars": 3}, "https://github.com/billwuhao/Comfyui_HeyGem": {"author_account_age_days": 2303, "last_update": "2025-06-02 14:14:35", "stars": 74}, "https://github.com/bitaffinity/ComfyUI_HF_Inference": {"author_account_age_days": 424, "last_update": "2024-06-14 10:23:29", "stars": 4}, "https://github.com/black-forest-labs/bfl-comfy-nodes": {"author_account_age_days": 464, "last_update": "2025-02-07 22:13:26", "stars": 78}, "https://github.com/blackcodetavern/ComfyUI-Benripack": {"author_account_age_days": 3241, "last_update": "2024-09-07 09:06:00", "stars": 2}, "https://github.com/blepping/ComfyUI-ApplyResAdapterUnet": {"author_account_age_days": 521, "last_update": "2025-02-27 16:14:46", "stars": 31}, "https://github.com/blepping/ComfyUI-bleh": {"author_account_age_days": 521, "last_update": "2025-05-23 13:02:29", "stars": 106}, "https://github.com/blepping/ComfyUI-sonar": {"author_account_age_days": 521, "last_update": "2025-06-12 15:38:19", "stars": 42}, "https://github.com/blepping/comfyui_jankdiffusehigh": {"author_account_age_days": 521, "last_update": "2025-05-06 10:28:37", "stars": 34}, "https://github.com/blepping/comfyui_jankhidiffusion": {"author_account_age_days": 521, "last_update": "2025-05-06 10:34:03", "stars": 124}, "https://github.com/blepping/comfyui_overly_complicated_sampling": {"author_account_age_days": 521, "last_update": "2025-05-25 16:30:41", "stars": 26}, "https://github.com/blib-la/blibla-comfyui-extensions": {"author_account_age_days": 641, "last_update": "2025-04-29 06:49:03", "stars": 173}, "https://github.com/blob8/ComfyUI_sloppy-comic": {"author_account_age_days": 435, "last_update": "2024-09-20 18:53:34", "stars": 8}, "https://github.com/blovett80/ComfyUI-PixelDojo": {"author_account_age_days": 570, "last_update": "2025-03-27 10:30:18", "stars": 0}, "https://github.com/blueraincoatli/comfyUI_SillyNodes": {"author_account_age_days": 680, "last_update": "2025-01-17 09:17:48", "stars": 3}, "https://github.com/bluevisor/ComfyUI_PS_Blend_Node": {"author_account_age_days": 4934, "last_update": "2025-03-31 08:48:48", "stars": 2}, "https://github.com/bmad4ever/ComfyUI-Bmad-DirtyUndoRedo": {"author_account_age_days": 3904, "last_update": "2024-05-22 18:11:51", "stars": 47}, "https://github.com/bmad4ever/comfyui_ab_samplercustom": {"author_account_age_days": 3904, "last_update": "2024-09-17 20:18:46", "stars": 9}, "https://github.com/bmad4ever/comfyui_lists_cartesian_product": {"author_account_age_days": 3904, "last_update": "2025-03-17 14:49:40", "stars": 4}, "https://github.com/bmad4ever/comfyui_quilting": {"author_account_age_days": 3904, "last_update": "2025-03-17 14:50:15", "stars": 10}, "https://github.com/bmad4ever/comfyui_wfc_like": {"author_account_age_days": 3904, "last_update": "2025-03-17 14:51:47", "stars": 5}, "https://github.com/bobmagicii/comfykit-custom-nodes": {"author_account_age_days": 5112, "last_update": "2024-08-22 22:28:30", "stars": 1}, "https://github.com/bollerdominik/ComfyUI-load-lora-from-url": {"author_account_age_days": 3781, "last_update": "2025-06-05 16:21:55", "stars": 0}, "https://github.com/bombax-xiaoice/ComfyUI-Allegro": {"author_account_age_days": 276, "last_update": "2025-05-13 04:00:11", "stars": 4}, "https://github.com/bombax-xiaoice/ComfyUI-DisPose": {"author_account_age_days": 276, "last_update": "2025-03-03 06:49:40", "stars": 0}, "https://github.com/bombax-xiaoice/ComfyUI-MagicDance": {"author_account_age_days": 276, "last_update": "2024-12-26 04:43:40", "stars": 2}, "https://github.com/bombax-xiaoice/ComfyUI-Open-Sora-I2V": {"author_account_age_days": 276, "last_update": "2025-01-21 07:58:50", "stars": 1}, "https://github.com/bombax-xiaoice/ComfyUI-OpenSoraPlan": {"author_account_age_days": 276, "last_update": "2025-01-22 05:38:11", "stars": 1}, "https://github.com/bombless/comfyUI-RememberingUtils": {"author_account_age_days": 4926, "last_update": "2024-12-25 01:31:05", "stars": 0}, "https://github.com/bongsang/ComfyUI-Bongsang": {"author_account_age_days": 3971, "last_update": "2025-01-05 05:42:30", "stars": 0}, "https://github.com/boredofnames/ComfyUI-ntfy": {"author_account_age_days": 4465, "last_update": "2025-03-28 00:54:54", "stars": 0}, "https://github.com/boricuapab/ComfyUI-Bori-JsonSetGetConverter": {"author_account_age_days": 1936, "last_update": "2025-06-02 06:52:21", "stars": 0}, "https://github.com/bradsec/ComfyUI_ResolutionSelector": {"author_account_age_days": 4023, "last_update": "2024-07-07 12:15:49", "stars": 12}, "https://github.com/bradsec/ComfyUI_StringEssentials": {"author_account_age_days": 4023, "last_update": "2025-06-09 06:17:09", "stars": 14}, "https://github.com/braintacles/braintacles-comfyui-nodes": {"author_account_age_days": 798, "last_update": "2024-07-31 15:01:52", "stars": 1}, "https://github.com/brantje/ComfyUI-api-tools": {"author_account_age_days": 4612, "last_update": "2025-05-28 11:37:29", "stars": 3}, "https://github.com/brantje/ComfyUI_MagicQuill": {"author_account_age_days": 4612, "last_update": "2025-05-20 19:32:21", "stars": 3}, "https://github.com/brayevalerien/ComfyUI-resynthesizer": {"author_account_age_days": 1961, "last_update": "2025-02-19 10:33:17", "stars": 20}, "https://github.com/brianfitzgerald/style_aligned_comfy": {"author_account_age_days": 4607, "last_update": "2025-03-24 20:04:44", "stars": 300}, "https://github.com/bronkula/comfyui-fitsize": {"author_account_age_days": 5522, "last_update": "2024-05-22 21:32:34", "stars": 49}, "https://github.com/bruefire/ComfyUI-SeqImageLoader": {"author_account_age_days": 2733, "last_update": "2025-06-22 19:09:54", "stars": 41}, "https://github.com/budihartono/comfyui-aspect-ratio-presets": {"author_account_age_days": 5053, "last_update": "2025-06-12 10:55:46", "stars": 1}, "https://github.com/budihartono/comfyui_otonx_nodes": {"author_account_age_days": 5053, "last_update": "2024-07-31 16:01:47", "stars": 1}, "https://github.com/bugltd/ComfyLab-Pack": {"author_account_age_days": 167, "last_update": "2025-05-13 17:35:50", "stars": 5}, "https://github.com/burnsbert/ComfyUI-EBU-LMStudio": {"author_account_age_days": 5013, "last_update": "2025-06-08 01:10:11", "stars": 13}, "https://github.com/burnsbert/ComfyUI-EBU-PromptHelper": {"author_account_age_days": 5013, "last_update": "2025-06-08 01:11:52", "stars": 1}, "https://github.com/burnsbert/ComfyUI-EBU-Workflow": {"author_account_age_days": 5013, "last_update": "2025-06-08 02:44:44", "stars": 0}, "https://github.com/bvhari/ComfyUI_CFGStar": {"author_account_age_days": 1542, "last_update": "2025-04-10 17:53:08", "stars": 1}, "https://github.com/bvhari/ComfyUI_ImageProcessing": {"author_account_age_days": 1542, "last_update": "2025-03-30 18:55:42", "stars": 22}, "https://github.com/bvhari/ComfyUI_PerpCFG": {"author_account_age_days": 1542, "last_update": "2025-03-30 18:53:54", "stars": 1}, "https://github.com/bvhari/ComfyUI_PerpWeight": {"author_account_age_days": 1542, "last_update": "2025-03-30 18:55:52", "stars": 12}, "https://github.com/bvhari/ComfyUI_SUNoise": {"author_account_age_days": 1542, "last_update": "2025-03-30 18:55:16", "stars": 13}, "https://github.com/bytedance/ComfyUI-HyperLoRA": {"author_account_age_days": 4455, "last_update": "2025-06-25 08:47:51", "stars": 349}, "https://github.com/bytedance/ComfyUI_InfiniteYou": {"author_account_age_days": 4455, "last_update": "2025-05-23 23:21:52", "stars": 188}, "https://github.com/bytedance/comfyui-lumi-batcher": {"author_account_age_days": 4455, "last_update": "2025-06-26 06:06:39", "stars": 163}, "https://github.com/c0ffymachyne/ComfyUI_BeatByte": {"author_account_age_days": 4882, "last_update": "2025-04-03 03:08:15", "stars": 5}, "https://github.com/c0ffymachyne/ComfyUI_SignalProcessing": {"author_account_age_days": 4882, "last_update": "2025-05-14 01:41:00", "stars": 11}, "https://github.com/cake-ml/tiny-sana-preview": {"author_account_age_days": 249, "last_update": "2025-02-08 00:36:49", "stars": 2}, "https://github.com/calcuis/gguf": {"author_account_age_days": 1015, "last_update": "2025-06-20 00:59:37", "stars": 66}, "https://github.com/caleboleary/ComfyUI-Arc2Face": {"author_account_age_days": 3670, "last_update": "2024-09-02 23:00:00", "stars": 44}, "https://github.com/caleboleary/Comfyui-calbenodes": {"author_account_age_days": 3670, "last_update": "2024-09-16 19:27:58", "stars": 1}, "https://github.com/camenduru/ComfyUI-TostAI": {"author_account_age_days": 2136, "last_update": "2024-08-22 04:04:06", "stars": 1}, "https://github.com/cardenluo/ComfyUI-Apt_Preset": {"author_account_age_days": 771, "last_update": "2025-06-21 03:25:27", "stars": 22}, "https://github.com/casterpollux/MiniMax-bmo": {"author_account_age_days": 40, "last_update": "2025-06-24 19:22:18", "stars": 31}, "https://github.com/catboxanon/comfyui_stealth_pnginfo": {"author_account_age_days": 899, "last_update": "2025-04-09 03:39:29", "stars": 3}, "https://github.com/cathodeDreams/comfyui-azul-scripts": {"author_account_age_days": 836, "last_update": "2025-04-30 17:03:38", "stars": 0}, "https://github.com/cdb-boop/ComfyUI-Bringing-Old-Photos-Back-to-Life": {"author_account_age_days": 1572, "last_update": "2024-09-12 06:55:50", "stars": 459}, "https://github.com/cdb-boop/comfyui-image-round": {"author_account_age_days": 1572, "last_update": "2025-05-10 13:32:13", "stars": 10}, "https://github.com/cdxOo/comfyui-text-node-with-comments": {"author_account_age_days": 3663, "last_update": "2024-08-03 00:54:38", "stars": 2}, "https://github.com/celoron/ComfyUI-VisualQueryTemplate": {"author_account_age_days": 5379, "last_update": "2025-04-01 20:35:56", "stars": 12}, "https://github.com/celsojr2013/comfyui_jamworks_client": {"author_account_age_days": 3766, "last_update": "2024-06-23 12:35:44", "stars": 0}, "https://github.com/celsojr2013/comfyui_simpletools": {"author_account_age_days": 3766, "last_update": "2024-06-22 11:35:40", "stars": 2}, "https://github.com/cenzijing/ComfyUI-Markmap": {"author_account_age_days": 1832, "last_update": "2025-01-04 21:00:08", "stars": 1}, "https://github.com/cerspense/ComfyUI_cspnodes": {"author_account_age_days": 3049, "last_update": "2024-12-17 04:07:09", "stars": 33}, "https://github.com/ceruleandeep/ComfyUI-LLaVA-Captioner": {"author_account_age_days": 1521, "last_update": "2024-08-03 16:22:31", "stars": 133}, "https://github.com/cganimitta/ComfyUI_CGAnimittaTools": {"author_account_age_days": 943, "last_update": "2025-04-11 05:29:55", "stars": 41}, "https://github.com/chakib-belgaid/ComfyUI-autosize": {"author_account_age_days": 4203, "last_update": "2024-06-14 07:13:20", "stars": 0}, "https://github.com/chakib-belgaid/Comfyui_Prompt_styler": {"author_account_age_days": 4203, "last_update": "2024-07-01 12:40:52", "stars": 0}, "https://github.com/chandlergis/ComfyUI-IMG_Query": {"author_account_age_days": 723, "last_update": "2024-05-23 01:25:57", "stars": 1}, "https://github.com/chandlergis/ComfyUI_EmojiOverlay": {"author_account_age_days": 723, "last_update": "2024-06-14 09:05:03", "stars": 0}, "https://github.com/changwook987/ComfyUI-Small-Utility": {"author_account_age_days": 1561, "last_update": "2025-01-25 17:18:32", "stars": 0}, "https://github.com/chaojie/ComfyUI-AniPortrait": {"author_account_age_days": 5201, "last_update": "2024-05-22 22:26:03", "stars": 252}, "https://github.com/chaojie/ComfyUI-CameraCtrl-Wrapper": {"author_account_age_days": 5201, "last_update": "2024-06-14 09:07:23", "stars": 21}, "https://github.com/chaojie/ComfyUI-Champ": {"author_account_age_days": 5201, "last_update": "2024-05-22 22:26:47", "stars": 24}, "https://github.com/chaojie/ComfyUI-DragAnything": {"author_account_age_days": 5201, "last_update": "2024-06-14 10:23:53", "stars": 70}, "https://github.com/chaojie/ComfyUI-DragNUWA": {"author_account_age_days": 5201, "last_update": "2024-06-14 10:25:01", "stars": 407}, "https://github.com/chaojie/ComfyUI-DynamiCrafter": {"author_account_age_days": 5201, "last_update": "2024-06-14 10:23:59", "stars": 129}, "https://github.com/chaojie/ComfyUI-EasyAnimate": {"author_account_age_days": 5201, "last_update": "2024-05-22 22:24:00", "stars": 54}, "https://github.com/chaojie/ComfyUI-Gemma": {"author_account_age_days": 5201, "last_update": "2024-05-22 22:27:47", "stars": 6}, "https://github.com/chaojie/ComfyUI-I2VGEN-XL": {"author_account_age_days": 5201, "last_update": "2024-06-14 09:06:10", "stars": 28}, "https://github.com/chaojie/ComfyUI-Img2Img-Turbo": {"author_account_age_days": 5201, "last_update": "2024-05-22 22:26:30", "stars": 36}, "https://github.com/chaojie/ComfyUI-LaVIT": {"author_account_age_days": 5201, "last_update": "2024-06-14 10:27:44", "stars": 12}, "https://github.com/chaojie/ComfyUI-LightGlue": {"author_account_age_days": 5201, "last_update": "2024-01-20 16:53:51", "stars": 48}, "https://github.com/chaojie/ComfyUI-Moore-AnimateAnyone": {"author_account_age_days": 5201, "last_update": "2024-06-10 20:16:06", "stars": 213}, "https://github.com/chaojie/ComfyUI-Motion-Vector-Extractor": {"author_account_age_days": 5201, "last_update": "2024-06-14 10:26:15", "stars": 1}, "https://github.com/chaojie/ComfyUI-MotionCtrl": {"author_account_age_days": 5201, "last_update": "2024-06-14 10:26:02", "stars": 138}, "https://github.com/chaojie/ComfyUI-MotionCtrl-SVD": {"author_account_age_days": 5201, "last_update": "2024-06-14 10:26:30", "stars": 86}, "https://github.com/chaojie/ComfyUI-MuseTalk": {"author_account_age_days": 5201, "last_update": "2024-05-22 22:25:07", "stars": 263}, "https://github.com/chaojie/ComfyUI-MuseV": {"author_account_age_days": 5201, "last_update": "2024-05-22 22:25:31", "stars": 159}, "https://github.com/chaojie/ComfyUI-Open-Sora": {"author_account_age_days": 5201, "last_update": "2024-07-19 05:13:25", "stars": 103}, "https://github.com/chaojie/ComfyUI-Open-Sora-Plan": {"author_account_age_days": 5201, "last_update": "2024-05-29 16:15:10", "stars": 50}, "https://github.com/chaojie/ComfyUI-Panda3d": {"author_account_age_days": 5201, "last_update": "2024-06-14 10:28:47", "stars": 16}, "https://github.com/chaojie/ComfyUI-Pymunk": {"author_account_age_days": 5201, "last_update": "2024-06-14 12:02:32", "stars": 16}, "https://github.com/chaojie/ComfyUI-RAFT": {"author_account_age_days": 5201, "last_update": "2024-06-14 11:02:00", "stars": 26}, "https://github.com/chaojie/ComfyUI-SimDA": {"author_account_age_days": 5201, "last_update": "2024-06-14 12:02:39", "stars": 13}, "https://github.com/chaojie/ComfyUI-Trajectory": {"author_account_age_days": 5201, "last_update": "2024-05-22 22:27:12", "stars": 6}, "https://github.com/chaojie/ComfyUI-Video-Editing-X-Attention": {"author_account_age_days": 5201, "last_update": "2024-06-14 10:28:16", "stars": 17}, "https://github.com/chaojie/ComfyUI-dust3r": {"author_account_age_days": 5201, "last_update": "2024-05-22 22:27:33", "stars": 22}, "https://github.com/chaojie/ComfyUI_StreamingT2V": {"author_account_age_days": 5201, "last_update": "2024-06-14 10:26:21", "stars": 36}, "https://github.com/chaosaiart/Chaosaiart-Nodes": {"author_account_age_days": 701, "last_update": "2025-05-06 07:15:41", "stars": 103}, "https://github.com/charlyad142/ComfyUI_bfl_api_pro_nodes": {"author_account_age_days": 2983, "last_update": "2025-05-07 19:34:13", "stars": 0}, "https://github.com/chaunceyyann/comfyui-image-processing-nodes": {"author_account_age_days": 4234, "last_update": "2025-06-20 02:01:53", "stars": 0}, "https://github.com/checkbins/checkbin-comfy": {"author_account_age_days": 251, "last_update": "2025-01-31 18:05:33", "stars": 0}, "https://github.com/chenbaiyujason/ComfyUI_StepFun": {"author_account_age_days": 2102, "last_update": "2024-12-05 14:45:27", "stars": 6}, "https://github.com/chenlongming/ComfyUI_Spectral": {"author_account_age_days": 3545, "last_update": "2025-02-22 17:20:35", "stars": 1}, "https://github.com/chenpipi0807/ComfyUI-Index-TTS": {"author_account_age_days": 654, "last_update": "2025-06-25 14:30:23", "stars": 178}, "https://github.com/chenpipi0807/ComfyUI_NSFW_Godie": {"author_account_age_days": 654, "last_update": "2025-03-20 11:48:28", "stars": 2}, "https://github.com/chenpipi0807/PIP_ArtisticWords": {"author_account_age_days": 654, "last_update": "2025-03-21 07:29:20", "stars": 26}, "https://github.com/chenpx976/ComfyUI-RunRunRun": {"author_account_age_days": 3782, "last_update": "2024-05-23 01:19:37", "stars": 0}, "https://github.com/cherninlab/logo-generator-comfyui": {"author_account_age_days": 464, "last_update": "2024-12-22 15:45:31", "stars": 1}, "https://github.com/chesnokovivan/ComfyUI-Novakid": {"author_account_age_days": 1897, "last_update": "2024-06-10 20:15:56", "stars": 0}, "https://github.com/chflame163/ComfyUI_CatVTON_Wrapper": {"author_account_age_days": 812, "last_update": "2025-01-01 12:55:16", "stars": 338}, "https://github.com/chflame163/ComfyUI_CogView4_Wrapper": {"author_account_age_days": 812, "last_update": "2025-03-06 09:27:25", "stars": 51}, "https://github.com/chflame163/ComfyUI_FaceSimilarity": {"author_account_age_days": 812, "last_update": "2025-03-31 13:12:01", "stars": 33}, "https://github.com/chflame163/ComfyUI_Janus_Wrapper": {"author_account_age_days": 812, "last_update": "2025-03-12 02:00:43", "stars": 17}, "https://github.com/chflame163/ComfyUI_LayerStyle": {"author_account_age_days": 812, "last_update": "2025-06-20 00:45:04", "stars": 2358}, "https://github.com/chflame163/ComfyUI_LayerStyle_Advance": {"author_account_age_days": 812, "last_update": "2025-05-17 12:57:26", "stars": 363}, "https://github.com/chflame163/ComfyUI_MSSpeech_TTS": {"author_account_age_days": 812, "last_update": "2025-03-31 13:11:24", "stars": 29}, "https://github.com/chflame163/ComfyUI_OmniGen_Wrapper": {"author_account_age_days": 812, "last_update": "2025-03-12 01:58:47", "stars": 141}, "https://github.com/chflame163/ComfyUI_WordCloud": {"author_account_age_days": 812, "last_update": "2025-03-31 13:11:39", "stars": 108}, "https://github.com/chibiace/ComfyUI-Chibi-Nodes": {"author_account_age_days": 3216, "last_update": "2025-03-18 11:13:16", "stars": 74}, "https://github.com/choey/Comfy-Topaz": {"author_account_age_days": 5890, "last_update": "2024-09-28 08:02:47", "stars": 191}, "https://github.com/chou18194766xx/comfyui-EncryptSave": {"author_account_age_days": 593, "last_update": "2025-05-18 07:55:45", "stars": 4}, "https://github.com/chou18194766xx/comfyui_EncryptPreview": {"author_account_age_days": 593, "last_update": "2025-04-26 12:29:43", "stars": 2}, "https://github.com/chri002/ComfyUI_depthMapOperation": {"author_account_age_days": 2087, "last_update": "2025-05-27 06:19:56", "stars": 10}, "https://github.com/chris-arsenault/ComfyUI-AharaNodes": {"author_account_age_days": 4340, "last_update": "2024-12-25 16:45:58", "stars": 0}, "https://github.com/chris-the-wiz/EmbeddingsCurveEditor_ComfyUI": {"author_account_age_days": 2164, "last_update": "2024-07-31 13:51:59", "stars": 7}, "https://github.com/chrisfreilich/virtuoso-nodes": {"author_account_age_days": 1100, "last_update": "2025-04-19 22:57:17", "stars": 87}, "https://github.com/chrisgoringe/cg-controller": {"author_account_age_days": 4432, "last_update": "2025-04-25 00:43:21", "stars": 71}, "https://github.com/chrisgoringe/cg-image-filter": {"author_account_age_days": 4432, "last_update": "2025-06-23 06:11:13", "stars": 63}, "https://github.com/chrisgoringe/cg-noisetools": {"author_account_age_days": 4432, "last_update": "2024-12-17 04:09:18", "stars": 17}, "https://github.com/chrisgoringe/cg-prompt-info": {"author_account_age_days": 4432, "last_update": "2024-05-22 21:07:33", "stars": 30}, "https://github.com/chrisgoringe/cg-use-everywhere": {"author_account_age_days": 4432, "last_update": "2025-06-26 02:09:05", "stars": 727}, "https://github.com/chrish-slingshot/CrasHUtils": {"author_account_age_days": 968, "last_update": "2024-10-29 22:55:39", "stars": 13}, "https://github.com/chrissy0/chris-comfyui-nodes": {"author_account_age_days": 2422, "last_update": "2024-09-17 16:09:35", "stars": 1}, "https://github.com/christian-byrne/audio-separation-nodes-comfyui": {"author_account_age_days": 1716, "last_update": "2025-06-16 04:01:47", "stars": 239}, "https://github.com/christian-byrne/claude-code-comfyui-nodes": {"author_account_age_days": 1716, "last_update": "2025-06-17 04:43:43", "stars": 1}, "https://github.com/christian-byrne/comfyui-default-values-manager": {"author_account_age_days": 1716, "last_update": "2024-07-28 20:52:51", "stars": 11}, "https://github.com/christian-byrne/comfyui-search-navigation": {"author_account_age_days": 1716, "last_update": "2024-06-26 04:41:12", "stars": 7}, "https://github.com/christian-byrne/img2colors-comfyui-node": {"author_account_age_days": 1716, "last_update": "2025-01-05 18:48:59", "stars": 13}, "https://github.com/christian-byrne/img2txt-comfyui-nodes": {"author_account_age_days": 1716, "last_update": "2025-03-14 10:38:33", "stars": 89}, "https://github.com/christian-byrne/size-match-compositing-nodes": {"author_account_age_days": 1716, "last_update": "2025-01-05 17:45:02", "stars": 5}, "https://github.com/christian-byrne/youtube-dl-comfyui": {"author_account_age_days": 1716, "last_update": "2024-10-01 16:32:14", "stars": 4}, "https://github.com/ciga2011/ComfyUI-MarkItDown": {"author_account_age_days": 4566, "last_update": "2025-02-27 20:16:01", "stars": 8}, "https://github.com/ciga2011/ComfyUI-Pollinations": {"author_account_age_days": 4566, "last_update": "2025-01-14 15:23:14", "stars": 3}, "https://github.com/ciga2011/ComfyUI-PromptOptimizer": {"author_account_age_days": 4566, "last_update": "2025-01-16 02:24:50", "stars": 7}, "https://github.com/ciri/comfyui-model-downloader": {"author_account_age_days": 5738, "last_update": "2025-03-24 14:53:09", "stars": 73}, "https://github.com/city96/ComfyUI-GGUF": {"author_account_age_days": 865, "last_update": "2025-06-14 23:07:45", "stars": 2103}, "https://github.com/city96/ComfyUI_ColorMod": {"author_account_age_days": 865, "last_update": "2024-08-06 22:38:54", "stars": 94}, "https://github.com/city96/ComfyUI_DiT": {"author_account_age_days": 865, "last_update": "2024-08-06 22:44:33", "stars": 5}, "https://github.com/city96/ComfyUI_ExtraModels": {"author_account_age_days": 865, "last_update": "2024-12-17 06:44:05", "stars": 509}, "https://github.com/city96/ComfyUI_NetDist": {"author_account_age_days": 865, "last_update": "2024-05-22 18:05:10", "stars": 455}, "https://github.com/city96/SD-Latent-Interposer": {"author_account_age_days": 865, "last_update": "2024-08-06 22:01:47", "stars": 295}, "https://github.com/city96/SD-Latent-Upscaler": {"author_account_age_days": 865, "last_update": "2024-05-22 18:05:50", "stars": 159}, "https://github.com/civen-cn/ComfyUI-PaddleOcr": {"author_account_age_days": 2872, "last_update": "2024-12-31 19:11:04", "stars": 7}, "https://github.com/civen-cn/ComfyUI-Whisper-Translator": {"author_account_age_days": 2872, "last_update": "2025-01-04 03:37:06", "stars": 6}, "https://github.com/civitai/civitai_comfy_nodes": {"author_account_age_days": 966, "last_update": "2024-08-25 03:32:49", "stars": 146}, "https://github.com/claussteinmassl/ComfyUI-CS-CustomNodes": {"author_account_age_days": 3068, "last_update": "2024-06-14 09:03:10", "stars": 1}, "https://github.com/cleanlii/comfyui-dalle-integration": {"author_account_age_days": 2470, "last_update": "2025-04-02 08:29:56", "stars": 1}, "https://github.com/clhui/ComfyUi-clh-Tool": {"author_account_age_days": 3196, "last_update": "2024-12-28 10:22:00", "stars": 6}, "https://github.com/clouddreamfly/ComfyUI-PromptWrapper": {"author_account_age_days": 1960, "last_update": "2025-06-02 16:16:12", "stars": 2}, "https://github.com/cloudkoala/comfyui-koala": {"author_account_age_days": 280, "last_update": "2025-06-06 00:17:19", "stars": 0}, "https://github.com/cluny85/ComfyUI-Scripting-Tools": {"author_account_age_days": 4913, "last_update": "2025-06-16 12:28:27", "stars": 0}, "https://github.com/cmdicely/simple_image_to_palette": {"author_account_age_days": 5851, "last_update": "2025-06-22 03:20:08", "stars": 0}, "https://github.com/cnbjjj/ComfyUI-Jtils": {"author_account_age_days": 542, "last_update": "2025-05-10 23:25:19", "stars": 3}, "https://github.com/codecringebinge/ComfyUI-Arrow-Key-Canvas-Navigation": {"author_account_age_days": 3166, "last_update": "2024-09-29 22:35:01", "stars": 2}, "https://github.com/codeprimate/ComfyUI-MaskContourProcessor": {"author_account_age_days": 6216, "last_update": "2024-12-16 06:53:08", "stars": 2}, "https://github.com/comfy-deploy/comfyui-llm-toolkit": {"author_account_age_days": 534, "last_update": "2025-06-18 01:48:53", "stars": 19}, "https://github.com/comfyanonymous/ComfyUI": {"author_account_age_days": 916, "last_update": "2025-06-26 07:39:12", "stars": 80796}, "https://github.com/comfyanonymous/ComfyUI_TensorRT": {"author_account_age_days": 916, "last_update": "2024-10-10 00:23:55", "stars": 622}, "https://github.com/comfyanonymous/ComfyUI_experiments": {"author_account_age_days": 916, "last_update": "2024-05-22 15:29:49", "stars": 183}, "https://github.com/concarne000/ConCarneNode": {"author_account_age_days": 2268, "last_update": "2024-05-22 22:10:18", "stars": 4}, "https://github.com/conquestace/ComfyUI-ImageUploader": {"author_account_age_days": 4977, "last_update": "2024-05-23 01:25:49", "stars": 2}, "https://github.com/coolzilj/ComfyUI-LJNodes": {"author_account_age_days": 5030, "last_update": "2024-06-15 01:57:32", "stars": 87}, "https://github.com/coolzilj/ComfyUI-Photopea": {"author_account_age_days": 5030, "last_update": "2024-06-14 08:10:57", "stars": 148}, "https://github.com/coreyryanhanson/ComfyQR": {"author_account_age_days": 3423, "last_update": "2025-01-26 16:25:19", "stars": 77}, "https://github.com/coreyryanhanson/ComfyQR-scanning-nodes": {"author_account_age_days": 3423, "last_update": "2025-01-26 16:26:36", "stars": 11}, "https://github.com/coulterj/comfyui-svg-visual-normalize": {"author_account_age_days": 3308, "last_update": "2025-05-26 11:45:44", "stars": 0}, "https://github.com/cozy-comfyui/cozy_comm": {"author_account_age_days": 433, "last_update": "2025-04-03 17:02:54", "stars": 2}, "https://github.com/cozymantis/cozy-utils-comfyui-nodes": {"author_account_age_days": 476, "last_update": "2025-04-07 09:53:31", "stars": 5}, "https://github.com/cozymantis/human-parser-comfyui-node": {"author_account_age_days": 476, "last_update": "2025-04-19 14:09:03", "stars": 106}, "https://github.com/cozymantis/pose-generator-comfyui-node": {"author_account_age_days": 476, "last_update": "2025-04-07 09:53:17", "stars": 82}, "https://github.com/cr7Por/ComfyUI_DepthFlow": {"author_account_age_days": 1493, "last_update": "2024-09-16 09:10:08", "stars": 5}, "https://github.com/craig-tanaka/comfyui_animeseg": {"author_account_age_days": 2976, "last_update": "2025-05-20 18:59:45", "stars": 0}, "https://github.com/crave33/RenesStuffDanbooruTagGet": {"author_account_age_days": 134, "last_update": "2025-02-23 15:48:48", "stars": 0}, "https://github.com/crystian/ComfyUI-Crystools": {"author_account_age_days": 4485, "last_update": "2025-06-26 18:52:04", "stars": 1243}, "https://github.com/crystian/ComfyUI-Crystools-save": {"author_account_age_days": 4485, "last_update": "2025-06-01 20:17:39", "stars": 45}, "https://github.com/cubiq/Block_Patcher_ComfyUI": {"author_account_age_days": 5378, "last_update": "2024-09-22 09:49:06", "stars": 83}, "https://github.com/cubiq/ComfyUI_FaceAnalysis": {"author_account_age_days": 5378, "last_update": "2025-05-20 05:18:36", "stars": 450}, "https://github.com/cubiq/ComfyUI_IPAdapter_plus": {"author_account_age_days": 5378, "last_update": "2025-04-14 07:29:17", "stars": 5252}, "https://github.com/cubiq/ComfyUI_InstantID": {"author_account_age_days": 5378, "last_update": "2025-04-14 07:50:01", "stars": 1647}, "https://github.com/cubiq/ComfyUI_essentials": {"author_account_age_days": 5378, "last_update": "2025-04-14 07:33:29", "stars": 875}, "https://github.com/cubiq/PuLID_ComfyUI": {"author_account_age_days": 5378, "last_update": "2025-04-14 07:47:23", "stars": 869}, "https://github.com/cuongloveit/comfy_http_request": {"author_account_age_days": 3620, "last_update": "2024-06-14 11:00:11", "stars": 5}, "https://github.com/curiousjp/ComfyUI-MaskBatchPermutations": {"author_account_age_days": 2297, "last_update": "2024-05-28 13:09:32", "stars": 5}, "https://github.com/cyberhirsch/seb_nodes": {"author_account_age_days": 2234, "last_update": "2025-06-12 18:30:24", "stars": 0}, "https://github.com/czcz1024/Comfyui-FaceCompare": {"author_account_age_days": 4601, "last_update": "2024-06-14 07:13:32", "stars": 0}, "https://github.com/da2el-ai/ComfyUI-d2-send-eagle": {"author_account_age_days": 757, "last_update": "2025-03-10 14:31:22", "stars": 16}, "https://github.com/da2el-ai/ComfyUI-d2-size-selector": {"author_account_age_days": 757, "last_update": "2024-10-02 14:04:20", "stars": 4}, "https://github.com/da2el-ai/ComfyUI-d2-steps": {"author_account_age_days": 757, "last_update": "2024-10-02 14:03:14", "stars": 5}, "https://github.com/da2el-ai/ComfyUI-d2-xyplot-utils": {"author_account_age_days": 757, "last_update": "2024-10-02 14:00:58", "stars": 5}, "https://github.com/da2el-ai/D2-PromptSelector-comfyUI": {"author_account_age_days": 757, "last_update": "2025-04-05 03:00:34", "stars": 3}, "https://github.com/da2el-ai/D2-SavePSD-ComfyUI": {"author_account_age_days": 757, "last_update": "2025-04-08 15:28:06", "stars": 2}, "https://github.com/da2el-ai/D2-nodes-ComfyUI": {"author_account_age_days": 757, "last_update": "2025-06-21 17:06:13", "stars": 35}, "https://github.com/dadoirie/ComfyUI_Dados_Nodes": {"author_account_age_days": 1957, "last_update": "2025-06-09 10:35:26", "stars": 1}, "https://github.com/dafeng012/comfyui-imgmake": {"author_account_age_days": 1043, "last_update": "2024-11-03 17:38:47", "stars": 14}, "https://github.com/dagthomas/comfyui_dagthomas": {"author_account_age_days": 4439, "last_update": "2025-04-23 14:00:14", "stars": 253}, "https://github.com/danger-electrodes/ComfyUI_Fawfluencer_Nodes": {"author_account_age_days": 758, "last_update": "2025-04-15 10:31:38", "stars": 0}, "https://github.com/daniabib/ComfyUI_ProPainter_Nodes": {"author_account_age_days": 2772, "last_update": "2024-12-22 13:50:25", "stars": 316}, "https://github.com/daniel-lewis-ab/ComfyUI-Llama": {"author_account_age_days": 3719, "last_update": "2024-06-29 19:55:42", "stars": 59}, "https://github.com/daniel-lewis-ab/ComfyUI-TTS": {"author_account_age_days": 3719, "last_update": "2024-06-14 08:09:49", "stars": 28}, "https://github.com/darkpixel/darkprompts": {"author_account_age_days": 5691, "last_update": "2025-06-09 16:39:38", "stars": 8}, "https://github.com/darth-veitcher/comfydv": {"author_account_age_days": 4796, "last_update": "2025-05-13 07:24:56", "stars": 1}, "https://github.com/daryltucker/ComfyUI-LoadFiles": {"author_account_age_days": 4832, "last_update": "2024-08-31 23:59:44", "stars": 2}, "https://github.com/dasilva333/ComfyUI_ContrastingColor": {"author_account_age_days": 5081, "last_update": "2025-02-22 04:49:59", "stars": 1}, "https://github.com/dasilva333/ComfyUI_MarkdownImage": {"author_account_age_days": 5081, "last_update": "2025-04-12 03:11:13", "stars": 0}, "https://github.com/dave-palt/comfyui_DSP_imagehelpers": {"author_account_age_days": 525, "last_update": "2024-05-22 23:12:11", "stars": 0}, "https://github.com/davidgressett/comfyui-systemlevel": {"author_account_age_days": 2998, "last_update": "2025-01-22 23:51:40", "stars": 0}, "https://github.com/daxcay/ComfyUI-DataSet": {"author_account_age_days": 462, "last_update": "2025-03-01 05:24:50", "stars": 50}, "https://github.com/daxcay/ComfyUI-JDCN": {"author_account_age_days": 462, "last_update": "2025-04-14 09:20:22", "stars": 120}, "https://github.com/daxcay/ComfyUI-NODEJS": {"author_account_age_days": 462, "last_update": "2024-11-28 09:46:29", "stars": 14}, "https://github.com/daxcay/ComfyUI-Nexus": {"author_account_age_days": 462, "last_update": "2025-03-01 15:40:05", "stars": 86}, "https://github.com/daxcay/ComfyUI-TG": {"author_account_age_days": 462, "last_update": "2024-11-28 09:45:12", "stars": 19}, "https://github.com/daxcay/ComfyUI-WA": {"author_account_age_days": 462, "last_update": "2024-11-28 09:44:50", "stars": 48}, "https://github.com/daxcay/ComfyUI-YouTubeVideoPlayer": {"author_account_age_days": 462, "last_update": "2024-11-28 09:45:30", "stars": 5}, "https://github.com/dchatel/comfyui_davcha": {"author_account_age_days": 4903, "last_update": "2025-04-03 06:39:42", "stars": 1}, "https://github.com/dchatel/comfyui_facetools": {"author_account_age_days": 4903, "last_update": "2025-05-18 12:30:50", "stars": 140}, "https://github.com/denfrost/Den_ComfyUI_Workflow": {"author_account_age_days": 3864, "last_update": "2025-05-07 07:15:01", "stars": 4}, "https://github.com/deroberon/StableZero123-comfyui": {"author_account_age_days": 5663, "last_update": "2024-05-22 22:09:53", "stars": 172}, "https://github.com/deroberon/demofusion-comfyui": {"author_account_age_days": 5663, "last_update": "2024-05-22 22:09:42", "stars": 88}, "https://github.com/dfghsdh/ComfyUI_FluxPromptGen": {"author_account_age_days": 277, "last_update": "2024-09-23 07:51:56", "stars": 14}, "https://github.com/dfl/comfyui-clip-with-break": {"author_account_age_days": 6342, "last_update": "2025-03-04 20:16:06", "stars": 13}, "https://github.com/dfl/comfyui-tcd-scheduler": {"author_account_age_days": 6342, "last_update": "2024-05-22 23:23:28", "stars": 83}, "https://github.com/diStyApps/ComfyUI-disty-Flow": {"author_account_age_days": 4572, "last_update": "2025-01-04 18:03:37", "stars": 547}, "https://github.com/diStyApps/ComfyUI_FrameMaker": {"author_account_age_days": 4572, "last_update": "2024-05-23 00:11:33", "stars": 22}, "https://github.com/dicksensei69/comfyui_loops": {"author_account_age_days": 1002, "last_update": "2025-05-03 22:22:55", "stars": 0}, "https://github.com/dicksondickson/ComfyUI-Dickson-Nodes": {"author_account_age_days": 4358, "last_update": "2024-09-18 04:30:33", "stars": 10}, "https://github.com/digitaljohn/comfyui-propost": {"author_account_age_days": 4885, "last_update": "2025-02-10 23:25:24", "stars": 179}, "https://github.com/dimtion/comfyui-raw-image": {"author_account_age_days": 4732, "last_update": "2025-03-31 00:25:41", "stars": 1}, "https://github.com/dimtoneff/ComfyUI-PixelArt-Detector": {"author_account_age_days": 3782, "last_update": "2025-04-01 15:43:07", "stars": 306}, "https://github.com/dionren/ComfyUI-Pro-Export-Tool": {"author_account_age_days": 4253, "last_update": "2024-10-11 08:26:18", "stars": 2}, "https://github.com/diontimmer/ComfyUI-Vextra-Nodes": {"author_account_age_days": 5138, "last_update": "2024-06-20 16:48:44", "stars": 77}, "https://github.com/discopixel-studio/comfyui-discopixel": {"author_account_age_days": 713, "last_update": "2024-09-30 00:46:13", "stars": 12}, "https://github.com/discus0434/comfyui-aesthetic-predictor-v2-5": {"author_account_age_days": 1837, "last_update": "2024-06-14 08:12:05", "stars": 13}, "https://github.com/discus0434/comfyui-caching-embeddings": {"author_account_age_days": 1837, "last_update": "2024-06-14 08:59:36", "stars": 2}, "https://github.com/discus0434/comfyui-flux-accelerator": {"author_account_age_days": 1837, "last_update": "2024-12-19 14:39:39", "stars": 136}, "https://github.com/djbielejeski/a-person-mask-generator": {"author_account_age_days": 4651, "last_update": "2025-03-14 11:19:45", "stars": 355}, "https://github.com/dmMaze/sketch2manga": {"author_account_age_days": 2217, "last_update": "2025-03-31 08:51:09", "stars": 41}, "https://github.com/dmarx/ComfyUI-AudioReactive": {"author_account_age_days": 4872, "last_update": "2024-05-22 22:12:53", "stars": 10}, "https://github.com/dmarx/ComfyUI-Keyframed": {"author_account_age_days": 4872, "last_update": "2024-07-01 01:41:23", "stars": 87}, "https://github.com/domenecmiralles/obobo_nodes": {"author_account_age_days": 974, "last_update": "2025-06-12 22:44:13", "stars": 0}, "https://github.com/dorpxam/ComfyUI-FramePack-F1-T2V": {"author_account_age_days": 627, "last_update": "2025-05-29 06:33:54", "stars": 2}, "https://github.com/dorpxam/ComfyUI-LTXVideoLoRA": {"author_account_age_days": 627, "last_update": "2025-05-10 16:42:44", "stars": 16}, "https://github.com/doubletwisted/ComfyUI-Deadline-Plugin": {"author_account_age_days": 957, "last_update": "2025-05-25 22:47:23", "stars": 4}, "https://github.com/drago87/ComfyUI_Dragos_Nodes": {"author_account_age_days": 4137, "last_update": "2024-05-22 21:32:15", "stars": 3}, "https://github.com/dreamhartley/ComfyUI_show_seed": {"author_account_age_days": 851, "last_update": "2025-01-14 16:15:12", "stars": 1}, "https://github.com/drmbt/comfyui-dreambait-nodes": {"author_account_age_days": 4145, "last_update": "2025-06-23 04:17:05", "stars": 4}, "https://github.com/drphero/comfyui_prompttester": {"author_account_age_days": 3579, "last_update": "2025-06-19 01:06:51", "stars": 1}, "https://github.com/drustan-hawk/primitive-types": {"author_account_age_days": 656, "last_update": "2024-08-01 17:44:51", "stars": 6}, "https://github.com/dseditor/ComfyUI-ListHelper": {"author_account_age_days": 1370, "last_update": "2025-06-22 10:21:59", "stars": 2}, "https://github.com/dseditor/ComfyUI-ScheduledTask": {"author_account_age_days": 1370, "last_update": "2025-06-19 16:07:41", "stars": 6}, "https://github.com/dseditor/ComfyUI-Thread": {"author_account_age_days": 1370, "last_update": "2025-06-17 02:38:00", "stars": 3}, "https://github.com/duchamps0305/comfyui-white-extractor": {"author_account_age_days": 989, "last_update": "2025-01-23 08:09:12", "stars": 0}, "https://github.com/ducido/ObjectFusion_ComfyUI_nodes": {"author_account_age_days": 897, "last_update": "2025-05-02 08:31:46", "stars": 1}, "https://github.com/dymokomi/comfyui_dygen": {"author_account_age_days": 940, "last_update": "2024-11-28 20:08:13", "stars": 1}, "https://github.com/dzqdzq/ComfyUI-crop-alpha": {"author_account_age_days": 3382, "last_update": "2025-02-17 14:46:11", "stars": 2}, "https://github.com/e-tier-newbie/ComfyUI-E-Tier-TextSaver": {"author_account_age_days": 48, "last_update": "2025-06-06 21:59:50", "stars": 0}, "https://github.com/e7mac/ComfyUI-ShadertoyGL": {"author_account_age_days": 5171, "last_update": "2024-06-20 14:52:42", "stars": 4}, "https://github.com/ealkanat/comfyui-easy-padding": {"author_account_age_days": 2830, "last_update": "2024-12-31 02:38:22", "stars": 17}, "https://github.com/eastoc/ComfyUI_SemanticSAM": {"author_account_age_days": 3085, "last_update": "2024-08-13 19:24:33", "stars": 4}, "https://github.com/edelvarden/ComfyUI-Display-Value": {"author_account_age_days": 2501, "last_update": "2025-05-25 23:02:40", "stars": 0}, "https://github.com/edelvarden/comfyui_image_metadata_extension": {"author_account_age_days": 2501, "last_update": "2025-06-19 18:38:45", "stars": 47}, "https://github.com/edenartlab/eden_comfy_pipelines": {"author_account_age_days": 640, "last_update": "2025-06-16 14:34:11", "stars": 91}, "https://github.com/edenartlab/sd-lora-trainer": {"author_account_age_days": 640, "last_update": "2025-02-24 16:18:16", "stars": 54}, "https://github.com/educator-art/ComfyUI-Load-DirectoryFiles": {"author_account_age_days": 569, "last_update": "2025-04-22 08:51:32", "stars": 3}, "https://github.com/emojiiii/ComfyUI_Emojiiii_Custom_Nodes": {"author_account_age_days": 2890, "last_update": "2024-09-03 06:55:04", "stars": 0}, "https://github.com/envy-ai/ComfyUI-ConDelta": {"author_account_age_days": 323, "last_update": "2025-04-24 00:16:02", "stars": 200}, "https://github.com/erosDiffusion/ComfyUI-enricos-nodes": {"author_account_age_days": 364, "last_update": "2025-05-05 22:53:42", "stars": 460}, "https://github.com/evanspearman/ComfyMath": {"author_account_age_days": 4606, "last_update": "2025-03-08 18:14:34", "stars": 128}, "https://github.com/excelwong/ComfyUI-PromptComposer": {"author_account_age_days": 3733, "last_update": "2025-04-30 10:33:43", "stars": 0}, "https://github.com/exdysa/comfyui-selector": {"author_account_age_days": 1363, "last_update": "2025-03-14 12:21:29", "stars": 4}, "https://github.com/exectails/comfyui-et_dynamicprompts": {"author_account_age_days": 4285, "last_update": "2024-11-29 22:37:19", "stars": 4}, "https://github.com/exectails/comfyui-et_infoutils": {"author_account_age_days": 4285, "last_update": "2024-11-29 17:27:49", "stars": 2}, "https://github.com/exectails/comfyui-et_stringutils": {"author_account_age_days": 4285, "last_update": "2024-11-26 20:26:14", "stars": 1}, "https://github.com/ez-af/ComfyUI-EZ-AF-Nodes": {"author_account_age_days": 302, "last_update": "2025-06-25 06:54:14", "stars": 12}, "https://github.com/fablestudio/ComfyUI-Showrunner-Utils": {"author_account_age_days": 2416, "last_update": "2025-06-04 04:34:09", "stars": 0}, "https://github.com/facok/ComfyUI-HunyuanVideoMultiLora": {"author_account_age_days": 826, "last_update": "2025-05-13 18:35:00", "stars": 115}, "https://github.com/facok/ComfyUI-TeaCacheHunyuanVideo": {"author_account_age_days": 826, "last_update": "2025-04-05 05:24:59", "stars": 92}, "https://github.com/fairy-root/ComfyUI-GLHF": {"author_account_age_days": 2302, "last_update": "2025-04-03 13:47:20", "stars": 4}, "https://github.com/fairy-root/ComfyUI-OpenAI-FM": {"author_account_age_days": 2302, "last_update": "2025-05-09 00:12:06", "stars": 30}, "https://github.com/fairy-root/ComfyUI-Show-Text": {"author_account_age_days": 2302, "last_update": "2025-04-08 14:21:57", "stars": 10}, "https://github.com/fairy-root/Flux-Prompt-Generator": {"author_account_age_days": 2302, "last_update": "2025-04-22 02:20:47", "stars": 208}, "https://github.com/fairy-root/comfyui-ollama-llms": {"author_account_age_days": 2302, "last_update": "2025-03-27 20:47:17", "stars": 17}, "https://github.com/fallingmeteorite/nsfw-image-check-comfyui": {"author_account_age_days": 1456, "last_update": "2025-06-02 03:59:25", "stars": 7}, "https://github.com/fashn-AI/ComfyUI-FASHN": {"author_account_age_days": 723, "last_update": "2025-04-23 10:15:13", "stars": 25}, "https://github.com/fat-tire/comfyui-unified-media-suite": {"author_account_age_days": 5304, "last_update": "2025-02-25 04:41:02", "stars": 4}, "https://github.com/fblissjr/ComfyUI-DatasetHelper": {"author_account_age_days": 3732, "last_update": "2025-01-27 18:58:33", "stars": 6}, "https://github.com/fblissjr/ComfyUI-EmbeddingPipelineAnalytics": {"author_account_age_days": 3732, "last_update": "2025-01-24 18:51:53", "stars": 2}, "https://github.com/fblissjr/ComfyUI-WanSeamlessFlow": {"author_account_age_days": 3732, "last_update": "2025-03-17 22:36:22", "stars": 4}, "https://github.com/fearnworks/ComfyUI_FearnworksNodes": {"author_account_age_days": 929, "last_update": "2024-08-05 01:50:04", "stars": 19}, "https://github.com/feixuetuba/Spleeter": {"author_account_age_days": 4330, "last_update": "2025-01-19 10:39:17", "stars": 0}, "https://github.com/felixszeto/ComfyUI-RequestNodes": {"author_account_age_days": 1417, "last_update": "2025-04-19 18:59:35", "stars": 86}, "https://github.com/fexli/fexli-util-node-comfyui": {"author_account_age_days": 1926, "last_update": "2025-06-18 06:07:31", "stars": 3}, "https://github.com/fexploit/ComfyUI-AutoLabel": {"author_account_age_days": 5411, "last_update": "2025-03-18 13:07:46", "stars": 7}, "https://github.com/fexploit/ComfyUI-AutoTrimBG": {"author_account_age_days": 5411, "last_update": "2025-03-10 12:59:42", "stars": 3}, "https://github.com/fexploit/ComfyUI-Classifier": {"author_account_age_days": 5411, "last_update": "2025-03-10 20:33:42", "stars": 1}, "https://github.com/filipemeneses/comfy_pixelization": {"author_account_age_days": 3844, "last_update": "2025-06-12 07:07:01", "stars": 63}, "https://github.com/filliptm/ComfyUI_FL-Trainer": {"author_account_age_days": 2104, "last_update": "2024-10-18 00:20:18", "stars": 166}, "https://github.com/filliptm/ComfyUI_Fill-ChatterBox": {"author_account_age_days": 2104, "last_update": "2025-06-25 01:45:29", "stars": 132}, "https://github.com/filliptm/ComfyUI_Fill-Nodes": {"author_account_age_days": 2104, "last_update": "2025-06-25 16:39:20", "stars": 413}, "https://github.com/finegrain-ai/comfyui-finegrain": {"author_account_age_days": 870, "last_update": "2025-05-16 07:44:57", "stars": 12}, "https://github.com/flamacore/ComfyUI-YouTubeUploader": {"author_account_age_days": 3676, "last_update": "2025-06-14 22:11:33", "stars": 1}, "https://github.com/florestefano1975/ComfyUI-Advanced-Sequence-Seed": {"author_account_age_days": 561, "last_update": "2025-04-09 12:40:05", "stars": 1}, "https://github.com/florestefano1975/ComfyUI-CogVideoX": {"author_account_age_days": 561, "last_update": "2025-04-09 12:39:53", "stars": 15}, "https://github.com/florestefano1975/ComfyUI-HiDiffusion": {"author_account_age_days": 561, "last_update": "2025-04-09 12:40:58", "stars": 143}, "https://github.com/florestefano1975/ComfyUI-StabilityAI-Suite": {"author_account_age_days": 561, "last_update": "2025-04-09 12:40:36", "stars": 4}, "https://github.com/florestefano1975/comfyui-portrait-master": {"author_account_age_days": 561, "last_update": "2025-06-24 09:25:13", "stars": 1078}, "https://github.com/florestefano1975/comfyui-prompt-composer": {"author_account_age_days": 561, "last_update": "2025-04-27 15:00:00", "stars": 275}, "https://github.com/flowtyone/ComfyUI-Flowty-CRM": {"author_account_age_days": 641, "last_update": "2024-06-14 10:23:09", "stars": 155}, "https://github.com/flowtyone/ComfyUI-Flowty-LDSR": {"author_account_age_days": 641, "last_update": "2024-06-14 09:04:51", "stars": 242}, "https://github.com/flowtyone/ComfyUI-Flowty-TripoSR": {"author_account_age_days": 641, "last_update": "2024-06-16 00:53:22", "stars": 508}, "https://github.com/fluffydiveX/ComfyUI-hvBlockswap": {"author_account_age_days": 200, "last_update": "2025-03-30 03:30:40", "stars": 8}, "https://github.com/flycarl/ComfyUI-Pixelate": {"author_account_age_days": 5227, "last_update": "2024-11-26 13:31:56", "stars": 1}, "https://github.com/flyingshutter/As_ComfyUI_CustomNodes": {"author_account_age_days": 3863, "last_update": "2025-05-23 17:29:13", "stars": 7}, "https://github.com/fmatray/ComfyUI_BattlemapGrid": {"author_account_age_days": 3989, "last_update": "2024-06-05 22:35:06", "stars": 0}, "https://github.com/fofr/ComfyUI-HyperSDXL1StepUnetScheduler": {"author_account_age_days": 5475, "last_update": "2024-06-20 11:51:50", "stars": 11}, "https://github.com/fofr/ComfyUI-Prompter-fofrAI": {"author_account_age_days": 5475, "last_update": "2025-02-10 16:39:49", "stars": 74}, "https://github.com/fofr/comfyui-basic-auth": {"author_account_age_days": 5475, "last_update": "2025-03-17 09:38:05", "stars": 1}, "https://github.com/fofr/comfyui-fofr-toolkit": {"author_account_age_days": 5475, "last_update": "2024-08-09 11:36:38", "stars": 4}, "https://github.com/forever22777/comfyui-self-guidance": {"author_account_age_days": 692, "last_update": "2025-04-17 08:13:40", "stars": 10}, "https://github.com/fotobudka-team/comfyui-ai-faces": {"author_account_age_days": 223, "last_update": "2025-06-25 19:54:59", "stars": 1}, "https://github.com/foxtrot-roger/comfyui-rf-nodes": {"author_account_age_days": 2688, "last_update": "2024-08-13 22:01:40", "stars": 2}, "https://github.com/fpgaminer/joycaption_comfyui": {"author_account_age_days": 4837, "last_update": "2025-05-15 23:30:13", "stars": 76}, "https://github.com/fplu/comfyui_lama_with_refiner": {"author_account_age_days": 2450, "last_update": "2025-06-22 16:38:15", "stars": 1}, "https://github.com/frankchieng/ComfyUI_Aniportrait": {"author_account_age_days": 808, "last_update": "2024-09-13 10:41:16", "stars": 55}, "https://github.com/frankchieng/ComfyUI_MagicClothing": {"author_account_age_days": 808, "last_update": "2024-09-04 04:57:15", "stars": 573}, "https://github.com/frankchieng/ComfyUI_llm_easyanimiate": {"author_account_age_days": 808, "last_update": "2024-06-26 03:13:32", "stars": 12}, "https://github.com/fredconex/ComfyUI-SongBloom": {"author_account_age_days": 1069, "last_update": "2025-06-25 15:00:17", "stars": 3}, "https://github.com/fredconex/ComfyUI-SoundFlow": {"author_account_age_days": 1069, "last_update": "2025-06-16 14:18:04", "stars": 39}, "https://github.com/fredconex/ComfyUI-SyncEdit": {"author_account_age_days": 1069, "last_update": "2025-06-16 21:52:36", "stars": 0}, "https://github.com/freelifehacker/ComfyUI-ImgMask2PNG": {"author_account_age_days": 2526, "last_update": "2024-08-28 08:32:23", "stars": 0}, "https://github.com/fsdymy1024/ComfyUI_fsdymy": {"author_account_age_days": 2573, "last_update": "2024-07-01 17:58:52", "stars": 9}, "https://github.com/fssorc/ComfyUI_FFT": {"author_account_age_days": 4954, "last_update": "2024-09-30 01:27:21", "stars": 13}, "https://github.com/fssorc/ComfyUI_FaceShaper": {"author_account_age_days": 4954, "last_update": "2024-09-20 06:15:46", "stars": 169}, "https://github.com/fssorc/ComfyUI_RopeWrapper": {"author_account_age_days": 4954, "last_update": "2025-01-07 04:55:59", "stars": 16}, "https://github.com/fssorc/ComfyUI_pose_inter": {"author_account_age_days": 4954, "last_update": "2025-05-27 07:05:00", "stars": 79}, "https://github.com/fuselayer/comfyui-mosaic-blur": {"author_account_age_days": 645, "last_update": "2025-04-05 00:57:07", "stars": 1}, "https://github.com/gabe-init/ComfyUI-11labs": {"author_account_age_days": 31, "last_update": "2025-05-27 00:27:28", "stars": 2}, "https://github.com/gabe-init/ComfyUI-Google-Image-Search": {"author_account_age_days": 31, "last_update": "2025-05-27 00:54:00", "stars": 1}, "https://github.com/gabe-init/ComfyUI-Openrouter_node": {"author_account_age_days": 31, "last_update": "2025-06-13 01:42:28", "stars": 4}, "https://github.com/gabe-init/ComfyUI-String-Similarity": {"author_account_age_days": 31, "last_update": "2025-05-27 00:59:21", "stars": 0}, "https://github.com/game4d/ComfyUI-BDsInfiniteYou": {"author_account_age_days": 4071, "last_update": "2025-04-01 03:12:04", "stars": 7}, "https://github.com/gasparuff/CustomSelector": {"author_account_age_days": 4361, "last_update": "2025-05-09 06:17:31", "stars": 1}, "https://github.com/gelasdev/ComfyUI-FLUX-BFL-API": {"author_account_age_days": 2348, "last_update": "2025-06-08 01:01:01", "stars": 43}, "https://github.com/gemell1/ComfyUI_GMIC": {"author_account_age_days": 2324, "last_update": "2024-05-22 21:28:51", "stars": 8}, "https://github.com/geocine/geocine-comfyui": {"author_account_age_days": 5320, "last_update": "2025-03-08 15:46:56", "stars": 0}, "https://github.com/ggarra13/ComfyUI-mrv2": {"author_account_age_days": 4226, "last_update": "2025-03-27 17:24:38", "stars": 4}, "https://github.com/giriss/comfy-image-saver": {"author_account_age_days": 4605, "last_update": "2024-05-22 20:40:55", "stars": 279}, "https://github.com/gisu/comfyui-foxpack": {"author_account_age_days": 5366, "last_update": "2024-08-20 06:43:22", "stars": 2}, "https://github.com/gitadmini/comfyui_extractstoryboards": {"author_account_age_days": 3409, "last_update": "2025-06-11 02:01:24", "stars": 1}, "https://github.com/githubYiheng/ComfyUI_Change_IMAGE_BOREDER": {"author_account_age_days": 4270, "last_update": "2024-05-23 01:20:09", "stars": 0}, "https://github.com/githubYiheng/ComfyUI_GetFileNameFromURL": {"author_account_age_days": 4270, "last_update": "2024-05-23 01:19:47", "stars": 1}, "https://github.com/githubYiheng/comfyui_kmeans_filter": {"author_account_age_days": 4270, "last_update": "2024-06-14 09:01:24", "stars": 0}, "https://github.com/githubYiheng/comfyui_meanshift_filter": {"author_account_age_days": 4270, "last_update": "2024-06-14 10:59:43", "stars": 0}, "https://github.com/githubYiheng/comfyui_private_postprocessor": {"author_account_age_days": 4270, "last_update": "2024-06-14 08:09:39", "stars": 1}, "https://github.com/gitmylo/ComfyUI-audio-nodes": {"author_account_age_days": 2675, "last_update": "2025-04-07 07:24:06", "stars": 9}, "https://github.com/glibsonoran/Plush-for-ComfyUI": {"author_account_age_days": 2865, "last_update": "2025-06-21 21:12:31", "stars": 175}, "https://github.com/glifxyz/ComfyUI-GlifNodes": {"author_account_age_days": 941, "last_update": "2024-11-25 12:37:14", "stars": 55}, "https://github.com/glowcone/comfyui-base64-to-image": {"author_account_age_days": 4113, "last_update": "2024-07-08 22:53:25", "stars": 16}, "https://github.com/glowcone/comfyui-string-converter": {"author_account_age_days": 4113, "last_update": "2024-07-31 13:40:48", "stars": 1}, "https://github.com/gmorks/ComfyUI-SendToDiscord": {"author_account_age_days": 2662, "last_update": "2025-01-29 08:10:54", "stars": 0}, "https://github.com/goburiin/nsfwrecog-comfyui": {"author_account_age_days": 321, "last_update": "2024-08-14 02:17:15", "stars": 0}, "https://github.com/godmt/ComfyUI-IP-Composer": {"author_account_age_days": 2097, "last_update": "2025-05-18 09:52:01", "stars": 5}, "https://github.com/godmt/ComfyUI-List-Utils": {"author_account_age_days": 2097, "last_update": "2025-05-26 21:41:06", "stars": 8}, "https://github.com/godspede/ComfyUI_Substring": {"author_account_age_days": 3481, "last_update": "2025-03-27 15:33:12", "stars": 0}, "https://github.com/gokayfem/ComfyUI-Depth-Visualization": {"author_account_age_days": 1425, "last_update": "2024-10-31 23:50:57", "stars": 64}, "https://github.com/gokayfem/ComfyUI-Dream-Interpreter": {"author_account_age_days": 1425, "last_update": "2024-07-31 16:11:04", "stars": 79}, "https://github.com/gokayfem/ComfyUI-Texture-Simple": {"author_account_age_days": 1425, "last_update": "2024-07-31 16:14:23", "stars": 51}, "https://github.com/gokayfem/ComfyUI-fal-API": {"author_account_age_days": 1425, "last_update": "2025-06-20 10:38:44", "stars": 135}, "https://github.com/gokayfem/ComfyUI_VLM_nodes": {"author_account_age_days": 1425, "last_update": "2025-02-13 10:37:34", "stars": 498}, "https://github.com/goldwins520/Comfyui_saveimg2webdav": {"author_account_age_days": 1943, "last_update": "2025-05-25 06:15:38", "stars": 0}, "https://github.com/gonzalu/ComfyUI_YFG_Comical": {"author_account_age_days": 2829, "last_update": "2025-05-03 20:30:02", "stars": 25}, "https://github.com/googincheng/ComfyUX": {"author_account_age_days": 3156, "last_update": "2024-08-22 09:47:17", "stars": 148}, "https://github.com/gorillaframeai/GF_nodes": {"author_account_age_days": 601, "last_update": "2025-04-19 15:49:54", "stars": 27}, "https://github.com/gorillaframeai/GF_translate": {"author_account_age_days": 601, "last_update": "2025-02-04 19:26:53", "stars": 4}, "https://github.com/greengerong/ComfyUI-JanusPro-PL": {"author_account_age_days": 4636, "last_update": "2025-02-08 03:32:59", "stars": 11}, "https://github.com/greengerong/ComfyUI-Lumina-Video": {"author_account_age_days": 4636, "last_update": "2025-02-23 03:01:18", "stars": 7}, "https://github.com/gremlation/ComfyUI-ImageLabel": {"author_account_age_days": 190, "last_update": "2025-04-03 09:49:57", "stars": 4}, "https://github.com/gremlation/ComfyUI-JMESPath": {"author_account_age_days": 190, "last_update": "2025-04-03 09:50:11", "stars": 1}, "https://github.com/gremlation/ComfyUI-TrackAndWheel": {"author_account_age_days": 190, "last_update": "2025-04-03 09:50:20", "stars": 2}, "https://github.com/gremlation/ComfyUI-ViewData": {"author_account_age_days": 190, "last_update": "2025-04-03 09:50:28", "stars": 1}, "https://github.com/gremlation/ComfyUI-jq": {"author_account_age_days": 190, "last_update": "2025-04-03 09:50:39", "stars": 1}, "https://github.com/griptape-ai/ComfyUI-Griptape": {"author_account_age_days": 888, "last_update": "2025-05-29 03:24:58", "stars": 194}, "https://github.com/gseth/ControlAltAI-Nodes": {"author_account_age_days": 4214, "last_update": "2025-06-05 04:21:56", "stars": 132}, "https://github.com/gt732/ComfyUI-DreamWaltz-G": {"author_account_age_days": 1523, "last_update": "2024-10-27 03:15:13", "stars": 2}, "https://github.com/guerreiro/comfyg-switch": {"author_account_age_days": 5408, "last_update": "2025-06-22 17:35:28", "stars": 3}, "https://github.com/guill/abracadabra-comfyui": {"author_account_age_days": 4560, "last_update": "2024-12-23 09:46:10", "stars": 1}, "https://github.com/guyaton/guy-nodes-comfyui": {"author_account_age_days": 269, "last_update": "2024-10-02 13:15:26", "stars": 0}, "https://github.com/hackkhai/ComfyUI-Image-Matting": {"author_account_age_days": 2218, "last_update": "2024-07-31 15:02:56", "stars": 18}, "https://github.com/hanoixan/ComfyUI-DataBeast": {"author_account_age_days": 5204, "last_update": "2024-11-05 17:47:30", "stars": 2}, "https://github.com/haohaocreates/ComfyUI-HH-Image-Selector": {"author_account_age_days": 473, "last_update": "2024-07-28 21:08:27", "stars": 0}, "https://github.com/hassan-sd/comfyui-image-prompt-loader": {"author_account_age_days": 937, "last_update": "2025-06-11 21:10:47", "stars": 2}, "https://github.com/havvk/ComfyUI_AIIA": {"author_account_age_days": 4005, "last_update": "2025-06-18 06:41:23", "stars": 7}, "https://github.com/hay86/ComfyUI_DDColor": {"author_account_age_days": 5034, "last_update": "2024-06-14 08:12:13", "stars": 7}, "https://github.com/hay86/ComfyUI_Dreamtalk": {"author_account_age_days": 5034, "last_update": "2024-08-15 03:37:37", "stars": 12}, "https://github.com/hay86/ComfyUI_Hallo": {"author_account_age_days": 5034, "last_update": "2024-07-30 09:55:03", "stars": 21}, "https://github.com/hay86/ComfyUI_LatentSync": {"author_account_age_days": 5034, "last_update": "2025-01-06 07:47:40", "stars": 16}, "https://github.com/hay86/ComfyUI_MiniCPM-V": {"author_account_age_days": 5034, "last_update": "2024-08-09 07:52:59", "stars": 39}, "https://github.com/hay86/ComfyUI_OpenVoice": {"author_account_age_days": 5034, "last_update": "2024-07-02 08:16:20", "stars": 18}, "https://github.com/hayd-zju/ICEdit-ComfyUI-official": {"author_account_age_days": 2285, "last_update": "2025-05-26 06:23:23", "stars": 191}, "https://github.com/hayde0096/Comfyui-EasySettingpipes": {"author_account_age_days": 3094, "last_update": "2025-05-31 03:51:08", "stars": 0}, "https://github.com/hayden-fr/ComfyUI-Model-Manager": {"author_account_age_days": 2305, "last_update": "2025-05-16 15:14:43", "stars": 131}, "https://github.com/hben35096/ComfyUI-ReplenishNodes": {"author_account_age_days": 715, "last_update": "2025-05-24 17:06:41", "stars": 6}, "https://github.com/hben35096/ComfyUI-ToolBox": {"author_account_age_days": 715, "last_update": "2024-09-02 14:49:43", "stars": 6}, "https://github.com/hekmon/comfyui-checkpoint-extract": {"author_account_age_days": 4531, "last_update": "2025-03-31 13:30:54", "stars": 0}, "https://github.com/hekmon/comfyui-openai-api": {"author_account_age_days": 4531, "last_update": "2025-04-08 09:40:51", "stars": 5}, "https://github.com/heshengtao/comfyui_LLM_party": {"author_account_age_days": 3262, "last_update": "2025-06-22 08:42:11", "stars": 1755}, "https://github.com/heshengtao/comfyui_LLM_schools": {"author_account_age_days": 3262, "last_update": "2024-08-24 15:08:14", "stars": 7}, "https://github.com/hexxacubic/ComfyUI-Prompt_Library": {"author_account_age_days": 34, "last_update": "2025-06-06 23:30:44", "stars": 0}, "https://github.com/hgabha/WWAA-CustomNodes": {"author_account_age_days": 523, "last_update": "2025-05-11 09:11:45", "stars": 22}, "https://github.com/hhhzzyang/Comfyui_Lama": {"author_account_age_days": 874, "last_update": "2024-05-22 21:13:19", "stars": 54}, "https://github.com/hieuck/ComfyUI-BiRefNet": {"author_account_age_days": 2892, "last_update": "2024-12-04 16:20:00", "stars": 1}, "https://github.com/hiforce/comfyui-hiforce-plugin": {"author_account_age_days": 2170, "last_update": "2024-06-14 08:13:24", "stars": 8}, "https://github.com/hinablue/ComfyUI_3dPoseEditor": {"author_account_age_days": 5479, "last_update": "2024-06-21 17:38:40", "stars": 201}, "https://github.com/hmwl/ComfyUI-TaskMonitor": {"author_account_age_days": 2981, "last_update": "2025-05-25 15:41:14", "stars": 4}, "https://github.com/hmwl/ComfyUI_zip": {"author_account_age_days": 2981, "last_update": "2025-05-25 16:21:35", "stars": 2}, "https://github.com/hnmr293/ComfyUI-latent-ops": {"author_account_age_days": 922, "last_update": "2025-04-16 08:04:59", "stars": 2}, "https://github.com/hnmr293/comfyui-savemem": {"author_account_age_days": 922, "last_update": "2025-04-15 02:10:14", "stars": 0}, "https://github.com/hodanajan/optimal-crop-resolution": {"author_account_age_days": 2697, "last_update": "2025-01-21 10:46:26", "stars": 1}, "https://github.com/hoveychen/ComfyUI-MusePose-Remaster": {"author_account_age_days": 5010, "last_update": "2024-10-22 09:40:04", "stars": 7}, "https://github.com/huagetai/ComfyUI-Gaffer": {"author_account_age_days": 4999, "last_update": "2024-06-19 00:58:38", "stars": 51}, "https://github.com/huagetai/ComfyUI_LightGradient": {"author_account_age_days": 4999, "last_update": "2024-05-23 01:21:27", "stars": 9}, "https://github.com/huanngzh/ComfyUI-MVAdapter": {"author_account_age_days": 1605, "last_update": "2025-06-26 07:01:15", "stars": 414}, "https://github.com/hubentu/ComfyUI-loras-loader": {"author_account_age_days": 3872, "last_update": "2025-06-04 19:02:35", "stars": 2}, "https://github.com/huchenlei/ComfyUI-IC-Light-Native": {"author_account_age_days": 3243, "last_update": "2025-02-25 16:35:36", "stars": 618}, "https://github.com/huchenlei/ComfyUI-layerdiffuse": {"author_account_age_days": 3243, "last_update": "2025-02-25 16:35:50", "stars": 1687}, "https://github.com/huchenlei/ComfyUI-openpose-editor": {"author_account_age_days": 3243, "last_update": "2024-07-31 13:44:16", "stars": 101}, "https://github.com/huchenlei/ComfyUI_DanTagGen": {"author_account_age_days": 3243, "last_update": "2024-08-01 01:42:14", "stars": 65}, "https://github.com/huchenlei/ComfyUI_densediffusion": {"author_account_age_days": 3243, "last_update": "2025-02-25 16:34:32", "stars": 134}, "https://github.com/huchenlei/ComfyUI_omost": {"author_account_age_days": 3243, "last_update": "2025-02-25 16:35:18", "stars": 445}, "https://github.com/hughescr/ComfyUI-OpenPose-Keypoint-Extractor": {"author_account_age_days": 6008, "last_update": "2025-04-08 18:48:00", "stars": 32}, "https://github.com/hugobb/FastGAN-ComfyUI-Node": {"author_account_age_days": 3203, "last_update": "2025-04-25 20:24:20", "stars": 1}, "https://github.com/huixingyun/ComfyUI-HX-Captioner": {"author_account_age_days": 203, "last_update": "2025-01-25 06:48:18", "stars": 0}, "https://github.com/huixingyun/ComfyUI-HX-Pimg": {"author_account_age_days": 203, "last_update": "2025-03-04 09:30:50", "stars": 0}, "https://github.com/humgate/simplecomfy": {"author_account_age_days": 1734, "last_update": "2024-06-14 08:58:21", "stars": 0}, "https://github.com/hunzmusic/ComfyUI-IG2MV": {"author_account_age_days": 96, "last_update": "2025-05-09 10:46:42", "stars": 26}, "https://github.com/hustille/ComfyUI_Fooocus_KSampler": {"author_account_age_days": 807, "last_update": "2024-05-22 20:39:48", "stars": 62}, "https://github.com/hustille/ComfyUI_hus_utils": {"author_account_age_days": 807, "last_update": "2024-05-22 20:39:34", "stars": 5}, "https://github.com/hvppycoding/comfyui-random-sampler-scheduler-steps": {"author_account_age_days": 972, "last_update": "2025-06-04 15:41:16", "stars": 0}, "https://github.com/hwhaocool/ComfyUI-Select-Any": {"author_account_age_days": 3255, "last_update": "2024-07-31 13:52:47", "stars": 2}, "https://github.com/hybskgks28275/ComfyUI-hybs-nodes": {"author_account_age_days": 1294, "last_update": "2025-05-28 07:15:21", "stars": 1}, "https://github.com/hyunamy/comfy-ui-on-complete-email-me": {"author_account_age_days": 3478, "last_update": "2025-04-10 01:38:49", "stars": 3}, "https://github.com/iDAPPA/ComfyUI-AMDGPUMonitor": {"author_account_age_days": 107, "last_update": "2025-03-13 18:16:21", "stars": 2}, "https://github.com/iFREEGROUP/comfyui-undistort": {"author_account_age_days": 1924, "last_update": "2024-06-14 08:59:52", "stars": 2}, "https://github.com/iSuneast/ComfyUI-WebhookNotifier": {"author_account_age_days": 4539, "last_update": "2025-04-06 03:53:13", "stars": 1}, "https://github.com/ialhabbal/OcclusionMask": {"author_account_age_days": 3387, "last_update": "2025-06-24 14:38:58", "stars": 20}, "https://github.com/iamandeepsandhu/ComfyUI-NSFW-Check": {"author_account_age_days": 2573, "last_update": "2024-11-26 07:32:18", "stars": 10}, "https://github.com/icesun963/ComfyUI_HFDownLoad": {"author_account_age_days": 4473, "last_update": "2024-07-18 12:13:23", "stars": 0}, "https://github.com/ichabodcole/ComfyUI-Ichis-Pack": {"author_account_age_days": 4763, "last_update": "2025-04-20 08:05:27", "stars": 2}, "https://github.com/idrirap/ComfyUI-Lora-Auto-Trigger-Words": {"author_account_age_days": 3417, "last_update": "2025-01-16 08:38:21", "stars": 206}, "https://github.com/iemesowum/ComfyUI_IsaacNodes": {"author_account_age_days": 5659, "last_update": "2025-03-27 13:28:10", "stars": 1}, "https://github.com/if-ai/ComfyUI-IF_AI_Dreamtalk": {"author_account_age_days": 3230, "last_update": "2025-03-14 13:19:03", "stars": 25}, "https://github.com/if-ai/ComfyUI-IF_AI_HFDownloaderNode": {"author_account_age_days": 3230, "last_update": "2025-03-09 09:21:13", "stars": 18}, "https://github.com/if-ai/ComfyUI-IF_AI_ParlerTTSNode": {"author_account_age_days": 3230, "last_update": "2025-03-14 13:27:47", "stars": 17}, "https://github.com/if-ai/ComfyUI-IF_AI_WishperSpeechNode": {"author_account_age_days": 3230, "last_update": "2025-03-09 09:17:01", "stars": 44}, "https://github.com/if-ai/ComfyUI-IF_AI_tools": {"author_account_age_days": 3230, "last_update": "2025-03-09 09:11:32", "stars": 654}, "https://github.com/if-ai/ComfyUI-IF_DatasetMkr": {"author_account_age_days": 3230, "last_update": "2025-03-17 08:14:01", "stars": 20}, "https://github.com/if-ai/ComfyUI-IF_Gemini": {"author_account_age_days": 3230, "last_update": "2025-04-14 06:27:01", "stars": 27}, "https://github.com/if-ai/ComfyUI-IF_LLM": {"author_account_age_days": 3230, "last_update": "2025-04-09 09:23:21", "stars": 123}, "https://github.com/if-ai/ComfyUI-IF_MemoAvatar": {"author_account_age_days": 3230, "last_update": "2025-03-09 09:28:07", "stars": 166}, "https://github.com/if-ai/ComfyUI-IF_Trellis": {"author_account_age_days": 3230, "last_update": "2025-03-09 09:31:12", "stars": 430}, "https://github.com/if-ai/ComfyUI-IF_VideoPrompts": {"author_account_age_days": 3230, "last_update": "2025-04-02 17:19:28", "stars": 47}, "https://github.com/if-ai/ComfyUI-WanResolutionSelector": {"author_account_age_days": 3230, "last_update": "2025-06-20 19:40:37", "stars": 0}, "https://github.com/if-ai/ComfyUI_IF_AI_LoadImages": {"author_account_age_days": 3230, "last_update": "2025-03-14 13:24:31", "stars": 9}, "https://github.com/ifmylove2011/comfyui-missed-tool": {"author_account_age_days": 3538, "last_update": "2025-04-10 09:15:08", "stars": 1}, "https://github.com/ihmily/ComfyUI-Light-Tool": {"author_account_age_days": 996, "last_update": "2025-06-04 10:12:12", "stars": 11}, "https://github.com/illuminatianon/comfyui-csvwildcards": {"author_account_age_days": 111, "last_update": "2025-05-02 21:45:53", "stars": 0}, "https://github.com/imb101/ComfyUI-FaceSwap": {"author_account_age_days": 1256, "last_update": "2024-05-22 18:22:29", "stars": 33}, "https://github.com/infinigence/ComfyUI_Model_Cache": {"author_account_age_days": 511, "last_update": "2025-03-28 02:35:14", "stars": 8}, "https://github.com/inflamously/comfyui-prompt-enhancer": {"author_account_age_days": 4148, "last_update": "2025-06-02 22:49:50", "stars": 0}, "https://github.com/injet-zhou/comfyui_extra_api": {"author_account_age_days": 2595, "last_update": "2025-06-06 02:35:34", "stars": 11}, "https://github.com/inventorado/ComfyUI_NNT": {"author_account_age_days": 3253, "last_update": "2025-01-08 17:22:46", "stars": 68}, "https://github.com/irreveloper/ComfyUI-DSD": {"author_account_age_days": 4085, "last_update": "2025-03-15 16:55:07", "stars": 39}, "https://github.com/iwanders/ComfyUI_nodes": {"author_account_age_days": 4793, "last_update": "2024-07-11 01:06:26", "stars": 1}, "https://github.com/jacklukai/ComfyUI_DeployCash": {"author_account_age_days": 350, "last_update": "2025-04-25 09:46:49", "stars": 0}, "https://github.com/jags111/ComfyUI_Jags_Audiotools": {"author_account_age_days": 4238, "last_update": "2025-03-20 16:23:33", "stars": 78}, "https://github.com/jags111/ComfyUI_Jags_VectorMagic": {"author_account_age_days": 4238, "last_update": "2025-04-02 08:46:34", "stars": 79}, "https://github.com/jags111/efficiency-nodes-comfyui": {"author_account_age_days": 4238, "last_update": "2025-05-30 03:20:24", "stars": 1279}, "https://github.com/jaimitoes/ComfyUI_Wan2_1_lora_trainer": {"author_account_age_days": 4127, "last_update": "2025-06-26 13:45:46", "stars": 16}, "https://github.com/jakechai/ComfyUI-JakeUpgrade": {"author_account_age_days": 1939, "last_update": "2025-06-22 02:42:41", "stars": 94}, "https://github.com/jamal-alkharrat/ComfyUI_rotate_image": {"author_account_age_days": 1344, "last_update": "2024-05-22 23:19:02", "stars": 2}, "https://github.com/jamesWalker55/comfyui-p2ldgan": {"author_account_age_days": 2894, "last_update": "2024-05-22 18:19:04", "stars": 17}, "https://github.com/jamesWalker55/comfyui-various": {"author_account_age_days": 2894, "last_update": "2025-02-27 11:01:51", "stars": 104}, "https://github.com/jammyfu/ComfyUI_PaintingCoderUtils": {"author_account_age_days": 4845, "last_update": "2025-02-26 05:03:05", "stars": 12}, "https://github.com/jasonjgardner/comfui-substance-designer-integration": {"author_account_age_days": 4745, "last_update": "2025-06-08 20:40:11", "stars": 1}, "https://github.com/jax-explorer/ComfyUI-InstantCharacter": {"author_account_age_days": 944, "last_update": "2025-05-13 15:04:58", "stars": 181}, "https://github.com/jax-explorer/ComfyUI-VideoBasic": {"author_account_age_days": 944, "last_update": "2025-06-22 14:53:19", "stars": 16}, "https://github.com/jax-explorer/ComfyUI-VideoBasicLatentSync": {"author_account_age_days": 944, "last_update": "2025-04-07 10:07:44", "stars": 0}, "https://github.com/jax-explorer/ComfyUI-easycontrol": {"author_account_age_days": 944, "last_update": "2025-04-17 15:39:33", "stars": 187}, "https://github.com/jax-explorer/comfyui-model-dynamic-loader": {"author_account_age_days": 944, "last_update": "2025-06-01 07:58:16", "stars": 1}, "https://github.com/jax-explorer/fast_video_comfyui": {"author_account_age_days": 944, "last_update": "2024-05-23 01:17:35", "stars": 0}, "https://github.com/jeffrey2212/ComfyUI-PonyCharacterPrompt": {"author_account_age_days": 4838, "last_update": "2024-10-26 05:38:07", "stars": 2}, "https://github.com/jeffy5/comfyui-faceless-node": {"author_account_age_days": 3298, "last_update": "2025-04-07 02:19:38", "stars": 52}, "https://github.com/jerome7562/ComfyUI-XenoFlow": {"author_account_age_days": 134, "last_update": "2025-03-10 16:33:16", "stars": 4}, "https://github.com/jerrylongyan/ComfyUI-My-Mask": {"author_account_age_days": 4299, "last_update": "2025-01-08 08:39:19", "stars": 2}, "https://github.com/jerrywap/ComfyUI_LoadImageFromHttpURL": {"author_account_age_days": 2701, "last_update": "2025-04-09 19:31:50", "stars": 1}, "https://github.com/jerrywap/ComfyUI_UploadToWebhookHTTP": {"author_account_age_days": 2701, "last_update": "2025-04-07 15:01:04", "stars": 1}, "https://github.com/jesenzhang/ComfyUI_StreamDiffusion": {"author_account_age_days": 4019, "last_update": "2025-03-18 04:47:24", "stars": 147}, "https://github.com/jhj0517/ComfyUI-Moondream-Gaze-Detection": {"author_account_age_days": 1266, "last_update": "2025-02-03 04:53:25", "stars": 54}, "https://github.com/jhj0517/ComfyUI-jhj-Kokoro-Onnx": {"author_account_age_days": 1266, "last_update": "2025-02-04 14:15:08", "stars": 4}, "https://github.com/jianzhichun/ComfyUI-Easyai": {"author_account_age_days": 3404, "last_update": "2024-10-27 03:29:53", "stars": 20}, "https://github.com/jiaqianjing/ComfyUI-MidjourneyHub": {"author_account_age_days": 3497, "last_update": "2025-06-19 01:29:05", "stars": 9}, "https://github.com/jiaxiangc/ComfyUI-ResAdapter": {"author_account_age_days": 1653, "last_update": "2024-05-23 00:22:23", "stars": 289}, "https://github.com/jinanlongen/ComfyUI-Prompt-Expander": {"author_account_age_days": 4944, "last_update": "2025-01-28 08:04:24", "stars": 0}, "https://github.com/jinchanz/ComfyUI-ADIC": {"author_account_age_days": 2432, "last_update": "2025-06-19 11:43:00", "stars": 0}, "https://github.com/jitcoder/lora-info": {"author_account_age_days": 4408, "last_update": "2025-05-15 07:25:46", "stars": 101}, "https://github.com/jjkramhoeft/ComfyUI-Jjk-Nodes": {"author_account_age_days": 4016, "last_update": "2024-05-22 20:44:56", "stars": 24}, "https://github.com/jkrauss82/ultools-comfyui": {"author_account_age_days": 4579, "last_update": "2025-04-09 20:17:27", "stars": 7}, "https://github.com/jmkl/ComfyUI-ricing": {"author_account_age_days": 4964, "last_update": "2024-10-16 15:38:08", "stars": 10}, "https://github.com/jn-jairo/jn_comfyui": {"author_account_age_days": 4352, "last_update": "2024-08-16 18:09:12", "stars": 5}, "https://github.com/jnxmx/ComfyUI_HuggingFace_Downloader": {"author_account_age_days": 706, "last_update": "2025-04-27 12:08:27", "stars": 5}, "https://github.com/joeriben/ai4artsed_comfyui": {"author_account_age_days": 4114, "last_update": "2025-06-09 21:38:55", "stars": 1}, "https://github.com/joeriben/ai4artsed_comfyui_nodes": {"author_account_age_days": 4114, "last_update": "2025-06-23 22:07:41", "stars": 0}, "https://github.com/john-mnz/ComfyUI-Inspyrenet-Rembg": {"author_account_age_days": 575, "last_update": "2024-07-31 13:54:32", "stars": 583}, "https://github.com/jojkaart/ComfyUI-sampler-lcm-alternative": {"author_account_age_days": 5165, "last_update": "2025-03-28 19:02:01", "stars": 134}, "https://github.com/jordoh/ComfyUI-Deepface": {"author_account_age_days": 5361, "last_update": "2025-05-27 18:09:06", "stars": 28}, "https://github.com/joreyaesh/comfyui_scroll_over_textarea": {"author_account_age_days": 4481, "last_update": "2025-03-09 18:58:09", "stars": 0}, "https://github.com/joreyaesh/comfyui_touchpad_scroll_controller.enableTouchpadScroll": {"author_account_age_days": 4481, "last_update": "2025-03-18 03:15:42", "stars": 0}, "https://github.com/jqy-yo/Comfyui-BBoxLowerMask2": {"author_account_age_days": 408, "last_update": "2025-05-19 02:28:44", "stars": 0}, "https://github.com/jroc22/ComfyUI-CSV-prompt-builder": {"author_account_age_days": 1057, "last_update": "2024-08-01 19:39:30", "stars": 10}, "https://github.com/jstit/comfyui_custom_node_image": {"author_account_age_days": 2231, "last_update": "2024-08-27 05:10:12", "stars": 0}, "https://github.com/jtrue/ComfyUI-JaRue": {"author_account_age_days": 4297, "last_update": "2024-06-14 09:01:12", "stars": 7}, "https://github.com/jtydhr88/ComfyUI-Hunyuan3D-1-wrapper": {"author_account_age_days": 5120, "last_update": "2024-11-13 11:50:46", "stars": 28}, "https://github.com/jtydhr88/ComfyUI-LayerDivider": {"author_account_age_days": 5120, "last_update": "2025-04-25 11:21:00", "stars": 95}, "https://github.com/jtydhr88/ComfyUI-Workflow-Encrypt": {"author_account_age_days": 5120, "last_update": "2024-07-31 13:45:53", "stars": 33}, "https://github.com/judian17/ComfyUI-Extract_Flux_Lora": {"author_account_age_days": 2213, "last_update": "2025-05-05 02:46:31", "stars": 14}, "https://github.com/judian17/ComfyUI-UniWorld-jd17": {"author_account_age_days": 2213, "last_update": "2025-06-10 14:12:16", "stars": 22}, "https://github.com/judian17/ComfyUI-joycaption-beta-one-GGUF": {"author_account_age_days": 2213, "last_update": "2025-05-26 02:28:33", "stars": 37}, "https://github.com/judian17/ComfyUI_ZIM": {"author_account_age_days": 2213, "last_update": "2025-05-14 11:32:06", "stars": 4}, "https://github.com/juehackr/comfyui_fk_server": {"author_account_age_days": 1479, "last_update": "2025-06-05 09:22:26", "stars": 407}, "https://github.com/jupo-ai/comfy-ex-tagcomplete": {"author_account_age_days": 117, "last_update": "2025-05-24 07:58:32", "stars": 14}, "https://github.com/jurdnf/ComfyUI-JurdnsIterativeNoiseKSampler": {"author_account_age_days": 33, "last_update": "2025-06-23 03:58:50", "stars": 3}, "https://github.com/jurdnf/ComfyUI-JurdnsModelSculptor": {"author_account_age_days": 33, "last_update": "2025-06-23 14:03:22", "stars": 3}, "https://github.com/jurdnisglobby/ComfyUI-Jurdns-Groq-Node": {"author_account_age_days": 279, "last_update": "2025-01-18 06:20:23", "stars": 2}, "https://github.com/justUmen/Bjornulf_custom_nodes": {"author_account_age_days": 3156, "last_update": "2025-06-11 12:32:38", "stars": 382}, "https://github.com/justin-vt/ComfyUI-brushstrokes": {"author_account_age_days": 3074, "last_update": "2025-03-05 18:27:37", "stars": 1}, "https://github.com/k-komarov/comfyui-bunny-cdn-storage": {"author_account_age_days": 3842, "last_update": "2024-08-31 20:59:08", "stars": 0}, "https://github.com/ka-puna/comfyui-yanc": {"author_account_age_days": 2574, "last_update": "2025-05-25 20:41:57", "stars": 9}, "https://github.com/kaanyalova/ComfyUI_ExtendedImageFormats": {"author_account_age_days": 1634, "last_update": "2025-01-25 10:57:38", "stars": 5}, "https://github.com/kadirnar/ComfyUI-Transformers": {"author_account_age_days": 2697, "last_update": "2024-06-22 22:44:39", "stars": 21}, "https://github.com/kadirnar/ComfyUI-YOLO": {"author_account_age_days": 2697, "last_update": "2025-05-30 13:26:43", "stars": 84}, "https://github.com/kael558/ComfyUI-GGUF-FantasyTalking": {"author_account_age_days": 3014, "last_update": "2025-06-18 02:19:28", "stars": 0}, "https://github.com/kaibioinfo/ComfyUI_AdvancedRefluxControl": {"author_account_age_days": 5049, "last_update": "2025-04-19 10:24:42", "stars": 592}, "https://github.com/kale4eat/ComfyUI-path-util": {"author_account_age_days": 1979, "last_update": "2024-05-25 05:44:11", "stars": 0}, "https://github.com/kale4eat/ComfyUI-speech-dataset-toolkit": {"author_account_age_days": 1979, "last_update": "2025-06-17 01:58:03", "stars": 18}, "https://github.com/kale4eat/ComfyUI-string-util": {"author_account_age_days": 1979, "last_update": "2024-05-23 00:24:40", "stars": 4}, "https://github.com/kale4eat/ComfyUI-text-file-util": {"author_account_age_days": 1979, "last_update": "2024-05-23 00:24:51", "stars": 0}, "https://github.com/kambara/ComfyUI-PromptPalette": {"author_account_age_days": 5902, "last_update": "2025-06-27 00:19:45", "stars": 2}, "https://github.com/kantsche/ComfyUI-MixMod": {"author_account_age_days": 4253, "last_update": "2025-05-08 17:40:10", "stars": 22}, "https://github.com/kappa54m/ComfyUI_Usability": {"author_account_age_days": 1878, "last_update": "2024-08-08 15:31:47", "stars": 0}, "https://github.com/karthikg-09/ComfyUI-Vton-Mask": {"author_account_age_days": 563, "last_update": "2025-05-24 18:37:41", "stars": 0}, "https://github.com/kasukanra/ComfyUI_StringToHex": {"author_account_age_days": 3029, "last_update": "2024-08-20 04:52:06", "stars": 1}, "https://github.com/katalist-ai/comfyUI-nsfw-detection": {"author_account_age_days": 1121, "last_update": "2024-05-23 01:23:32", "stars": 1}, "https://github.com/kazeyori/ComfyUI-QuickImageSequenceProcess": {"author_account_age_days": 1095, "last_update": "2025-04-05 12:52:40", "stars": 0}, "https://github.com/kealiu/ComfyUI-S3-Tools": {"author_account_age_days": 4495, "last_update": "2024-07-04 10:13:07", "stars": 7}, "https://github.com/kealiu/ComfyUI-Zero123-Porting": {"author_account_age_days": 4495, "last_update": "2024-08-22 07:07:57", "stars": 22}, "https://github.com/kealiu/ComfyUI-ZeroShot-MTrans": {"author_account_age_days": 4495, "last_update": "2024-07-04 10:12:32", "stars": 174}, "https://github.com/keit0728/ComfyUI-Image-Toolkit": {"author_account_age_days": 3356, "last_update": "2025-05-30 06:46:47", "stars": 1}, "https://github.com/keit0728/ComfyUI-keitNodes": {"author_account_age_days": 3355, "last_update": "2025-06-20 09:34:46", "stars": 3}, "https://github.com/kenjiqq/qq-nodes-comfyui": {"author_account_age_days": 5257, "last_update": "2025-06-24 22:38:01", "stars": 47}, "https://github.com/kevin314/ComfyUI-FastVideo": {"author_account_age_days": 2499, "last_update": "2025-05-25 10:25:28", "stars": 2}, "https://github.com/kevinmcmahondev/comfyui-kmcdev-image-filter-adjustments": {"author_account_age_days": 1121, "last_update": "2025-02-19 06:55:25", "stars": 0}, "https://github.com/kevinmcmahondev/comfyui-skin-tone-detector": {"author_account_age_days": 1121, "last_update": "2024-12-22 06:44:20", "stars": 2}, "https://github.com/kft334/Knodes": {"author_account_age_days": 1326, "last_update": "2024-06-14 08:12:06", "stars": 4}, "https://github.com/kijai/ComfyUI-ADMotionDirector": {"author_account_age_days": 2550, "last_update": "2024-11-07 07:20:23", "stars": 176}, "https://github.com/kijai/ComfyUI-APISR-KJ": {"author_account_age_days": 2550, "last_update": "2024-05-21 16:30:21", "stars": 68}, "https://github.com/kijai/ComfyUI-BrushNet-Wrapper": {"author_account_age_days": 2550, "last_update": "2024-06-20 12:15:16", "stars": 144}, "https://github.com/kijai/ComfyUI-CCSR": {"author_account_age_days": 2550, "last_update": "2024-06-28 11:13:33", "stars": 228}, "https://github.com/kijai/ComfyUI-CogVideoXWrapper": {"author_account_age_days": 2550, "last_update": "2025-06-19 21:10:27", "stars": 1503}, "https://github.com/kijai/ComfyUI-ControlNeXt-SVD": {"author_account_age_days": 2550, "last_update": "2024-08-15 08:26:15", "stars": 185}, "https://github.com/kijai/ComfyUI-DDColor": {"author_account_age_days": 2550, "last_update": "2024-05-21 16:04:26", "stars": 151}, "https://github.com/kijai/ComfyUI-DepthAnythingV2": {"author_account_age_days": 2550, "last_update": "2025-06-16 13:16:52", "stars": 329}, "https://github.com/kijai/ComfyUI-DiffusionLight": {"author_account_age_days": 2550, "last_update": "2024-05-21 16:16:52", "stars": 73}, "https://github.com/kijai/ComfyUI-DynamiCrafterWrapper": {"author_account_age_days": 2550, "last_update": "2025-06-02 11:49:00", "stars": 676}, "https://github.com/kijai/ComfyUI-ELLA-wrapper": {"author_account_age_days": 2550, "last_update": "2024-05-21 16:47:28", "stars": 116}, "https://github.com/kijai/ComfyUI-Florence2": {"author_account_age_days": 2550, "last_update": "2025-05-21 14:46:50", "stars": 1305}, "https://github.com/kijai/ComfyUI-FluxTrainer": {"author_account_age_days": 2550, "last_update": "2025-04-02 07:35:46", "stars": 933}, "https://github.com/kijai/ComfyUI-GIMM-VFI": {"author_account_age_days": 2550, "last_update": "2025-05-10 18:09:40", "stars": 324}, "https://github.com/kijai/ComfyUI-Geowizard": {"author_account_age_days": 2550, "last_update": "2024-12-16 19:33:54", "stars": 122}, "https://github.com/kijai/ComfyUI-HFRemoteVae": {"author_account_age_days": 2550, "last_update": "2025-03-01 18:22:59", "stars": 51}, "https://github.com/kijai/ComfyUI-HunyuanVideoWrapper": {"author_account_age_days": 2550, "last_update": "2025-05-12 13:31:36", "stars": 2487}, "https://github.com/kijai/ComfyUI-IC-Light": {"author_account_age_days": 2550, "last_update": "2025-05-30 19:21:20", "stars": 1051}, "https://github.com/kijai/ComfyUI-KJNodes": {"author_account_age_days": 2550, "last_update": "2025-06-18 08:03:54", "stars": 1482}, "https://github.com/kijai/ComfyUI-KwaiKolorsWrapper": {"author_account_age_days": 2550, "last_update": "2024-10-18 08:47:45", "stars": 595}, "https://github.com/kijai/ComfyUI-LBMWrapper": {"author_account_age_days": 2550, "last_update": "2025-05-14 09:25:13", "stars": 217}, "https://github.com/kijai/ComfyUI-LLaVA-OneVision": {"author_account_age_days": 2550, "last_update": "2024-08-25 14:04:22", "stars": 85}, "https://github.com/kijai/ComfyUI-LVCDWrapper": {"author_account_age_days": 2550, "last_update": "2024-09-30 11:49:12", "stars": 63}, "https://github.com/kijai/ComfyUI-LaVi-Bridge-Wrapper": {"author_account_age_days": 2550, "last_update": "2024-05-21 16:41:18", "stars": 22}, "https://github.com/kijai/ComfyUI-LivePortraitKJ": {"author_account_age_days": 2550, "last_update": "2024-08-05 21:39:49", "stars": 2002}, "https://github.com/kijai/ComfyUI-Lotus": {"author_account_age_days": 2550, "last_update": "2024-10-13 12:33:24", "stars": 137}, "https://github.com/kijai/ComfyUI-LuminaWrapper": {"author_account_age_days": 2550, "last_update": "2024-07-31 13:52:06", "stars": 195}, "https://github.com/kijai/ComfyUI-Marigold": {"author_account_age_days": 2550, "last_update": "2025-05-16 10:22:16", "stars": 537}, "https://github.com/kijai/ComfyUI-MimicMotionWrapper": {"author_account_age_days": 2550, "last_update": "2025-01-12 17:34:34", "stars": 489}, "https://github.com/kijai/ComfyUI-MoGe": {"author_account_age_days": 2550, "last_update": "2025-02-07 18:42:39", "stars": 49}, "https://github.com/kijai/ComfyUI-OpenDiTWrapper": {"author_account_age_days": 2550, "last_update": "2024-07-03 14:59:13", "stars": 43}, "https://github.com/kijai/ComfyUI-PyramidFlowWrapper": {"author_account_age_days": 2550, "last_update": "2024-11-15 13:28:18", "stars": 369}, "https://github.com/kijai/ComfyUI-SUPIR": {"author_account_age_days": 2550, "last_update": "2025-06-07 09:17:40", "stars": 1993}, "https://github.com/kijai/ComfyUI-SVD": {"author_account_age_days": 2550, "last_update": "2024-05-22 21:09:54", "stars": 163}, "https://github.com/kijai/ComfyUI-StableXWrapper": {"author_account_age_days": 2550, "last_update": "2025-01-31 11:59:01", "stars": 56}, "https://github.com/kijai/ComfyUI-depth-fm": {"author_account_age_days": 2550, "last_update": "2024-05-22 21:10:15", "stars": 75}, "https://github.com/kijai/ComfyUI-moondream": {"author_account_age_days": 2550, "last_update": "2024-08-12 16:30:11", "stars": 105}, "https://github.com/kijai/ComfyUI-segment-anything-2": {"author_account_age_days": 2550, "last_update": "2025-03-19 09:40:37", "stars": 965}, "https://github.com/kimara-ai/ComfyUI-Kimara-AI-Advanced-Watermarks": {"author_account_age_days": 224, "last_update": "2025-04-03 17:22:59", "stars": 16}, "https://github.com/kinfolk0117/ComfyUI_GradientDeepShrink": {"author_account_age_days": 836, "last_update": "2024-05-22 21:25:13", "stars": 28}, "https://github.com/kinfolk0117/ComfyUI_GridSwapper": {"author_account_age_days": 836, "last_update": "2024-10-27 09:04:20", "stars": 29}, "https://github.com/kinfolk0117/ComfyUI_Pilgram": {"author_account_age_days": 836, "last_update": "2024-05-22 21:25:24", "stars": 7}, "https://github.com/kinfolk0117/ComfyUI_SimpleTiles": {"author_account_age_days": 836, "last_update": "2024-05-22 21:25:01", "stars": 55}, "https://github.com/kk8bit/KayTool": {"author_account_age_days": 724, "last_update": "2025-05-25 03:46:23", "stars": 151}, "https://github.com/klinter007/klinter_nodes": {"author_account_age_days": 793, "last_update": "2025-03-30 14:45:57", "stars": 18}, "https://github.com/knuknX/ComfyUI-Image-Tools": {"author_account_age_days": 567, "last_update": "2024-06-14 09:05:58", "stars": 3}, "https://github.com/kohs100/comfyui-ppwc": {"author_account_age_days": 3258, "last_update": "2025-06-17 03:09:43", "stars": 0}, "https://github.com/kohya-ss/ControlNet-LLLite-ComfyUI": {"author_account_age_days": 2176, "last_update": "2024-05-22 20:44:44", "stars": 191}, "https://github.com/komojini/ComfyUI_SDXL_DreamBooth_LoRA_CustomNodes": {"author_account_age_days": 950, "last_update": "2024-05-22 21:34:27", "stars": 3}, "https://github.com/komojini/komojini-comfyui-nodes": {"author_account_age_days": 950, "last_update": "2024-05-22 21:34:39", "stars": 75}, "https://github.com/kostenickj/jk-comfyui-helpers": {"author_account_age_days": 3417, "last_update": "2024-12-19 10:22:42", "stars": 5}, "https://github.com/kpsss34/ComfyUI-kpsss34-Sana": {"author_account_age_days": 10, "last_update": "2025-06-25 07:38:11", "stars": 0}, "https://github.com/kraglik/prompt_collapse": {"author_account_age_days": 2825, "last_update": "2024-12-15 08:39:51", "stars": 5}, "https://github.com/krmahil/comfyui-hollow-preserve": {"author_account_age_days": 2646, "last_update": "2025-05-15 09:55:46", "stars": 1}, "https://github.com/kukuo6666/ComfyUI-Equirect": {"author_account_age_days": 1934, "last_update": "2025-03-29 18:28:47", "stars": 1}, "https://github.com/kungful/ComfyUI_to_webui": {"author_account_age_days": 1490, "last_update": "2025-05-30 20:08:56", "stars": 15}, "https://github.com/kunieone/ComfyUI_alkaid": {"author_account_age_days": 2887, "last_update": "2024-05-23 01:10:21", "stars": 0}, "https://github.com/kwaroran/abg-comfyui": {"author_account_age_days": 975, "last_update": "2024-05-22 18:19:51", "stars": 25}, "https://github.com/kycg/comfyui-Lora-auto-downloader": {"author_account_age_days": 1308, "last_update": "2024-11-08 19:57:23", "stars": 1}, "https://github.com/l-comm/WatermarkRemoval": {"author_account_age_days": 178, "last_update": "2025-01-13 05:33:32", "stars": 4}, "https://github.com/l20richo/ComfyUI-Azure-Blob-Storage": {"author_account_age_days": 1534, "last_update": "2024-06-22 16:53:47", "stars": 2}, "https://github.com/l3ony2k/comfyui-leon-nodes": {"author_account_age_days": 2024, "last_update": "2025-06-25 09:18:17", "stars": 0}, "https://github.com/laksjdjf/Batch-Condition-ComfyUI": {"author_account_age_days": 3198, "last_update": "2024-05-22 20:42:42", "stars": 7}, "https://github.com/laksjdjf/ComfyUI-Imatrix": {"author_account_age_days": 3198, "last_update": "2025-06-07 00:17:26", "stars": 3}, "https://github.com/laksjdjf/LCMSampler-ComfyUI": {"author_account_age_days": 3198, "last_update": "2024-05-22 20:42:17", "stars": 16}, "https://github.com/laksjdjf/LoRTnoC-ComfyUI": {"author_account_age_days": 3198, "last_update": "2024-05-22 20:42:29", "stars": 13}, "https://github.com/laksjdjf/cd-tuner_negpip-ComfyUI": {"author_account_age_days": 3198, "last_update": "2024-05-22 20:42:04", "stars": 23}, "https://github.com/laksjdjf/cgem156-ComfyUI": {"author_account_age_days": 3198, "last_update": "2025-04-30 14:52:29", "stars": 72}, "https://github.com/laksjdjf/pfg-ComfyUI": {"author_account_age_days": 3198, "last_update": "2024-05-22 20:41:41", "stars": 12}, "https://github.com/larsupb/LoRA-Merger-ComfyUI": {"author_account_age_days": 3443, "last_update": "2024-10-24 11:28:08", "stars": 46}, "https://github.com/latenightlabs/ComfyUI-LNL": {"author_account_age_days": 518, "last_update": "2024-10-07 20:09:43", "stars": 26}, "https://github.com/lazniak/Head-Orientation-Node-for-ComfyUI---by-PabloGFX": {"author_account_age_days": 2649, "last_update": "2024-09-25 15:02:14", "stars": 10}, "https://github.com/lazniak/LiquidTime-Interpolation": {"author_account_age_days": 2649, "last_update": "2025-04-03 11:42:12", "stars": 12}, "https://github.com/lazniak/comfyui-google-photos-loader": {"author_account_age_days": 2649, "last_update": "2025-04-03 11:46:29", "stars": 3}, "https://github.com/lc03lc/Comfyui_OmniConsistency": {"author_account_age_days": 1343, "last_update": "2025-06-01 02:56:02", "stars": 58}, "https://github.com/lceric/comfyui-gpt-image": {"author_account_age_days": 3077, "last_update": "2025-05-19 10:49:30", "stars": 8}, "https://github.com/lebrosoft/ComfyUI-VideoChatWrapper": {"author_account_age_days": 3884, "last_update": "2025-06-06 04:07:48", "stars": 2}, "https://github.com/leeguandong/ComfyUI_1Prompt1Story": {"author_account_age_days": 3162, "last_update": "2025-03-13 16:11:50", "stars": 5}, "https://github.com/leeguandong/ComfyUI_ChatGen": {"author_account_age_days": 3162, "last_update": "2025-03-13 16:24:46", "stars": 2}, "https://github.com/leeguandong/ComfyUI_Cogview4": {"author_account_age_days": 3162, "last_update": "2025-03-13 15:58:44", "stars": 2}, "https://github.com/leeguandong/ComfyUI_CompareModelWeights": {"author_account_age_days": 3162, "last_update": "2025-01-09 02:43:41", "stars": 3}, "https://github.com/leeguandong/ComfyUI_CrossImageAttention": {"author_account_age_days": 3162, "last_update": "2024-08-16 11:59:42", "stars": 3}, "https://github.com/leeguandong/ComfyUI_DeepSeekVL2": {"author_account_age_days": 3162, "last_update": "2025-03-13 16:32:16", "stars": 0}, "https://github.com/leeguandong/ComfyUI_FluxAttentionMask": {"author_account_age_days": 3162, "last_update": "2025-03-15 07:37:50", "stars": 4}, "https://github.com/leeguandong/ComfyUI_FluxClipWeight": {"author_account_age_days": 3162, "last_update": "2025-03-02 07:32:55", "stars": 3}, "https://github.com/leeguandong/ComfyUI_FluxCustomId": {"author_account_age_days": 3162, "last_update": "2025-01-06 01:12:44", "stars": 7}, "https://github.com/leeguandong/ComfyUI_FluxLayerDiffuse": {"author_account_age_days": 3162, "last_update": "2025-03-17 01:07:01", "stars": 15}, "https://github.com/leeguandong/ComfyUI_Gemma3": {"author_account_age_days": 3162, "last_update": "2025-03-25 14:45:01", "stars": 7}, "https://github.com/leeguandong/ComfyUI_InternVL2": {"author_account_age_days": 3162, "last_update": "2024-08-10 11:00:11", "stars": 13}, "https://github.com/leeguandong/ComfyUI_LLaSM": {"author_account_age_days": 3162, "last_update": "2024-08-10 10:58:17", "stars": 4}, "https://github.com/leeguandong/ComfyUI_M3Net": {"author_account_age_days": 3162, "last_update": "2024-08-16 00:03:21", "stars": 12}, "https://github.com/leeguandong/ComfyUI_MasaCtrl": {"author_account_age_days": 3162, "last_update": "2024-09-01 03:47:35", "stars": 3}, "https://github.com/leeguandong/ComfyUI_QWQ32B": {"author_account_age_days": 3162, "last_update": "2025-03-15 17:19:23", "stars": 2}, "https://github.com/leeguandong/ComfyUI_Style_Aligned": {"author_account_age_days": 3162, "last_update": "2024-08-16 11:59:33", "stars": 5}, "https://github.com/leeguandong/ComfyUI_VideoEditing": {"author_account_age_days": 3162, "last_update": "2024-08-14 16:59:49", "stars": 4}, "https://github.com/leeguandong/ComfyUI_VisualAttentionMap": {"author_account_age_days": 3162, "last_update": "2024-08-26 05:15:14", "stars": 8}, "https://github.com/leestuartx/ComfyUI-GG": {"author_account_age_days": 4146, "last_update": "2025-03-10 16:26:37", "stars": 2}, "https://github.com/lenskikh/ComfyUI-Prompt-Worker": {"author_account_age_days": 3864, "last_update": "2025-06-26 02:09:02", "stars": 11}, "https://github.com/leoleelxh/Comfy-Topaz-Photo": {"author_account_age_days": 4443, "last_update": "2025-05-24 05:47:40", "stars": 13}, "https://github.com/leoleelxh/ComfyUI-LLMs": {"author_account_age_days": 4443, "last_update": "2025-06-17 13:52:33", "stars": 47}, "https://github.com/leonardomiramondi/flux-context-comfyui": {"author_account_age_days": 786, "last_update": "2025-06-25 10:18:42", "stars": 0}, "https://github.com/lepiai/ComfyUI-Minitools": {"author_account_age_days": 2237, "last_update": "2025-05-24 16:11:50", "stars": 8}, "https://github.com/lerignoux/ComfyUI-PechaKucha": {"author_account_age_days": 4672, "last_update": "2025-05-12 15:42:19", "stars": 1}, "https://github.com/lgldlk/ComfyUI-PC-ding-dong": {"author_account_age_days": 2057, "last_update": "2024-12-27 03:25:38", "stars": 65}, "https://github.com/lgldlk/ComfyUI-PSD-Replace": {"author_account_age_days": 2057, "last_update": "2025-03-15 07:03:24", "stars": 4}, "https://github.com/liangt/comfyui-loadimagewithsubfolder": {"author_account_age_days": 4426, "last_update": "2025-03-27 16:49:42", "stars": 3}, "https://github.com/licyk/ComfyUI-HakuImg": {"author_account_age_days": 1635, "last_update": "2025-05-04 03:31:32", "stars": 8}, "https://github.com/licyk/ComfyUI-Restart-Sampler": {"author_account_age_days": 1635, "last_update": "2025-02-24 04:53:52", "stars": 10}, "https://github.com/licyk/ComfyUI-TCD-Sampler": {"author_account_age_days": 1635, "last_update": "2025-03-27 16:32:33", "stars": 4}, "https://github.com/lihaoyun6/ComfyUI-CSV-Random-Picker": {"author_account_age_days": 3478, "last_update": "2025-05-10 10:41:53", "stars": 1}, "https://github.com/lilly1987/ComfyUI_node_Lilly": {"author_account_age_days": 3277, "last_update": "2024-12-21 01:50:03", "stars": 55}, "https://github.com/lingha0h/comfyui_kj": {"author_account_age_days": 141, "last_update": "2025-03-20 13:24:29", "stars": 6}, "https://github.com/linjian-ufo/comfyui_deepseek_lj257_update": {"author_account_age_days": 465, "last_update": "2025-06-17 11:26:32", "stars": 0}, "https://github.com/linksluckytime/comfyui_snacknodes": {"author_account_age_days": 792, "last_update": "2025-05-07 01:48:50", "stars": 0}, "https://github.com/linshier/comfyui-remote-tools": {"author_account_age_days": 4159, "last_update": "2024-05-28 07:44:23", "stars": 4}, "https://github.com/lisaks/comfyui-panelforge": {"author_account_age_days": 1104, "last_update": "2025-04-29 00:25:00", "stars": 1}, "https://github.com/liuqianhonga/ComfyUI-Html2Image": {"author_account_age_days": 554, "last_update": "2025-06-22 07:58:49", "stars": 9}, "https://github.com/liuqianhonga/ComfyUI-Image-Compressor": {"author_account_age_days": 554, "last_update": "2025-06-22 08:32:22", "stars": 18}, "https://github.com/liuqianhonga/ComfyUI-QHNodes": {"author_account_age_days": 554, "last_update": "2025-06-22 08:33:17", "stars": 3}, "https://github.com/liuqianhonga/ComfyUI-String-Helper": {"author_account_age_days": 554, "last_update": "2025-06-22 07:56:48", "stars": 7}, "https://github.com/liushuchun/ComfyUI_Lora_List_With_Url_Loader": {"author_account_age_days": 4414, "last_update": "2024-09-26 12:38:32", "stars": 2}, "https://github.com/liusida/ComfyUI-AutoCropFaces": {"author_account_age_days": 3575, "last_update": "2024-08-12 17:38:17", "stars": 85}, "https://github.com/liusida/ComfyUI-B-LoRA": {"author_account_age_days": 3575, "last_update": "2024-06-18 03:17:46", "stars": 75}, "https://github.com/liusida/ComfyUI-Debug": {"author_account_age_days": 3575, "last_update": "2024-06-14 10:25:26", "stars": 11}, "https://github.com/liusida/ComfyUI-Login": {"author_account_age_days": 3575, "last_update": "2024-11-15 01:35:25", "stars": 171}, "https://github.com/liusida/ComfyUI-SD3-nodes": {"author_account_age_days": 3575, "last_update": "2024-06-14 13:01:41", "stars": 5}, "https://github.com/livepeer/ComfyUI-Stream-Pack": {"author_account_age_days": 3074, "last_update": "2025-05-07 12:46:57", "stars": 8}, "https://github.com/ljleb/comfy-mecha": {"author_account_age_days": 2831, "last_update": "2025-06-09 02:40:54", "stars": 79}, "https://github.com/lks-ai/ComfyUI-StableAudioSampler": {"author_account_age_days": 467, "last_update": "2025-01-07 08:33:57", "stars": 252}, "https://github.com/lks-ai/anynode": {"author_account_age_days": 467, "last_update": "2024-07-07 03:45:48", "stars": 534}, "https://github.com/lldacing/ComfyUI_BEN_ll": {"author_account_age_days": 2460, "last_update": "2025-05-22 07:01:42", "stars": 4}, "https://github.com/lldacing/ComfyUI_BiRefNet_ll": {"author_account_age_days": 2460, "last_update": "2025-06-01 16:39:20", "stars": 222}, "https://github.com/lldacing/ComfyUI_Patches_ll": {"author_account_age_days": 2460, "last_update": "2025-04-08 06:22:28", "stars": 107}, "https://github.com/lldacing/ComfyUI_PuLID_Flux_ll": {"author_account_age_days": 2460, "last_update": "2025-04-08 06:21:55", "stars": 373}, "https://github.com/lldacing/ComfyUI_StableDelight_ll": {"author_account_age_days": 2460, "last_update": "2025-04-08 06:22:43", "stars": 12}, "https://github.com/lldacing/ComfyUI_StableHair_ll": {"author_account_age_days": 2460, "last_update": "2025-03-31 09:16:21", "stars": 65}, "https://github.com/lldacing/comfyui-easyapi-nodes": {"author_account_age_days": 2460, "last_update": "2025-05-15 07:06:47", "stars": 77}, "https://github.com/lo-th/Comfyui_three_js": {"author_account_age_days": 4847, "last_update": "2025-06-12 08:18:17", "stars": 19}, "https://github.com/lodestone-rock/ComfyUI_FluxMod": {"author_account_age_days": 951, "last_update": "2025-06-22 09:44:09", "stars": 108}, "https://github.com/logtd/ComfyUI-4DHumans": {"author_account_age_days": 490, "last_update": "2024-08-30 21:12:55", "stars": 7}, "https://github.com/logtd/ComfyUI-APGScaling": {"author_account_age_days": 490, "last_update": "2024-10-06 20:51:27", "stars": 29}, "https://github.com/logtd/ComfyUI-DiLightNet": {"author_account_age_days": 490, "last_update": "2024-10-06 03:48:15", "stars": 11}, "https://github.com/logtd/ComfyUI-FLATTEN": {"author_account_age_days": 490, "last_update": "2024-08-30 21:18:55", "stars": 109}, "https://github.com/logtd/ComfyUI-Fluxtapoz": {"author_account_age_days": 490, "last_update": "2025-01-09 02:38:40", "stars": 1344}, "https://github.com/logtd/ComfyUI-InstanceDiffusion": {"author_account_age_days": 490, "last_update": "2024-08-30 21:17:51", "stars": 180}, "https://github.com/logtd/ComfyUI-InversedNoise": {"author_account_age_days": 490, "last_update": "2024-05-22 00:10:18", "stars": 16}, "https://github.com/logtd/ComfyUI-MochiEdit": {"author_account_age_days": 490, "last_update": "2024-11-03 18:38:16", "stars": 294}, "https://github.com/logtd/ComfyUI-MotionThiefExperiment": {"author_account_age_days": 490, "last_update": "2024-08-30 21:19:48", "stars": 41}, "https://github.com/logtd/ComfyUI-RAVE_ATTN": {"author_account_age_days": 490, "last_update": "2024-05-22 00:20:03", "stars": 14}, "https://github.com/logtd/ComfyUI-ReNoise": {"author_account_age_days": 490, "last_update": "2024-09-01 22:17:49", "stars": 6}, "https://github.com/logtd/ComfyUI-RefSampling": {"author_account_age_days": 490, "last_update": "2024-09-11 20:56:01", "stars": 5}, "https://github.com/logtd/ComfyUI-RefUNet": {"author_account_age_days": 490, "last_update": "2024-08-30 21:20:20", "stars": 45}, "https://github.com/logtd/ComfyUI-SEGAttention": {"author_account_age_days": 490, "last_update": "2024-09-11 20:55:00", "stars": 38}, "https://github.com/logtd/ComfyUI-SSREncoder": {"author_account_age_days": 490, "last_update": "2024-08-24 23:33:09", "stars": 1}, "https://github.com/logtd/ComfyUI-SeeCoder": {"author_account_age_days": 490, "last_update": "2024-08-24 23:31:10", "stars": 0}, "https://github.com/logtd/ComfyUI-TrackingNodes": {"author_account_age_days": 490, "last_update": "2024-05-22 00:03:27", "stars": 19}, "https://github.com/logtd/ComfyUI-ViewCrafter": {"author_account_age_days": 490, "last_update": "2024-09-30 19:32:41", "stars": 12}, "https://github.com/longgui0318/comfyui-common-util": {"author_account_age_days": 4532, "last_update": "2025-04-07 08:19:05", "stars": 1}, "https://github.com/longgui0318/comfyui-llm-assistant": {"author_account_age_days": 4532, "last_update": "2024-09-17 13:12:43", "stars": 7}, "https://github.com/longgui0318/comfyui-magic-clothing": {"author_account_age_days": 4532, "last_update": "2024-08-08 14:42:04", "stars": 77}, "https://github.com/longgui0318/comfyui-mask-util": {"author_account_age_days": 4532, "last_update": "2025-04-07 08:18:11", "stars": 7}, "https://github.com/lord-lethris/ComfyUI-RPG-Characters": {"author_account_age_days": 4791, "last_update": "2025-06-18 23:08:15", "stars": 1}, "https://github.com/lordgasmic/comfyui_save_image_with_options": {"author_account_age_days": 5135, "last_update": "2024-06-20 16:39:23", "stars": 0}, "https://github.com/lordgasmic/comfyui_wildcards": {"author_account_age_days": 5135, "last_update": "2024-06-20 16:52:14", "stars": 10}, "https://github.com/lquesada/ComfyUI-Inpaint-CropAndStitch": {"author_account_age_days": 4418, "last_update": "2025-05-10 07:46:49", "stars": 729}, "https://github.com/lquesada/ComfyUI-Interactive": {"author_account_age_days": 4418, "last_update": "2025-05-01 03:39:47", "stars": 38}, "https://github.com/lquesada/ComfyUI-Prompt-Combinator": {"author_account_age_days": 4418, "last_update": "2025-04-16 20:52:10", "stars": 38}, "https://github.com/lrzjason/ComfyUI-Watermark-Detection": {"author_account_age_days": 4030, "last_update": "2025-05-28 20:46:50", "stars": 18}, "https://github.com/lrzjason/Comfyui-In-Context-Lora-Utils": {"author_account_age_days": 4030, "last_update": "2025-04-03 09:09:43", "stars": 224}, "https://github.com/lrzjason/Comfyui-Kolors-Utils": {"author_account_age_days": 4030, "last_update": "2025-05-05 16:10:11", "stars": 17}, "https://github.com/lrzjason/Comfyui-ThinkRemover": {"author_account_age_days": 4030, "last_update": "2025-02-07 10:57:50", "stars": 4}, "https://github.com/ltdrdata/ComfyUI-Impact-Pack": {"author_account_age_days": 830, "last_update": "2025-06-19 03:41:19", "stars": 2522}, "https://github.com/ltdrdata/ComfyUI-Impact-Subpack": {"author_account_age_days": 830, "last_update": "2025-06-17 13:31:07", "stars": 211}, "https://github.com/ltdrdata/ComfyUI-Inspire-Pack": {"author_account_age_days": 830, "last_update": "2025-06-17 13:06:13", "stars": 607}, "https://github.com/ltdrdata/ComfyUI-Manager": {"author_account_age_days": 442, "last_update": "2025-06-26 22:23:17", "stars": 10562}, "https://github.com/ltdrdata/comfyui-connection-helper": {"author_account_age_days": 830, "last_update": "2025-04-07 13:49:56", "stars": 27}, "https://github.com/ltdrdata/was-node-suite-comfyui": {"author_account_age_days": 830, "last_update": "2025-06-03 09:41:36", "stars": 34}, "https://github.com/lthero-big/ComfyUI-GaussianShadingWatermark": {"author_account_age_days": 1761, "last_update": "2025-03-23 08:18:07", "stars": 5}, "https://github.com/luandev/ComfyUI-CrewAI": {"author_account_age_days": 4175, "last_update": "2025-01-17 18:06:27", "stars": 53}, "https://github.com/lucak5s/comfyui_gfpgan": {"author_account_age_days": 1048, "last_update": "2025-06-25 21:42:30", "stars": 1}, "https://github.com/lucianoambrosini/ComfyUI-ATk-Nodes": {"author_account_age_days": 3482, "last_update": "2025-06-25 05:04:14", "stars": 2}, "https://github.com/lujiazho/ComfyUI-CatvtonFluxWrapper": {"author_account_age_days": 1805, "last_update": "2024-12-02 22:10:41", "stars": 91}, "https://github.com/lum3on/ComfyUI-FrameUtilitys": {"author_account_age_days": 142, "last_update": "2025-06-09 22:12:49", "stars": 1}, "https://github.com/lum3on/ComfyUI-ModelQuantizer": {"author_account_age_days": 142, "last_update": "2025-06-14 20:45:21", "stars": 34}, "https://github.com/lum3on/ComfyUI-StableAudioX": {"author_account_age_days": 142, "last_update": "2025-06-24 22:55:28", "stars": 19}, "https://github.com/lum3on/comfyui_EdgeTAM": {"author_account_age_days": 142, "last_update": "2025-06-16 10:42:14", "stars": 0}, "https://github.com/lum3on/comfyui_LLM_Polymath": {"author_account_age_days": 142, "last_update": "2025-06-12 22:24:36", "stars": 63}, "https://github.com/lumalabs/ComfyUI-LumaAI-API": {"author_account_age_days": 1478, "last_update": "2025-03-31 22:54:28", "stars": 200}, "https://github.com/lunarring/bitalino_comfy": {"author_account_age_days": 1610, "last_update": "2025-02-21 09:03:54", "stars": 0}, "https://github.com/lxe/ComfyUI-OpenAI-Compat-LLM-Node": {"author_account_age_days": 4865, "last_update": "2025-05-28 05:39:55", "stars": 0}, "https://github.com/m-sokes/ComfyUI-Sokes-Nodes": {"author_account_age_days": 683, "last_update": "2025-06-27 01:42:33", "stars": 2}, "https://github.com/madtunebk/ComfyUI-ControlnetAux": {"author_account_age_days": 828, "last_update": "2024-06-28 16:16:51", "stars": 15}, "https://github.com/maepopi/Diffusers-in-ComfyUI": {"author_account_age_days": 2725, "last_update": "2025-03-28 07:29:38", "stars": 6}, "https://github.com/magekinnarus/ComfyUI-V-Prediction-Node": {"author_account_age_days": 1000, "last_update": "2025-02-04 08:29:24", "stars": 2}, "https://github.com/magic-eraser-org/ComfyUI-Unwatermark": {"author_account_age_days": 44, "last_update": "2025-05-14 06:50:13", "stars": 1}, "https://github.com/maludwig/basix_image_filters": {"author_account_age_days": 3874, "last_update": "2025-05-15 23:29:38", "stars": 5}, "https://github.com/mang01010/MangoNodePack": {"author_account_age_days": 118, "last_update": "2025-05-21 21:53:18", "stars": 3}, "https://github.com/mango-rgb/ComfyUI-Mango-Random-node": {"author_account_age_days": 771, "last_update": "2025-01-21 11:31:10", "stars": 1}, "https://github.com/mape/ComfyUI-mape-Helpers": {"author_account_age_days": 6122, "last_update": "2024-06-27 16:30:32", "stars": 180}, "https://github.com/maracman/ComfyUI-SubjectStyle-CSV": {"author_account_age_days": 1534, "last_update": "2024-06-24 13:53:39", "stars": 4}, "https://github.com/marawan206/ComfyUI-FaceCropper": {"author_account_age_days": 537, "last_update": "2025-03-07 01:44:44", "stars": 10}, "https://github.com/marcoc2/ComfyUI-AnotherUtils": {"author_account_age_days": 5551, "last_update": "2024-12-20 04:34:13", "stars": 1}, "https://github.com/marcoc2/ComfyUI_CogView4-6B_diffusers": {"author_account_age_days": 5551, "last_update": "2025-03-04 17:43:50", "stars": 2}, "https://github.com/marduk191/ComfyUI-Fluxpromptenhancer": {"author_account_age_days": 4782, "last_update": "2025-04-03 17:22:55", "stars": 106}, "https://github.com/marduk191/comfyui-marnodes": {"author_account_age_days": 4782, "last_update": "2025-03-27 13:26:45", "stars": 3}, "https://github.com/marhensa/sdxl-recommended-res-calc": {"author_account_age_days": 5142, "last_update": "2025-04-13 09:33:49", "stars": 87}, "https://github.com/marklieberman/ComfyUI-Liebs-Picker": {"author_account_age_days": 4173, "last_update": "2025-05-09 21:16:27", "stars": 2}, "https://github.com/marklieberman/ComfyUI-Liebs-Title": {"author_account_age_days": 4173, "last_update": "2025-05-12 23:32:28", "stars": 0}, "https://github.com/marklieberman/ComfyUI-Liebs-Toast": {"author_account_age_days": 4173, "last_update": "2025-05-12 23:34:55", "stars": 0}, "https://github.com/markuryy/ComfyUI-Flux-Prompt-Saver": {"author_account_age_days": 3246, "last_update": "2024-10-30 10:25:15", "stars": 12}, "https://github.com/markuryy/ComfyUI-Simple-Video-XY-Plot": {"author_account_age_days": 3246, "last_update": "2025-03-12 18:18:54", "stars": 3}, "https://github.com/markuryy/ComfyUI-SuperLoader": {"author_account_age_days": 3246, "last_update": "2025-03-12 18:23:22", "stars": 0}, "https://github.com/martijnat/comfyui-previewlatent": {"author_account_age_days": 3199, "last_update": "2024-05-22 21:28:39", "stars": 36}, "https://github.com/martin-rizzo/ComfyUI-TinyBreaker": {"author_account_age_days": 1965, "last_update": "2025-05-04 00:02:02", "stars": 35}, "https://github.com/massao000/ComfyUI_aspect_ratios": {"author_account_age_days": 1774, "last_update": "2024-05-22 22:23:10", "stars": 10}, "https://github.com/matan1905/ComfyUI-Serving-Toolkit": {"author_account_age_days": 3107, "last_update": "2025-05-01 10:03:33", "stars": 67}, "https://github.com/matorzhin/milan-nodes-comfyui": {"author_account_age_days": 3020, "last_update": "2025-06-05 16:37:41", "stars": 0}, "https://github.com/mattjohnpowell/comfyui-lmstudio-image-to-text-node": {"author_account_age_days": 4917, "last_update": "2025-06-22 11:06:40", "stars": 17}, "https://github.com/mav-rik/facerestore_cf": {"author_account_age_days": 3288, "last_update": "2025-06-21 08:02:17", "stars": 276}, "https://github.com/mbrostami/ComfyUI-HF": {"author_account_age_days": 4689, "last_update": "2024-05-27 21:45:33", "stars": 19}, "https://github.com/mbrostami/ComfyUI-TITrain": {"author_account_age_days": 4689, "last_update": "2025-03-14 17:39:11", "stars": 8}, "https://github.com/mcmonkeyprojects/sd-dynamic-thresholding": {"author_account_age_days": 2469, "last_update": "2025-03-14 09:33:32", "stars": 1207}, "https://github.com/meanin2/comfyui-MGnodes": {"author_account_age_days": 1022, "last_update": "2025-01-24 07:32:08", "stars": 2}, "https://github.com/meap158/ComfyUI-Background-Replacement": {"author_account_age_days": 3570, "last_update": "2025-01-06 23:45:28", "stars": 59}, "https://github.com/meap158/ComfyUI-GPU-temperature-protection": {"author_account_age_days": 3570, "last_update": "2024-05-22 20:43:21", "stars": 3}, "https://github.com/meap158/ComfyUI-Prompt-Expansion": {"author_account_age_days": 3570, "last_update": "2024-05-22 20:43:37", "stars": 77}, "https://github.com/mech-tools/comfyui-checkpoint-automatic-config": {"author_account_age_days": 4807, "last_update": "2024-09-05 14:23:29", "stars": 3}, "https://github.com/mediocreatmybest/ComfyUI-Transformers-Pipeline": {"author_account_age_days": 1569, "last_update": "2025-02-24 15:11:36", "stars": 4}, "https://github.com/melMass/comfy_mtb": {"author_account_age_days": 4113, "last_update": "2025-06-26 15:33:45", "stars": 577}, "https://github.com/melMass/comfy_oiio": {"author_account_age_days": 4113, "last_update": "2025-04-14 20:24:37", "stars": 4}, "https://github.com/mephisto83/petty-paint-comfyui-node": {"author_account_age_days": 4040, "last_update": "2024-10-23 22:23:03", "stars": 3}, "https://github.com/meshmesh-io/ComfyUI-MeshMesh": {"author_account_age_days": 596, "last_update": "2024-05-23 00:10:09", "stars": 0}, "https://github.com/meshmesh-io/mm-comfyui-loopback": {"author_account_age_days": 596, "last_update": "2024-05-23 00:09:57", "stars": 1}, "https://github.com/meshmesh-io/mm-comfyui-megamask": {"author_account_age_days": 596, "last_update": "2024-05-23 00:09:47", "stars": 0}, "https://github.com/metal3d/ComfyUI_Human_Parts": {"author_account_age_days": 5841, "last_update": "2025-03-07 08:14:46", "stars": 34}, "https://github.com/metal3d/ComfyUI_M3D_photo_effects": {"author_account_age_days": 5841, "last_update": "2025-03-11 12:09:55", "stars": 3}, "https://github.com/metncelik/comfyui_met_suite": {"author_account_age_days": 990, "last_update": "2025-03-27 12:27:48", "stars": 2}, "https://github.com/mfg637/ComfyUI-ScheduledGuider-Ext": {"author_account_age_days": 2685, "last_update": "2025-06-08 14:30:43", "stars": 2}, "https://github.com/mgfxer/ComfyUI-FrameFX": {"author_account_age_days": 369, "last_update": "2024-07-20 13:58:46", "stars": 23}, "https://github.com/miaoshouai/ComfyUI-Miaoshouai-Tagger": {"author_account_age_days": 835, "last_update": "2025-04-26 02:32:18", "stars": 416}, "https://github.com/michaelgold/ComfyUI-HF-Model-Downloader": {"author_account_age_days": 5724, "last_update": "2025-06-09 15:02:48", "stars": 2}, "https://github.com/microbote/ComfyUI-StyledCLIPTextEncode": {"author_account_age_days": 2375, "last_update": "2024-08-27 03:37:29", "stars": 2}, "https://github.com/mihaiiancu/ComfyUI_Inpaint": {"author_account_age_days": 3029, "last_update": "2024-05-22 18:19:38", "stars": 9}, "https://github.com/mikebilly/Transparent-background-comfyUI": {"author_account_age_days": 2931, "last_update": "2025-01-29 16:29:23", "stars": 2}, "https://github.com/mikkel/ComfyUI-text-overlay": {"author_account_age_days": 6284, "last_update": "2024-08-17 16:09:41", "stars": 57}, "https://github.com/mikkel/comfyui-mask-boundingbox": {"author_account_age_days": 6284, "last_update": "2024-05-22 21:26:23", "stars": 29}, "https://github.com/mingsky-ai/ComfyUI-MingNodes": {"author_account_age_days": 288, "last_update": "2024-10-18 16:51:14", "stars": 400}, "https://github.com/mira-6/comfyui-sasolver": {"author_account_age_days": 760, "last_update": "2025-02-23 21:44:23", "stars": 3}, "https://github.com/mirabarukaso/ComfyUI_Mira": {"author_account_age_days": 1588, "last_update": "2025-06-20 14:01:41", "stars": 130}, "https://github.com/misterjoessef/MLTask_ComfyUI": {"author_account_age_days": 1105, "last_update": "2024-08-17 16:45:24", "stars": 0}, "https://github.com/mit-han-lab/ComfyUI-nunchaku": {"author_account_age_days": 2591, "last_update": "2025-06-18 20:11:50", "stars": 1317}, "https://github.com/mittimi/ComfyUI_mittimiLoadPreset2": {"author_account_age_days": 4377, "last_update": "2025-06-13 16:52:53", "stars": 4}, "https://github.com/mittimi/ComfyUI_mittimiRecalculateSize": {"author_account_age_days": 4377, "last_update": "2024-09-07 07:43:41", "stars": 0}, "https://github.com/mittimi/ComfyUI_mittimiWidthHeight": {"author_account_age_days": 4377, "last_update": "2024-09-07 07:48:03", "stars": 1}, "https://github.com/mo230761/InsertAnything-ComfyUI-official": {"author_account_age_days": 1341, "last_update": "2025-06-04 13:23:00", "stars": 16}, "https://github.com/mobilehacker/ComfyUI_format-lora-stack": {"author_account_age_days": 4176, "last_update": "2025-04-04 19:45:39", "stars": 3}, "https://github.com/modelscope/comfyscope": {"author_account_age_days": 1067, "last_update": "2024-11-20 08:48:36", "stars": 4}, "https://github.com/modelscope/scepter": {"author_account_age_days": 1067, "last_update": "2025-04-03 06:00:15", "stars": 524}, "https://github.com/modusCell/ComfyUI-dimension-node-modusCell": {"author_account_age_days": 4971, "last_update": "2024-05-22 22:08:50", "stars": 1}, "https://github.com/mohseni-mr/ComfyUI-Mohseni-Kit": {"author_account_age_days": 1082, "last_update": "2025-02-17 07:14:46", "stars": 1}, "https://github.com/mohsensd1373/comfyui_wordpress": {"author_account_age_days": 4220, "last_update": "2025-05-08 02:25:36", "stars": 0}, "https://github.com/monkeyWie/ComfyUI-FormInput": {"author_account_age_days": 3646, "last_update": "2025-05-12 03:47:39", "stars": 0}, "https://github.com/moon7star9/ComfyUI_BiRefNet_Universal": {"author_account_age_days": 775, "last_update": "2025-02-26 03:01:29", "stars": 19}, "https://github.com/moose-lab/ComfyUI-GPT": {"author_account_age_days": 153, "last_update": "2025-04-12 07:59:29", "stars": 4}, "https://github.com/morgan55555/comfyui-lock-mode": {"author_account_age_days": 3552, "last_update": "2025-04-28 16:16:18", "stars": 0}, "https://github.com/morino-kumasan/comfyui-toml-prompt": {"author_account_age_days": 1691, "last_update": "2025-06-24 09:05:15", "stars": 0}, "https://github.com/motivated3/comfyui-shua-creator": {"author_account_age_days": 3173, "last_update": "2024-12-05 10:39:52", "stars": 6}, "https://github.com/moustafa-nasr/ComfyUI-SimpleLogger": {"author_account_age_days": 3838, "last_update": "2025-06-07 08:30:19", "stars": 4}, "https://github.com/moyi7712/ComfyUI_Seamless_Patten": {"author_account_age_days": 2668, "last_update": "2025-03-19 10:35:44", "stars": 17}, "https://github.com/mozman/ComfyUI_mozman_nodes": {"author_account_age_days": 4447, "last_update": "2024-05-22 22:13:32", "stars": 0}, "https://github.com/mr7thing/circle_pattern_processor": {"author_account_age_days": 504, "last_update": "2025-03-02 19:24:26", "stars": 0}, "https://github.com/mrchipset/ComfyUI-SaveImageS3": {"author_account_age_days": 2678, "last_update": "2025-04-07 00:27:45", "stars": 1}, "https://github.com/mrhan1993/ComfyUI-Fooocus": {"author_account_age_days": 2234, "last_update": "2025-01-15 15:18:07", "stars": 8}, "https://github.com/muhammederem/blip-comfyui": {"author_account_age_days": 2462, "last_update": "2025-05-25 14:11:04", "stars": 1}, "https://github.com/mullakhmetov/comfyui_dynamic_util_nodes": {"author_account_age_days": 4305, "last_update": "2024-07-15 14:13:58", "stars": 0}, "https://github.com/muxueChen/ComfyUI_NTCosyVoice": {"author_account_age_days": 3328, "last_update": "2025-05-20 13:36:56", "stars": 148}, "https://github.com/muzi12888/ComfyUI-PoseKeypoint-Mask": {"author_account_age_days": 3313, "last_update": "2025-03-15 00:23:20", "stars": 10}, "https://github.com/my-opencode/ComfyUI_IndustrialMagick": {"author_account_age_days": 1747, "last_update": "2024-07-31 14:04:26", "stars": 1}, "https://github.com/my-opencode/ComfyUI_KSamplerTimer": {"author_account_age_days": 1747, "last_update": "2024-07-31 14:13:17", "stars": 2}, "https://github.com/myshell-ai/ComfyUI-ShellAgent-Plugin": {"author_account_age_days": 836, "last_update": "2025-05-22 06:54:44", "stars": 22}, "https://github.com/n0neye/A3D-comfyui-integration": {"author_account_age_days": 1191, "last_update": "2025-04-28 03:54:34", "stars": 6}, "https://github.com/nagolinc/ComfyUI_FastVAEDecorder_SDXL": {"author_account_age_days": 4042, "last_update": "2024-07-19 14:46:14", "stars": 4}, "https://github.com/nagolinc/comfyui_openai_node": {"author_account_age_days": 4042, "last_update": "2024-06-15 15:59:07", "stars": 1}, "https://github.com/nako-nakoko/ComfyUI_Mel_Nodes": {"author_account_age_days": 83, "last_update": "2025-04-26 22:48:50", "stars": 0}, "https://github.com/narusas/Comfyui-Logic-Support": {"author_account_age_days": 5000, "last_update": "2025-05-30 04:44:16", "stars": 0}, "https://github.com/nat-chan/ComfyUI-graphToPrompt": {"author_account_age_days": 3361, "last_update": "2024-05-23 01:16:40", "stars": 2}, "https://github.com/nat-chan/comfyui-transceiver": {"author_account_age_days": 3361, "last_update": "2024-05-23 01:16:28", "stars": 5}, "https://github.com/nathannlu/ComfyUI-Cloud": {"author_account_age_days": 3093, "last_update": "2024-07-31 18:05:55", "stars": 199}, "https://github.com/nathannlu/ComfyUI-Pets": {"author_account_age_days": 3093, "last_update": "2024-06-14 11:00:42", "stars": 47}, "https://github.com/natto-maki/ComfyUI-NegiTools": {"author_account_age_days": 645, "last_update": "2024-09-15 05:11:18", "stars": 31}, "https://github.com/nchenevey1/comfyui-gimp-nodes": {"author_account_age_days": 1017, "last_update": "2024-10-26 09:11:34", "stars": 9}, "https://github.com/neggo/comfyui-sambanova": {"author_account_age_days": 4302, "last_update": "2025-05-15 01:49:53", "stars": 0}, "https://github.com/neocrz/comfyui-usetaesd": {"author_account_age_days": 1686, "last_update": "2025-06-14 18:58:39", "stars": 0}, "https://github.com/neph1/comfyui-smooth-step-lora-loader": {"author_account_age_days": 4019, "last_update": "2025-04-06 10:43:14", "stars": 6}, "https://github.com/neverbiasu/ComfyUI-BAGEL": {"author_account_age_days": 1386, "last_update": "2025-06-19 18:12:50", "stars": 162}, "https://github.com/neverbiasu/ComfyUI-ChatTTS": {"author_account_age_days": 1386, "last_update": "2025-05-12 08:15:13", "stars": 3}, "https://github.com/neverbiasu/ComfyUI-Dashscope": {"author_account_age_days": 1386, "last_update": "2025-04-05 02:19:36", "stars": 2}, "https://github.com/neverbiasu/ComfyUI-Image-Captioner": {"author_account_age_days": 1386, "last_update": "2025-05-12 16:09:03", "stars": 13}, "https://github.com/neverbiasu/ComfyUI-SAM2": {"author_account_age_days": 1386, "last_update": "2025-05-13 12:38:09", "stars": 185}, "https://github.com/neverbiasu/ComfyUI-StyleShot": {"author_account_age_days": 1386, "last_update": "2025-04-23 08:01:32", "stars": 12}, "https://github.com/newtextdoc1111/ComfyUI-Autocomplete-Plus": {"author_account_age_days": 103, "last_update": "2025-06-14 08:51:48", "stars": 17}, "https://github.com/ngosset/ComfyUI-ImageSimilarity": {"author_account_age_days": 4689, "last_update": "2025-01-18 18:17:50", "stars": 7}, "https://github.com/nicehero/comfyui-SegGPT": {"author_account_age_days": 4377, "last_update": "2024-08-26 06:05:35", "stars": 5}, "https://github.com/nickve28/ComfyUI-Nich-Utils": {"author_account_age_days": 4396, "last_update": "2025-06-19 10:15:26", "stars": 8}, "https://github.com/nicofdga/DZ-FaceDetailer": {"author_account_age_days": 1599, "last_update": "2024-06-17 10:00:30", "stars": 202}, "https://github.com/niknah/ComfyUI-F5-TTS": {"author_account_age_days": 5087, "last_update": "2025-06-13 12:27:03", "stars": 204}, "https://github.com/niknah/ComfyUI-Hunyuan-3D-2": {"author_account_age_days": 5087, "last_update": "2025-06-16 11:23:16", "stars": 49}, "https://github.com/niknah/ComfyUI-InfiniteYou": {"author_account_age_days": 5087, "last_update": "2025-04-16 08:44:22", "stars": 12}, "https://github.com/niknah/audio-general-ComfyUI": {"author_account_age_days": 5087, "last_update": "2025-05-28 02:51:53", "stars": 0}, "https://github.com/niknah/quick-connections": {"author_account_age_days": 5087, "last_update": "2025-03-27 22:16:29", "stars": 282}, "https://github.com/nilor-corp/nilor-nodes": {"author_account_age_days": 582, "last_update": "2025-05-20 14:44:55", "stars": 3}, "https://github.com/ningxiaoxiao/comfyui-NDI": {"author_account_age_days": 3355, "last_update": "2025-04-11 03:55:37", "stars": 59}, "https://github.com/nirbhay-faaya/ImgProcessing_ComfyUI": {"author_account_age_days": 703, "last_update": "2024-07-31 08:34:48", "stars": 0}, "https://github.com/nirex0/ComfyUI_pytorch_openpose": {"author_account_age_days": 3870, "last_update": "2024-06-14 12:01:07", "stars": 2}, "https://github.com/nisaruj/comfyui-daam": {"author_account_age_days": 3575, "last_update": "2025-06-08 12:41:49", "stars": 21}, "https://github.com/nisimjoseph/ComfyUI_OpenAI-Prompter": {"author_account_age_days": 4674, "last_update": "2025-01-18 19:57:31", "stars": 4}, "https://github.com/nkchocoai/ComfyUI-DanbooruPromptQuiz": {"author_account_age_days": 527, "last_update": "2025-03-30 08:30:33", "stars": 0}, "https://github.com/nkchocoai/ComfyUI-Dart": {"author_account_age_days": 527, "last_update": "2025-03-30 08:19:01", "stars": 26}, "https://github.com/nkchocoai/ComfyUI-PromptUtilities": {"author_account_age_days": 527, "last_update": "2025-03-30 08:19:25", "stars": 19}, "https://github.com/nkchocoai/ComfyUI-SaveImageWithMetaData": {"author_account_age_days": 527, "last_update": "2025-03-30 08:19:20", "stars": 80}, "https://github.com/nkchocoai/ComfyUI-SizeFromPresets": {"author_account_age_days": 527, "last_update": "2025-03-30 08:19:30", "stars": 7}, "https://github.com/nkchocoai/ComfyUI-TextOnSegs": {"author_account_age_days": 527, "last_update": "2025-03-30 08:19:45", "stars": 12}, "https://github.com/nobrainX2/comfyUI-customDia": {"author_account_age_days": 2154, "last_update": "2025-05-29 18:32:25", "stars": 13}, "https://github.com/noembryo/ComfyUI-noEmbryo": {"author_account_age_days": 3110, "last_update": "2025-05-11 19:04:36", "stars": 25}, "https://github.com/nofunstudio/Node_Fun_ComfyUI": {"author_account_age_days": 1580, "last_update": "2025-06-25 20:44:43", "stars": 2}, "https://github.com/nonnonstop/comfyui-faster-loading": {"author_account_age_days": 2489, "last_update": "2024-06-13 15:37:45", "stars": 10}, "https://github.com/northumber/ComfyUI-northTools": {"author_account_age_days": 3427, "last_update": "2025-05-22 18:08:04", "stars": 2}, "https://github.com/nosiu/comfyui-instantId-faceswap": {"author_account_age_days": 4272, "last_update": "2025-03-03 19:02:51", "stars": 237}, "https://github.com/nosiu/comfyui-text-randomizer": {"author_account_age_days": 4272, "last_update": "2025-03-03 01:40:12", "stars": 0}, "https://github.com/noxinias/ComfyUI_NoxinNodes": {"author_account_age_days": 2928, "last_update": "2024-05-22 21:24:24", "stars": 11}, "https://github.com/nsdtcloud3d/ComfyUI-3D-Convert": {"author_account_age_days": 442, "last_update": "2024-12-23 07:46:17", "stars": 13}, "https://github.com/ntc-ai/ComfyUI-DARE-LoRA-Merge": {"author_account_age_days": 2085, "last_update": "2024-05-22 22:22:14", "stars": 33}, "https://github.com/nuanarchy/ComfyUI-NuA-BIRD": {"author_account_age_days": 1459, "last_update": "2024-06-18 05:35:49", "stars": 8}, "https://github.com/nuanarchy/ComfyUI-NuA-FlashFace": {"author_account_age_days": 1459, "last_update": "2024-07-31 13:54:00", "stars": 24}, "https://github.com/nullquant/ComfyUI-BrushNet": {"author_account_age_days": 1544, "last_update": "2025-03-31 08:45:34", "stars": 888}, "https://github.com/numz/ComfyUI-FlowChain": {"author_account_age_days": 5151, "last_update": "2025-05-19 01:51:02", "stars": 151}, "https://github.com/numz/ComfyUI-SeedVR2_VideoUpscaler": {"author_account_age_days": 5151, "last_update": "2025-06-24 15:14:59", "stars": 86}, "https://github.com/numz/Comfyui-Orpheus": {"author_account_age_days": 5151, "last_update": "2025-04-16 19:20:21", "stars": 8}, "https://github.com/nux1111/ComfyUI_NetDist_Plus": {"author_account_age_days": 920, "last_update": "2024-08-27 23:15:18", "stars": 31}, "https://github.com/o-l-l-i/ComfyUI-Olm-CurveEditor": {"author_account_age_days": 2839, "last_update": "2025-06-24 17:41:52", "stars": 19}, "https://github.com/o-l-l-i/ComfyUI-Olm-Resolution-Picker": {"author_account_age_days": 2839, "last_update": "2025-06-24 18:05:33", "stars": 9}, "https://github.com/o-l-l-i/ComfyUI-OlmLUT": {"author_account_age_days": 2839, "last_update": "2025-06-09 18:55:46", "stars": 6}, "https://github.com/okgo4/ComfyUI-Mosaic-Mask": {"author_account_age_days": 3054, "last_update": "2025-04-03 09:41:53", "stars": 6}, "https://github.com/olduvai-jp/ComfyUI-CloudArchive": {"author_account_age_days": 1243, "last_update": "2025-04-15 07:18:38", "stars": 2}, "https://github.com/olduvai-jp/ComfyUI-HfLoader": {"author_account_age_days": 1243, "last_update": "2025-02-13 17:05:40", "stars": 4}, "https://github.com/oleksandr612/ComfyUI-Counter": {"author_account_age_days": 330, "last_update": "2024-08-05 16:18:48", "stars": 0}, "https://github.com/olivv-cs/ComfyUI-FunPack": {"author_account_age_days": 779, "last_update": "2025-06-13 19:02:21", "stars": 1}, "https://github.com/omar92/ComfyUI-QualityOfLifeSuit_Omar92": {"author_account_age_days": 4874, "last_update": "2024-09-10 14:16:30", "stars": 156}, "https://github.com/openvino-dev-samples/comfyui_openvino": {"author_account_age_days": 1372, "last_update": "2025-06-17 06:19:44", "stars": 4}, "https://github.com/opvelll/ComfyUI_TextListProduct": {"author_account_age_days": 1922, "last_update": "2024-10-30 16:00:09", "stars": 1}, "https://github.com/orange90/ComfyUI-Regex-Runner": {"author_account_age_days": 4464, "last_update": "2025-02-26 03:48:27", "stars": 3}, "https://github.com/orex2121/comfyui-OreX": {"author_account_age_days": 1874, "last_update": "2025-04-21 04:40:56", "stars": 5}, "https://github.com/orion4d/ComfyUI-Image-Effects": {"author_account_age_days": 948, "last_update": "2025-05-28 00:37:16", "stars": 20}, "https://github.com/orion4d/ComfyUI_extract_imag": {"author_account_age_days": 948, "last_update": "2025-06-11 13:37:18", "stars": 0}, "https://github.com/orion4d/ComfyUI_pdf_nodes": {"author_account_age_days": 948, "last_update": "2025-06-10 15:51:53", "stars": 0}, "https://github.com/orion4d/illusion_node": {"author_account_age_days": 948, "last_update": "2025-06-22 08:57:01", "stars": 0}, "https://github.com/orssorbit/ComfyUI-wanBlockswap": {"author_account_age_days": 3396, "last_update": "2025-03-19 12:56:23", "stars": 41}, "https://github.com/oshtz/ComfyUI-oshtz-nodes": {"author_account_age_days": 793, "last_update": "2025-05-22 09:55:47", "stars": 5}, "https://github.com/osi1880vr/prompt_quill_comfyui": {"author_account_age_days": 1444, "last_update": "2025-01-27 10:43:16", "stars": 18}, "https://github.com/ostris/ComfyUI-FlexTools": {"author_account_age_days": 2770, "last_update": "2025-04-21 23:12:58", "stars": 68}, "https://github.com/ostris/ostris_nodes_comfyui": {"author_account_age_days": 2770, "last_update": "2025-04-16 17:03:53", "stars": 30}, "https://github.com/otacoo/comfyui_otacoo": {"author_account_age_days": 64, "last_update": "2025-06-05 23:09:32", "stars": 2}, "https://github.com/ownimage/ComfyUI-ownimage": {"author_account_age_days": 3150, "last_update": "2024-05-22 22:22:37", "stars": 0}, "https://github.com/oxysoft/ComfyUI-gowiththeflow": {"author_account_age_days": 4479, "last_update": "2025-04-09 03:55:00", "stars": 3}, "https://github.com/oyvindg/ComfyUI-TrollSuite": {"author_account_age_days": 2697, "last_update": "2024-08-15 10:37:43", "stars": 4}, "https://github.com/oztrkoguz/ComfyUI_StoryCreator": {"author_account_age_days": 1209, "last_update": "2025-04-07 08:30:38", "stars": 29}, "https://github.com/p1atdev/comfyui-timm-backbone": {"author_account_age_days": 1982, "last_update": "2025-05-31 04:03:07", "stars": 1}, "https://github.com/palant/image-resize-comfyui": {"author_account_age_days": 5420, "last_update": "2024-01-18 20:59:55", "stars": 95}, "https://github.com/palant/integrated-nodes-comfyui": {"author_account_age_days": 5420, "last_update": "2023-12-27 22:52:00", "stars": 39}, "https://github.com/pamparamm/ComfyUI-ppm": {"author_account_age_days": 2498, "last_update": "2025-06-13 16:23:47", "stars": 196}, "https://github.com/pamparamm/ComfyUI-vectorscope-cc": {"author_account_age_days": 2498, "last_update": "2025-02-24 21:59:04", "stars": 19}, "https://github.com/pamparamm/sd-perturbed-attention": {"author_account_age_days": 2498, "last_update": "2025-06-24 00:09:09", "stars": 262}, "https://github.com/pants007/comfy-pants": {"author_account_age_days": 2684, "last_update": "2024-05-22 18:16:04", "stars": 2}, "https://github.com/papcorns/ComfyUI-Papcorns-Node-LoadImageFromUrl": {"author_account_age_days": 1879, "last_update": "2025-05-26 12:33:08", "stars": 1}, "https://github.com/pathway8-sudo/ComfyUI-Pathway-CutPNG-Node": {"author_account_age_days": 205, "last_update": "2025-03-03 07:47:31", "stars": 0}, "https://github.com/patriciogonzalezvivo/comfyui_glslnodes": {"author_account_age_days": 5447, "last_update": "2025-05-05 15:00:47", "stars": 220}, "https://github.com/paulo-coronado/comfy_clip_blip_node": {"author_account_age_days": 3054, "last_update": "2024-05-22 17:39:09", "stars": 29}, "https://github.com/pawelmal0101/ComfyUI-Webhook": {"author_account_age_days": 1028, "last_update": "2025-06-11 10:36:58", "stars": 0}, "https://github.com/pbpbpb2705/ComfyUI-LyraVSIH": {"author_account_age_days": 1597, "last_update": "2024-08-30 07:52:11", "stars": 0}, "https://github.com/penposs/ComfyUI_Gemini_Pro": {"author_account_age_days": 2125, "last_update": "2025-04-24 07:54:42", "stars": 7}, "https://github.com/penposs/Comfyui_wan_api": {"author_account_age_days": 2125, "last_update": "2025-04-02 16:02:44", "stars": 1}, "https://github.com/pharmapsychotic/comfy-cliption": {"author_account_age_days": 1282, "last_update": "2025-01-04 05:06:11", "stars": 52}, "https://github.com/phazei/ComfyUI-Prompt-Stash": {"author_account_age_days": 5376, "last_update": "2025-05-16 02:13:34", "stars": 13}, "https://github.com/philiprodriguez/ComfyUI-HunyuanImageLatentToVideoLatent": {"author_account_age_days": 3366, "last_update": "2025-01-12 16:43:09", "stars": 1}, "https://github.com/philipy1219/ComfyUI-TaylorSeer": {"author_account_age_days": 3626, "last_update": "2025-05-25 09:35:25", "stars": 26}, "https://github.com/philz1337x/ComfyUI-ClarityAI": {"author_account_age_days": 1031, "last_update": "2025-04-24 09:51:25", "stars": 182}, "https://github.com/phineas-pta/comfyui-auto-nodes-layout": {"author_account_age_days": 2656, "last_update": "2024-08-02 17:31:24", "stars": 46}, "https://github.com/phuvinh010701/ComfyUI-Nudenet": {"author_account_age_days": 2041, "last_update": "2025-05-01 01:46:07", "stars": 24}, "https://github.com/phyblas/paint-by-example_comfyui": {"author_account_age_days": 3420, "last_update": "2025-03-28 22:27:45", "stars": 9}, "https://github.com/pictorialink/ComfyUI-Custom-Node-Config": {"author_account_age_days": 43, "last_update": "2025-06-13 02:46:08", "stars": 0}, "https://github.com/pictorialink/ComfyUI-Text-Translation": {"author_account_age_days": 43, "last_update": "2025-06-06 02:56:11", "stars": 2}, "https://github.com/picturesonpictures/comfy_PoP": {"author_account_age_days": 954, "last_update": "2025-06-05 03:53:18", "stars": 21}, "https://github.com/pikenrover/ComfyUI_PRNodes": {"author_account_age_days": 337, "last_update": "2025-04-03 13:31:42", "stars": 2}, "https://github.com/pixelworldai/ComfyUI-AlphaFlatten": {"author_account_age_days": 339, "last_update": "2025-03-13 23:07:04", "stars": 1}, "https://github.com/pkpkTech/ComfyUI-SaveAVIF": {"author_account_age_days": 1870, "last_update": "2025-02-01 16:29:22", "stars": 2}, "https://github.com/pkpkTech/ComfyUI-SaveQueues": {"author_account_age_days": 1870, "last_update": "2024-05-22 22:19:54", "stars": 5}, "https://github.com/pkpkTech/ComfyUI-TemporaryLoader": {"author_account_age_days": 1870, "last_update": "2024-05-22 22:19:44", "stars": 2}, "https://github.com/pkpkTech/ComfyUI-ngrok": {"author_account_age_days": 1870, "last_update": "2024-05-22 22:19:32", "stars": 5}, "https://github.com/playbook3d/playbook3d-comfyui-nodes": {"author_account_age_days": 1868, "last_update": "2025-03-25 19:50:08", "stars": 21}, "https://github.com/plugcrypt/CRT-Nodes": {"author_account_age_days": 1430, "last_update": "2025-06-20 04:32:25", "stars": 5}, "https://github.com/pnikolic-amd/ComfyUI_MIGraphX": {"author_account_age_days": 168, "last_update": "2025-06-24 12:55:12", "stars": 5}, "https://github.com/pollockjj/ComfyUI-MultiGPU": {"author_account_age_days": 3874, "last_update": "2025-04-17 23:43:02", "stars": 324}, "https://github.com/portu-sim/comfyui_bmab": {"author_account_age_days": 689, "last_update": "2025-02-23 12:32:27", "stars": 107}, "https://github.com/prodogape/ComfyUI-EasyOCR": {"author_account_age_days": 1397, "last_update": "2024-08-05 07:03:20", "stars": 36}, "https://github.com/prodogape/ComfyUI-Minio": {"author_account_age_days": 1397, "last_update": "2024-05-23 00:13:38", "stars": 6}, "https://github.com/prodogape/ComfyUI-OmDet": {"author_account_age_days": 1397, "last_update": "2024-06-14 13:01:34", "stars": 3}, "https://github.com/prodogape/Comfyui-Yolov8-JSON": {"author_account_age_days": 1397, "last_update": "2024-08-28 02:10:39", "stars": 24}, "https://github.com/prozacgod/comfyui-pzc-multiworkspace": {"author_account_age_days": 5935, "last_update": "2024-05-22 23:11:46", "stars": 7}, "https://github.com/pschroedl/ComfyUI-SAM2-Realtime": {"author_account_age_days": 4355, "last_update": "2025-01-21 05:29:03", "stars": 14}, "https://github.com/ptmaster/comfyui-audio-speed": {"author_account_age_days": 4247, "last_update": "2025-06-24 15:23:03", "stars": 6}, "https://github.com/puke3615/ComfyUI-OneAPI": {"author_account_age_days": 3881, "last_update": "2025-06-26 11:14:43", "stars": 1}, "https://github.com/pupba/Comfy_ForEach": {"author_account_age_days": 2168, "last_update": "2025-05-12 07:08:54", "stars": 1}, "https://github.com/purewater2011/comfyui_color_detection": {"author_account_age_days": 4195, "last_update": "2025-05-19 09:59:44", "stars": 1}, "https://github.com/purpen/ComfyUI-AIRedoon": {"author_account_age_days": 5306, "last_update": "2024-12-11 09:38:42", "stars": 2}, "https://github.com/purpen/ComfyUI-ImageTagger": {"author_account_age_days": 5306, "last_update": "2024-11-27 17:20:49", "stars": 2}, "https://github.com/pxl-pshr/GlitchNodes": {"author_account_age_days": 253, "last_update": "2025-06-19 22:39:20", "stars": 49}, "https://github.com/pydn/ComfyUI-to-Python-Extension": {"author_account_age_days": 3064, "last_update": "2025-01-14 17:03:18", "stars": 1859}, "https://github.com/pythongosssss/ComfyUI-Custom-Scripts": {"author_account_age_days": 865, "last_update": "2025-04-30 12:00:10", "stars": 2511}, "https://github.com/pythongosssss/ComfyUI-WD14-Tagger": {"author_account_age_days": 865, "last_update": "2025-05-04 08:39:13", "stars": 887}, "https://github.com/pzc163/Comfyui-CatVTON": {"author_account_age_days": 1144, "last_update": "2024-10-03 12:50:42", "stars": 164}, "https://github.com/pzc163/Comfyui_MiniCPMv2_6-prompt-generator": {"author_account_age_days": 1144, "last_update": "2024-08-30 08:37:48", "stars": 79}, "https://github.com/quank123wip/ComfyUI-Step1X-Edit": {"author_account_age_days": 2871, "last_update": "2025-04-30 11:03:51", "stars": 75}, "https://github.com/quasiblob/ComfyUI-EsesCompositionGuides": {"author_account_age_days": 3655, "last_update": "2025-06-22 13:43:41", "stars": 5}, "https://github.com/quasiblob/ComfyUI-EsesImageAdjustments": {"author_account_age_days": 3655, "last_update": "2025-06-20 17:34:19", "stars": 26}, "https://github.com/quasiblob/ComfyUI-EsesImageOffset": {"author_account_age_days": 3655, "last_update": "2025-06-26 12:56:51", "stars": 1}, "https://github.com/qwixiwp/queuetools": {"author_account_age_days": 978, "last_update": "2024-06-14 10:27:57", "stars": 0}, "https://github.com/r-vage/ComfyUI-RvTools_v2": {"author_account_age_days": 39, "last_update": "2025-06-10 06:43:50", "stars": 4}, "https://github.com/r3dial/redial-discomphy": {"author_account_age_days": 799, "last_update": "2025-01-09 19:59:31", "stars": 1}, "https://github.com/r3dsd/comfyui-template-loader": {"author_account_age_days": 508, "last_update": "2025-01-12 08:55:49", "stars": 0}, "https://github.com/raindrop313/ComfyUI-WanVideoStartEndFrames": {"author_account_age_days": 1433, "last_update": "2025-03-22 09:59:11", "stars": 353}, "https://github.com/raindrop313/ComfyUI_SD3_Flowedit": {"author_account_age_days": 1433, "last_update": "2025-02-06 19:02:52", "stars": 6}, "https://github.com/rainlizard/ComfyUI-Raffle": {"author_account_age_days": 3530, "last_update": "2025-06-14 23:56:29", "stars": 4}, "https://github.com/rakki194/ComfyUI-ImageCompare": {"author_account_age_days": 145, "last_update": "2025-05-05 21:00:58", "stars": 0}, "https://github.com/ramesh-x90/ComfyUI_pyannote": {"author_account_age_days": 1689, "last_update": "2024-11-23 09:42:16", "stars": 3}, "https://github.com/ramyma/A8R8_ComfyUI_nodes": {"author_account_age_days": 3584, "last_update": "2024-12-09 16:06:25", "stars": 62}, "https://github.com/randjtw/advance-aesthetic-score": {"author_account_age_days": 1128, "last_update": "2024-05-23 01:14:47", "stars": 0}, "https://github.com/randomnoner11/ComfyUI-MistralAI-API": {"author_account_age_days": 187, "last_update": "2025-04-07 17:34:06", "stars": 1}, "https://github.com/ratulrafsan/Comfyui-SAL-VTON": {"author_account_age_days": 4861, "last_update": "2024-08-26 09:52:06", "stars": 86}, "https://github.com/raykindle/ComfyUI_Step1X-Edit": {"author_account_age_days": 2290, "last_update": "2025-05-06 02:01:37", "stars": 47}, "https://github.com/raysers/Mflux-ComfyUI": {"author_account_age_days": 2372, "last_update": "2025-03-09 21:14:27", "stars": 94}, "https://github.com/rcfcu2000/zhihuige-nodes-comfyui": {"author_account_age_days": 3792, "last_update": "2024-05-22 22:13:55", "stars": 1}, "https://github.com/rcsaquino/comfyui-custom-nodes": {"author_account_age_days": 1878, "last_update": "2024-08-26 10:08:29", "stars": 1}, "https://github.com/rdancer/ComfyUI_Florence2SAM2": {"author_account_age_days": 5988, "last_update": "2025-03-14 10:49:55", "stars": 37}, "https://github.com/receyuki/comfyui-prompt-reader-node": {"author_account_age_days": 2960, "last_update": "2025-02-01 15:56:44", "stars": 366}, "https://github.com/recraft-ai/ComfyUI-RecraftAI": {"author_account_age_days": 1090, "last_update": "2025-06-04 11:33:13", "stars": 61}, "https://github.com/redhottensors/ComfyUI-Prediction": {"author_account_age_days": 507, "last_update": "2024-07-14 21:19:01", "stars": 14}, "https://github.com/regiellis/ComfyUI-EasyColorCorrector": {"author_account_age_days": 4993, "last_update": "2025-06-26 01:38:40", "stars": 44}, "https://github.com/regiellis/ComfyUI-EasyNoobai": {"author_account_age_days": 4994, "last_update": "2025-05-12 14:17:10", "stars": 28}, "https://github.com/regiellis/ComfyUI-EasyPony": {"author_account_age_days": 4994, "last_update": "2025-04-05 15:15:29", "stars": 9}, "https://github.com/replicate/comfyui-replicate": {"author_account_age_days": 1976, "last_update": "2024-11-05 15:26:20", "stars": 185}, "https://github.com/revirevy/Comfyui_saveimage_imgbb": {"author_account_age_days": 4852, "last_update": "2025-04-23 10:49:48", "stars": 1}, "https://github.com/rgthree/rgthree-comfy": {"author_account_age_days": 5342, "last_update": "2025-06-23 04:15:47", "stars": 2003}, "https://github.com/rhdunn/comfyui-audio-processing": {"author_account_age_days": 6004, "last_update": "2024-08-22 19:11:01", "stars": 8}, "https://github.com/rhdunn/comfyui-bus-plugin": {"author_account_age_days": 6004, "last_update": "2024-08-22 19:00:56", "stars": 2}, "https://github.com/rhplus0831/ComfyMepi": {"author_account_age_days": 520, "last_update": "2025-04-12 22:59:21", "stars": 0}, "https://github.com/richinsley/Comfy-LFO": {"author_account_age_days": 3048, "last_update": "2024-05-22 20:46:30", "stars": 5}, "https://github.com/ricklove/comfyui-ricklove": {"author_account_age_days": 5200, "last_update": "2024-10-05 03:12:28", "stars": 1}, "https://github.com/rickyars/comfyui-llm-tile": {"author_account_age_days": 4571, "last_update": "2025-06-25 11:56:15", "stars": 1}, "https://github.com/risunobushi/ComfyUI-Similarity-Score": {"author_account_age_days": 1014, "last_update": "2025-01-03 15:27:06", "stars": 4}, "https://github.com/risunobushi/ComfyUI_DisplacementMapTools": {"author_account_age_days": 1014, "last_update": "2025-01-29 18:06:41", "stars": 3}, "https://github.com/risunobushi/comfyUI_FrequencySeparation_RGB-HSV": {"author_account_age_days": 1014, "last_update": "2024-06-14 10:28:04", "stars": 36}, "https://github.com/rkfg/ComfyUI-Dia_tts": {"author_account_age_days": 5639, "last_update": "2025-04-27 15:58:21", "stars": 0}, "https://github.com/rnbwdsh/ComfyUI-LatentWalk": {"author_account_age_days": 3914, "last_update": "2024-08-20 22:39:19", "stars": 13}, "https://github.com/robertvoy/ComfyUI-Flux-Continuum": {"author_account_age_days": 4472, "last_update": "2025-06-20 01:23:28", "stars": 196}, "https://github.com/robin-collins/ComfyUI-TechsToolz": {"author_account_age_days": 1491, "last_update": "2025-06-20 00:25:39", "stars": 0}, "https://github.com/robtl2/ComfyUI-ComfyBridge": {"author_account_age_days": 819, "last_update": "2024-11-18 23:28:13", "stars": 0}, "https://github.com/rohitsainier/ComfyUI-InstagramDownloader": {"author_account_age_days": 3521, "last_update": "2025-01-02 08:47:22", "stars": 18}, "https://github.com/romeobuilderotti/ComfyUI-PNG-Metadata": {"author_account_age_days": 658, "last_update": "2024-05-22 21:29:25", "stars": 7}, "https://github.com/ronaldzgithub/ComfyUI_Appstore": {"author_account_age_days": 2677, "last_update": "2024-12-04 15:02:42", "stars": 5}, "https://github.com/ronniebasak/ComfyUI-Tara-LLM-Integration": {"author_account_age_days": 4522, "last_update": "2024-11-18 05:08:11", "stars": 107}, "https://github.com/ronsantash/Comfyui-flexi-lora-loader": {"author_account_age_days": 1385, "last_update": "2025-01-12 11:57:27", "stars": 8}, "https://github.com/rookiepsi/comfypsi_blur_mask": {"author_account_age_days": 124, "last_update": "2025-06-25 15:13:33", "stars": 0}, "https://github.com/rookiepsi/comfyui-extended": {"author_account_age_days": 124, "last_update": "2025-06-22 13:42:26", "stars": 2}, "https://github.com/roundyyy/ComfyUI-mesh-simplifier": {"author_account_age_days": 1295, "last_update": "2025-03-09 23:39:24", "stars": 6}, "https://github.com/royceschultz/ComfyUI-Notifications": {"author_account_age_days": 2897, "last_update": "2025-04-23 01:40:31", "stars": 15}, "https://github.com/royceschultz/ComfyUI-TranscriptionTools": {"author_account_age_days": 2897, "last_update": "2025-04-23 00:52:31", "stars": 22}, "https://github.com/rubi-du/ComfyUI-BiRefNet-Super": {"author_account_age_days": 549, "last_update": "2025-05-21 02:21:09", "stars": 10}, "https://github.com/rubi-du/ComfyUI-Flux-Inpainting": {"author_account_age_days": 549, "last_update": "2025-05-14 06:09:10", "stars": 36}, "https://github.com/rubi-du/ComfyUI-ICC-nodes": {"author_account_age_days": 549, "last_update": "2025-05-14 06:10:11", "stars": 2}, "https://github.com/rubi-du/ComfyUI-MaskEditor-Extension": {"author_account_age_days": 549, "last_update": "2025-05-14 06:09:43", "stars": 9}, "https://github.com/rui40000/RUI-Nodes": {"author_account_age_days": 841, "last_update": "2024-05-22 22:12:26", "stars": 16}, "https://github.com/ruiqutech/ComfyUI-RuiquNodes": {"author_account_age_days": 437, "last_update": "2024-05-23 01:21:50", "stars": 0}, "https://github.com/runtime44/comfyui_r44_nodes": {"author_account_age_days": 535, "last_update": "2024-07-01 08:02:04", "stars": 41}, "https://github.com/ruucm/ruucm-comfy": {"author_account_age_days": 2788, "last_update": "2025-04-21 15:20:57", "stars": 2}, "https://github.com/ryanontheinside/ComfyUI-DeepLiveCam": {"author_account_age_days": 4057, "last_update": "2025-05-26 14:26:57", "stars": 7}, "https://github.com/ryanontheinside/ComfyUI_ControlFreak": {"author_account_age_days": 4057, "last_update": "2025-04-13 23:18:36", "stars": 14}, "https://github.com/ryanontheinside/ComfyUI_Doom": {"author_account_age_days": 4057, "last_update": "2024-11-08 17:58:21", "stars": 5}, "https://github.com/ryanontheinside/ComfyUI_EfficientTAM": {"author_account_age_days": 4057, "last_update": "2024-12-21 20:25:05", "stars": 3}, "https://github.com/ryanontheinside/ComfyUI_ProfilerX": {"author_account_age_days": 4057, "last_update": "2025-05-27 22:10:23", "stars": 60}, "https://github.com/ryanontheinside/ComfyUI_RealtimeNodes": {"author_account_age_days": 4057, "last_update": "2025-06-19 14:20:29", "stars": 58}, "https://github.com/ryanontheinside/ComfyUI_RyanOnTheInside": {"author_account_age_days": 4057, "last_update": "2025-06-01 22:34:26", "stars": 514}, "https://github.com/ryanontheinside/ComfyUI_SuperResolution": {"author_account_age_days": 4057, "last_update": "2025-04-07 17:53:16", "stars": 8}, "https://github.com/s9roll7/comfyui_cotracker_node": {"author_account_age_days": 952, "last_update": "2025-06-24 11:30:06", "stars": 13}, "https://github.com/saftle/uber_comfy_nodes": {"author_account_age_days": 5125, "last_update": "2024-08-24 02:42:40", "stars": 1}, "https://github.com/sakura1bgx/ComfyUI_FlipStreamViewer": {"author_account_age_days": 306, "last_update": "2025-05-31 01:17:30", "stars": 4}, "https://github.com/sanbuphy/ComfyUI-AudioLDM": {"author_account_age_days": 1289, "last_update": "2025-01-02 02:01:12", "stars": 0}, "https://github.com/santiagosamuel3455/ComfyUI-GeminiImageToPrompt": {"author_account_age_days": 318, "last_update": "2025-05-04 04:58:56", "stars": 1}, "https://github.com/scraed/LanPaint": {"author_account_age_days": 3829, "last_update": "2025-06-21 06:19:09", "stars": 373}, "https://github.com/sdfxai/SDFXBridgeForComfyUI": {"author_account_age_days": 602, "last_update": "2024-06-14 10:26:56", "stars": 11}, "https://github.com/seanlynch/comfyui-optical-flow": {"author_account_age_days": 5673, "last_update": "2024-05-22 20:52:17", "stars": 32}, "https://github.com/seanlynch/srl-nodes": {"author_account_age_days": 5673, "last_update": "2024-06-30 13:47:38", "stars": 8}, "https://github.com/sebord/ComfyUI-LMCQ": {"author_account_age_days": 1155, "last_update": "2025-06-24 15:43:00", "stars": 72}, "https://github.com/sergekatzmann/ComfyUI_Nimbus-Pack": {"author_account_age_days": 3700, "last_update": "2024-05-22 21:34:15", "stars": 4}, "https://github.com/set-soft/ComfyUI-AudioBatch": {"author_account_age_days": 3177, "last_update": "2025-06-04 12:59:15", "stars": 0}, "https://github.com/sh570655308/ComfyUI-GigapixelAI": {"author_account_age_days": 2869, "last_update": "2025-01-15 05:16:31", "stars": 150}, "https://github.com/sh570655308/ComfyUI-TopazVideoAI": {"author_account_age_days": 2869, "last_update": "2025-04-23 08:54:20", "stars": 216}, "https://github.com/shabri-arrahim/ComfyUI-Safety-Checker": {"author_account_age_days": 2137, "last_update": "2025-01-23 05:46:33", "stars": 1}, "https://github.com/shadowcz007/comfyui-Image-reward": {"author_account_age_days": 3682, "last_update": "2024-06-14 10:24:49", "stars": 31}, "https://github.com/shadowcz007/comfyui-consistency-decoder": {"author_account_age_days": 3682, "last_update": "2024-06-14 10:23:35", "stars": 2}, "https://github.com/shadowcz007/comfyui-edit-mask": {"author_account_age_days": 3682, "last_update": "2024-06-20 01:42:48", "stars": 6}, "https://github.com/shadowcz007/comfyui-liveportrait": {"author_account_age_days": 2410, "last_update": "2024-09-01 10:34:41", "stars": 458}, "https://github.com/shadowcz007/comfyui-mixlab-nodes": {"author_account_age_days": 2410, "last_update": "2025-02-05 10:24:45", "stars": 1643}, "https://github.com/shadowcz007/comfyui-sound-lab": {"author_account_age_days": 2410, "last_update": "2024-07-04 12:53:38", "stars": 120}, "https://github.com/shadowcz007/comfyui-try-on": {"author_account_age_days": 2410, "last_update": "2024-08-15 10:50:22", "stars": 13}, "https://github.com/shadowcz007/comfyui-ultralytics-yolo": {"author_account_age_days": 3682, "last_update": "2024-06-22 09:06:04", "stars": 33}, "https://github.com/shahkoorosh/ComfyUI-KGnodes": {"author_account_age_days": 560, "last_update": "2025-05-23 17:41:55", "stars": 3}, "https://github.com/shahkoorosh/ComfyUI-PersianText": {"author_account_age_days": 560, "last_update": "2025-05-23 17:43:33", "stars": 19}, "https://github.com/shenduldh/ComfyUI-Lightning": {"author_account_age_days": 2486, "last_update": "2025-03-13 05:58:04", "stars": 206}, "https://github.com/shi3z/ComfyUI_Memeplex_DALLE": {"author_account_age_days": 5460, "last_update": "2024-05-23 00:14:25", "stars": 2}, "https://github.com/shiertier/ComfyUI-TeaCache-lumina2": {"author_account_age_days": 1388, "last_update": "2025-06-03 10:09:06", "stars": 1}, "https://github.com/shiimizu/ComfyUI-PhotoMaker-Plus": {"author_account_age_days": 2132, "last_update": "2024-12-01 18:40:16", "stars": 283}, "https://github.com/shiimizu/ComfyUI-TiledDiffusion": {"author_account_age_days": 2132, "last_update": "2025-03-18 19:50:35", "stars": 444}, "https://github.com/shiimizu/ComfyUI-semantic-aware-guidance": {"author_account_age_days": 2132, "last_update": "2024-08-08 19:59:57", "stars": 12}, "https://github.com/shiimizu/ComfyUI_smZNodes": {"author_account_age_days": 2132, "last_update": "2025-06-04 15:26:05", "stars": 278}, "https://github.com/shingo1228/ComfyUI-SDXL-EmptyLatentImage": {"author_account_age_days": 2591, "last_update": "2024-05-22 20:41:29", "stars": 36}, "https://github.com/shingo1228/ComfyUI-send-eagle-slim": {"author_account_age_days": 2591, "last_update": "2024-07-30 22:28:41", "stars": 35}, "https://github.com/shinich39/comfyui-break-workflow": {"author_account_age_days": 680, "last_update": "2025-05-25 10:20:20", "stars": 0}, "https://github.com/shinich39/comfyui-civitai-workflow": {"author_account_age_days": 680, "last_update": "2025-06-11 16:47:58", "stars": 0}, "https://github.com/shinich39/comfyui-dynamic-routes": {"author_account_age_days": 680, "last_update": "2025-05-25 10:17:05", "stars": 5}, "https://github.com/shinich39/comfyui-get-meta": {"author_account_age_days": 680, "last_update": "2025-05-25 10:17:48", "stars": 7}, "https://github.com/shinich39/comfyui-innnnnpaint": {"author_account_age_days": 680, "last_update": "2025-05-25 10:18:06", "stars": 0}, "https://github.com/shinich39/comfyui-no-one-above-me": {"author_account_age_days": 680, "last_update": "2025-05-25 10:19:33", "stars": 0}, "https://github.com/shinich39/comfyui-prevent-sleep": {"author_account_age_days": 680, "last_update": "2025-05-25 10:18:45", "stars": 0}, "https://github.com/shobhitic/ComfyUI-PlusMinusTextClip": {"author_account_age_days": 4675, "last_update": "2024-06-20 13:57:29", "stars": 3}, "https://github.com/shockz0rz/comfy-easy-grids": {"author_account_age_days": 1994, "last_update": "2024-05-22 18:14:05", "stars": 24}, "https://github.com/siliconflow/BizyAir": {"author_account_age_days": 673, "last_update": "2025-06-24 02:35:18", "stars": 714}, "https://github.com/siliconflow/onediff_comfy_nodes": {"author_account_age_days": 673, "last_update": "2024-06-24 10:08:11", "stars": 23}, "https://github.com/silveroxides/ComfyUI-ModelUtils": {"author_account_age_days": 1869, "last_update": "2025-05-20 15:08:21", "stars": 1}, "https://github.com/silveroxides/ComfyUI-RR-JointTagger": {"author_account_age_days": 1869, "last_update": "2025-05-09 18:58:06", "stars": 2}, "https://github.com/silveroxides/ComfyUI_EmbeddingToolkit": {"author_account_age_days": 1869, "last_update": "2025-06-16 14:18:31", "stars": 6}, "https://github.com/silveroxides/ComfyUI_SigmoidOffsetScheduler": {"author_account_age_days": 1869, "last_update": "2025-05-11 19:44:35", "stars": 6}, "https://github.com/silveroxides/ComfyUI_bnb_nf4_fp4_Loaders": {"author_account_age_days": 1869, "last_update": "2025-04-28 01:08:43", "stars": 36}, "https://github.com/sipherxyz/comfyui-art-venture": {"author_account_age_days": 1474, "last_update": "2025-06-04 14:13:31", "stars": 273}, "https://github.com/sipie800/ComfyUI-PuLID-Flux-Enhanced": {"author_account_age_days": 2494, "last_update": "2025-02-07 15:04:47", "stars": 210}, "https://github.com/sittere/ComfyUI-YK_Line-loading": {"author_account_age_days": 1245, "last_update": "2025-03-02 09:10:54", "stars": 2}, "https://github.com/sjh00/ComfyUI-LoadImageWithInfo": {"author_account_age_days": 4130, "last_update": "2025-06-05 15:46:52", "stars": 2}, "https://github.com/skfoo/ComfyUI-Coziness": {"author_account_age_days": 2450, "last_update": "2024-08-16 03:10:43", "stars": 31}, "https://github.com/skycoder182/comfyui-filename-tools": {"author_account_age_days": 37, "last_update": "2025-05-20 18:06:04", "stars": 0}, "https://github.com/skycoder182/comfyui-skycoder-tools": {"author_account_age_days": 37, "last_update": "2025-06-08 12:26:41", "stars": 1}, "https://github.com/slvslvslv/ComfyUI-SmartHelperNodes": {"author_account_age_days": 345, "last_update": "2025-05-06 15:48:22", "stars": 2}, "https://github.com/slvslvslv/ComfyUI-SmartImageTools": {"author_account_age_days": 345, "last_update": "2025-05-03 12:46:43", "stars": 0}, "https://github.com/slyt/comfyui-ollama-nodes": {"author_account_age_days": 4307, "last_update": "2024-07-31 13:52:27", "stars": 0}, "https://github.com/sm079/ComfyUI-Face-Detection": {"author_account_age_days": 2052, "last_update": "2025-06-03 14:37:55", "stars": 0}, "https://github.com/smagnetize/kb-comfyui-nodes": {"author_account_age_days": 3082, "last_update": "2024-06-14 12:00:45", "stars": 0}, "https://github.com/smlbiobot/ComfyUI-Flux-Replicate-API": {"author_account_age_days": 3089, "last_update": "2024-12-26 16:21:00", "stars": 23}, "https://github.com/smlbiobot/sml-comfyui-prompt-expansion": {"author_account_age_days": 3089, "last_update": "2025-01-27 13:33:49", "stars": 13}, "https://github.com/smthemex/ComfyUI_AnyDoor": {"author_account_age_days": 722, "last_update": "2025-02-05 04:01:50", "stars": 63}, "https://github.com/smthemex/ComfyUI_CSD_MT": {"author_account_age_days": 722, "last_update": "2025-02-06 04:30:50", "stars": 19}, "https://github.com/smthemex/ComfyUI_CSGO_Wrapper": {"author_account_age_days": 722, "last_update": "2024-09-07 06:13:48", "stars": 16}, "https://github.com/smthemex/ComfyUI_ChatGLM_API": {"author_account_age_days": 722, "last_update": "2024-07-31 13:53:41", "stars": 23}, "https://github.com/smthemex/ComfyUI_CustomNet": {"author_account_age_days": 722, "last_update": "2024-08-11 08:58:37", "stars": 10}, "https://github.com/smthemex/ComfyUI_DICE_Talk": {"author_account_age_days": 722, "last_update": "2025-05-07 07:47:06", "stars": 25}, "https://github.com/smthemex/ComfyUI_DeepFakeDefenders": {"author_account_age_days": 722, "last_update": "2024-09-14 00:17:59", "stars": 41}, "https://github.com/smthemex/ComfyUI_Demucs": {"author_account_age_days": 722, "last_update": "2025-03-12 05:22:24", "stars": 8}, "https://github.com/smthemex/ComfyUI_Diffree": {"author_account_age_days": 722, "last_update": "2025-03-09 01:16:33", "stars": 31}, "https://github.com/smthemex/ComfyUI_DiffuEraser": {"author_account_age_days": 722, "last_update": "2025-02-14 12:09:00", "stars": 162}, "https://github.com/smthemex/ComfyUI_EchoMimic": {"author_account_age_days": 722, "last_update": "2025-04-05 12:23:33", "stars": 634}, "https://github.com/smthemex/ComfyUI_Face_Anon_Simple": {"author_account_age_days": 722, "last_update": "2025-03-12 05:22:03", "stars": 16}, "https://github.com/smthemex/ComfyUI_FoleyCrafter": {"author_account_age_days": 722, "last_update": "2025-05-29 11:42:48", "stars": 61}, "https://github.com/smthemex/ComfyUI_FollowYourEmoji": {"author_account_age_days": 722, "last_update": "2025-04-11 13:45:15", "stars": 16}, "https://github.com/smthemex/ComfyUI_Hallo2": {"author_account_age_days": 722, "last_update": "2025-03-12 05:22:46", "stars": 74}, "https://github.com/smthemex/ComfyUI_HiDiffusion_Pro": {"author_account_age_days": 722, "last_update": "2025-01-13 03:29:50", "stars": 52}, "https://github.com/smthemex/ComfyUI_HunyuanAvatar_Sm": {"author_account_age_days": 722, "last_update": "2025-06-24 13:06:34", "stars": 70}, "https://github.com/smthemex/ComfyUI_ID_Animator": {"author_account_age_days": 722, "last_update": "2024-07-31 13:53:27", "stars": 24}, "https://github.com/smthemex/ComfyUI_InstantIR_Wrapper": {"author_account_age_days": 722, "last_update": "2025-03-12 05:22:14", "stars": 236}, "https://github.com/smthemex/ComfyUI_KV_Edit": {"author_account_age_days": 722, "last_update": "2025-05-24 00:35:59", "stars": 58}, "https://github.com/smthemex/ComfyUI_Light_A_Video": {"author_account_age_days": 722, "last_update": "2025-04-10 01:05:56", "stars": 81}, "https://github.com/smthemex/ComfyUI_Llama3_8B": {"author_account_age_days": 722, "last_update": "2024-06-25 00:49:01", "stars": 26}, "https://github.com/smthemex/ComfyUI_MS_Diffusion": {"author_account_age_days": 722, "last_update": "2024-09-10 09:50:19", "stars": 59}, "https://github.com/smthemex/ComfyUI_MangaNinjia": {"author_account_age_days": 722, "last_update": "2025-04-09 14:21:57", "stars": 53}, "https://github.com/smthemex/ComfyUI_MooER": {"author_account_age_days": 722, "last_update": "2025-03-09 01:15:38", "stars": 5}, "https://github.com/smthemex/ComfyUI_OmniParser": {"author_account_age_days": 722, "last_update": "2025-03-12 05:22:34", "stars": 39}, "https://github.com/smthemex/ComfyUI_PBR_Maker": {"author_account_age_days": 722, "last_update": "2025-03-12 05:21:53", "stars": 13}, "https://github.com/smthemex/ComfyUI_ParlerTTS": {"author_account_age_days": 722, "last_update": "2024-12-25 06:26:03", "stars": 44}, "https://github.com/smthemex/ComfyUI_PartPacker": {"author_account_age_days": 722, "last_update": "2025-06-25 00:44:53", "stars": 16}, "https://github.com/smthemex/ComfyUI_Personalize_Anything": {"author_account_age_days": 722, "last_update": "2025-03-26 00:38:13", "stars": 43}, "https://github.com/smthemex/ComfyUI_PhotoDoodle": {"author_account_age_days": 722, "last_update": "2025-03-20 08:19:21", "stars": 97}, "https://github.com/smthemex/ComfyUI_Pic2Story": {"author_account_age_days": 722, "last_update": "2024-12-06 12:12:19", "stars": 9}, "https://github.com/smthemex/ComfyUI_Pipeline_Tool": {"author_account_age_days": 722, "last_update": "2024-08-05 06:14:57", "stars": 10}, "https://github.com/smthemex/ComfyUI_Pops": {"author_account_age_days": 722, "last_update": "2024-08-12 09:11:49", "stars": 21}, "https://github.com/smthemex/ComfyUI_SVFR": {"author_account_age_days": 722, "last_update": "2025-03-12 05:21:23", "stars": 91}, "https://github.com/smthemex/ComfyUI_Sapiens": {"author_account_age_days": 722, "last_update": "2025-03-12 05:22:59", "stars": 176}, "https://github.com/smthemex/ComfyUI_SongGeneration": {"author_account_age_days": 722, "last_update": "2025-06-23 13:49:47", "stars": 34}, "https://github.com/smthemex/ComfyUI_Sonic": {"author_account_age_days": 722, "last_update": "2025-05-22 00:46:49", "stars": 1032}, "https://github.com/smthemex/ComfyUI_StableAudio_Open": {"author_account_age_days": 722, "last_update": "2024-08-10 03:45:47", "stars": 27}, "https://github.com/smthemex/ComfyUI_Stable_Makeup": {"author_account_age_days": 722, "last_update": "2025-06-25 00:38:14", "stars": 97}, "https://github.com/smthemex/ComfyUI_StoryDiffusion": {"author_account_age_days": 722, "last_update": "2025-06-25 06:23:47", "stars": 449}, "https://github.com/smthemex/ComfyUI_Streamv2v_Plus": {"author_account_age_days": 722, "last_update": "2024-09-06 08:20:59", "stars": 10}, "https://github.com/smthemex/ComfyUI_TRELLIS": {"author_account_age_days": 722, "last_update": "2025-04-22 00:28:27", "stars": 167}, "https://github.com/smthemex/ComfyUI_VisualCloze": {"author_account_age_days": 722, "last_update": "2025-05-21 08:56:45", "stars": 11}, "https://github.com/smthemex/ComfyUI_YuE": {"author_account_age_days": 722, "last_update": "2025-02-24 12:02:41", "stars": 142}, "https://github.com/sn0w12/ComfyUI-Sn0w-Scripts": {"author_account_age_days": 1141, "last_update": "2025-05-04 15:47:59", "stars": 11}, "https://github.com/sn0w12/ComfyUI-Syntax-Highlighting": {"author_account_age_days": 1141, "last_update": "2025-04-07 10:24:12", "stars": 0}, "https://github.com/sneccc/comfyui-snek-nodes": {"author_account_age_days": 1935, "last_update": "2025-06-25 14:25:41", "stars": 1}, "https://github.com/somesomebody/lorainfo-sidebar": {"author_account_age_days": 59, "last_update": "2025-05-31 07:23:03", "stars": 2}, "https://github.com/souki202/ComfyUI-LoadImage-Advanced": {"author_account_age_days": 3627, "last_update": "2025-03-03 03:53:26", "stars": 1}, "https://github.com/sourceful-official/LoadLoraModelOnlyWithUrl": {"author_account_age_days": 1850, "last_update": "2024-12-04 12:14:51", "stars": 1}, "https://github.com/sousakujikken/ComfyUI-PixydustQuantizer": {"author_account_age_days": 775, "last_update": "2025-03-30 15:07:02", "stars": 29}, "https://github.com/space-nuko/ComfyUI-Disco-Diffusion": {"author_account_age_days": 3092, "last_update": "2024-08-07 11:51:17", "stars": 54}, "https://github.com/space-nuko/ComfyUI-OpenPose-Editor": {"author_account_age_days": 3092, "last_update": "2024-05-22 18:10:49", "stars": 213}, "https://github.com/space-nuko/nui-suite": {"author_account_age_days": 3092, "last_update": "2024-05-22 18:11:04", "stars": 11}, "https://github.com/spacepxl/ComfyUI-Depth-Pro": {"author_account_age_days": 662, "last_update": "2024-10-23 20:05:56", "stars": 187}, "https://github.com/spacepxl/ComfyUI-Florence-2": {"author_account_age_days": 662, "last_update": "2024-07-20 19:44:33", "stars": 82}, "https://github.com/spacepxl/ComfyUI-HQ-Image-Save": {"author_account_age_days": 662, "last_update": "2025-01-30 00:12:58", "stars": 58}, "https://github.com/spacepxl/ComfyUI-Image-Filters": {"author_account_age_days": 662, "last_update": "2025-05-21 21:27:56", "stars": 228}, "https://github.com/spacepxl/ComfyUI-LossTesting": {"author_account_age_days": 662, "last_update": "2025-01-26 05:09:57", "stars": 2}, "https://github.com/spacepxl/ComfyUI-RAVE": {"author_account_age_days": 662, "last_update": "2024-05-22 20:56:19", "stars": 92}, "https://github.com/spacepxl/ComfyUI-StyleGan": {"author_account_age_days": 662, "last_update": "2024-06-10 20:16:34", "stars": 19}, "https://github.com/spawner1145/CUI-Lumina2-TeaCache": {"author_account_age_days": 305, "last_update": "2025-06-08 09:51:09", "stars": 7}, "https://github.com/spawner1145/comfyui-aichat": {"author_account_age_days": 305, "last_update": "2025-06-09 11:06:53", "stars": 2}, "https://github.com/spinagon/ComfyUI-seam-carving": {"author_account_age_days": 5108, "last_update": "2025-03-14 08:47:57", "stars": 22}, "https://github.com/spinagon/ComfyUI-seamless-tiling": {"author_account_age_days": 5108, "last_update": "2025-03-14 08:48:11", "stars": 207}, "https://github.com/spro/comfyui-mirror": {"author_account_age_days": 5599, "last_update": "2024-05-22 20:50:25", "stars": 6}, "https://github.com/ssitu/ComfyUI_UltimateSDUpscale": {"author_account_age_days": 2057, "last_update": "2025-06-05 01:53:42", "stars": 1217}, "https://github.com/ssitu/ComfyUI_fabric": {"author_account_age_days": 2057, "last_update": "2024-05-22 18:10:19", "stars": 93}, "https://github.com/ssitu/ComfyUI_restart_sampling": {"author_account_age_days": 2057, "last_update": "2024-05-22 18:09:49", "stars": 89}, "https://github.com/ssitu/ComfyUI_roop": {"author_account_age_days": 2057, "last_update": "2024-05-22 18:10:03", "stars": 77}, "https://github.com/stavsap/comfyui-downloader": {"author_account_age_days": 4451, "last_update": "2025-06-22 20:44:33", "stars": 0}, "https://github.com/stavsap/comfyui-kokoro": {"author_account_age_days": 4451, "last_update": "2025-05-17 13:23:49", "stars": 52}, "https://github.com/stavsap/comfyui-ollama": {"author_account_age_days": 4451, "last_update": "2025-05-28 07:22:35", "stars": 569}, "https://github.com/stepfun-ai/ComfyUI-StepVideo": {"author_account_age_days": 320, "last_update": "2025-03-27 07:52:26", "stars": 39}, "https://github.com/stevenwg/ComfyUI-VideoGrid": {"author_account_age_days": 3666, "last_update": "2025-05-26 06:51:21", "stars": 0}, "https://github.com/stormcenter/ComfyUI-AutoSplitGridImage": {"author_account_age_days": 4495, "last_update": "2025-01-06 12:02:58", "stars": 32}, "https://github.com/stormcenter/ComfyUI-LivePhotoCreator": {"author_account_age_days": 4495, "last_update": "2025-01-06 12:03:42", "stars": 24}, "https://github.com/stormcenter/ComfyUI-SVGFullfill": {"author_account_age_days": 4495, "last_update": "2025-01-06 12:04:18", "stars": 10}, "https://github.com/storyicon/comfyui_musev_evolved": {"author_account_age_days": 2919, "last_update": "2024-06-14 11:02:40", "stars": 26}, "https://github.com/storyicon/comfyui_segment_anything": {"author_account_age_days": 2919, "last_update": "2024-07-12 10:17:33", "stars": 971}, "https://github.com/strand1/ComfyUI-Autogen": {"author_account_age_days": 4839, "last_update": "2025-01-21 05:10:43", "stars": 3}, "https://github.com/strimmlarn/ComfyUI-Strimmlarns-Aesthetic-Score": {"author_account_age_days": 2994, "last_update": "2024-06-17 10:01:44", "stars": 34}, "https://github.com/styler00dollar/ComfyUI-deepcache": {"author_account_age_days": 2213, "last_update": "2024-05-22 22:18:18", "stars": 11}, "https://github.com/styler00dollar/ComfyUI-sudo-latent-upscale": {"author_account_age_days": 2213, "last_update": "2024-05-22 22:18:07", "stars": 39}, "https://github.com/subtleGradient/TinkerBot-tech-for-ComfyUI-Touchpad": {"author_account_age_days": 6298, "last_update": "2024-08-16 01:18:03", "stars": 38}, "https://github.com/sugarkwork/ComfyUI_AspectRatioToSize": {"author_account_age_days": 1243, "last_update": "2025-06-04 00:48:13", "stars": 2}, "https://github.com/sugarkwork/comfyui-trtupscaler": {"author_account_age_days": 1243, "last_update": "2025-06-11 07:43:10", "stars": 1}, "https://github.com/sugarkwork/comfyui_cohere": {"author_account_age_days": 1243, "last_update": "2025-06-11 04:29:08", "stars": 1}, "https://github.com/sugarkwork/comfyui_tag_fillter": {"author_account_age_days": 1243, "last_update": "2025-04-16 07:37:35", "stars": 52}, "https://github.com/superyoman/comfyui_lumaAPI": {"author_account_age_days": 816, "last_update": "2024-06-17 21:00:05", "stars": 21}, "https://github.com/surinder83singh/ComfyUI-compare-videos": {"author_account_age_days": 4884, "last_update": "2025-05-06 01:30:48", "stars": 1}, "https://github.com/svetozarov/AS_LLM_nodes": {"author_account_age_days": 853, "last_update": "2025-03-23 12:05:43", "stars": 2}, "https://github.com/sweetndata/ComfyUI-Image-Harmonizer": {"author_account_age_days": 1099, "last_update": "2024-11-20 06:10:34", "stars": 2}, "https://github.com/sweetndata/ComfyUI-googletrans": {"author_account_age_days": 1099, "last_update": "2024-11-20 04:53:19", "stars": 3}, "https://github.com/sweetndata/ComfyUI_Sticker_Compositer": {"author_account_age_days": 1099, "last_update": "2025-01-02 06:54:51", "stars": 1}, "https://github.com/swhsiang/comfyui-3d-gs-renderer": {"author_account_age_days": 3302, "last_update": "2025-06-09 03:05:11", "stars": 1}, "https://github.com/syllebra/bilbox-comfyui": {"author_account_age_days": 3503, "last_update": "2024-12-06 23:51:55", "stars": 132}, "https://github.com/sylym/comfy_vid2vid": {"author_account_age_days": 2266, "last_update": "2024-05-22 17:53:40", "stars": 71}, "https://github.com/synthetai/ComfyUI-JM-KLing-API": {"author_account_age_days": 319, "last_update": "2025-05-14 10:42:13", "stars": 1}, "https://github.com/synthetai/ComfyUI-JM-MiniMax-API": {"author_account_age_days": 319, "last_update": "2025-06-24 02:00:05", "stars": 1}, "https://github.com/synthetai/ComfyUI-JM-Volcengine-API": {"author_account_age_days": 318, "last_update": "2025-06-24 08:11:39", "stars": 0}, "https://github.com/synthetai/ComfyUI-ToolBox": {"author_account_age_days": 319, "last_update": "2025-06-25 07:35:50", "stars": 0}, "https://github.com/synthetai/ComfyUI_FaceEnhancer": {"author_account_age_days": 319, "last_update": "2025-04-17 00:34:39", "stars": 2}, "https://github.com/synthetai/ComfyUI_PromptBatcher": {"author_account_age_days": 319, "last_update": "2025-04-14 04:42:03", "stars": 5}, "https://github.com/szhublox/ambw_comfyui": {"author_account_age_days": 1375, "last_update": "2024-05-22 18:04:57", "stars": 15}, "https://github.com/taabata/ComfyCanvas": {"author_account_age_days": 2050, "last_update": "2024-12-15 00:59:25", "stars": 88}, "https://github.com/taabata/LCM_Inpaint_Outpaint_Comfy": {"author_account_age_days": 2050, "last_update": "2024-11-18 00:45:28", "stars": 260}, "https://github.com/taabata/SANA_LOWVRAM": {"author_account_age_days": 2050, "last_update": "2024-12-28 01:16:29", "stars": 5}, "https://github.com/taches-ai/comfyui-scene-composer": {"author_account_age_days": 274, "last_update": "2025-05-28 07:30:03", "stars": 55}, "https://github.com/tachyon-beep/comfyui-simplefeed": {"author_account_age_days": 5288, "last_update": "2024-10-16 09:19:29", "stars": 10}, "https://github.com/takemetosiberia/ComfyUI-SAMURAI--SAM2-": {"author_account_age_days": 752, "last_update": "2024-12-01 13:06:02", "stars": 36}, "https://github.com/talesofai/comfyui-browser": {"author_account_age_days": 923, "last_update": "2024-11-11 01:42:30", "stars": 590}, "https://github.com/tanglaoya321/ComfyUI-StoryMaker": {"author_account_age_days": 4339, "last_update": "2024-10-01 01:20:00", "stars": 17}, "https://github.com/tatookan/comfyui_ssl_gemini_EXP": {"author_account_age_days": 2098, "last_update": "2025-03-19 15:54:44", "stars": 86}, "https://github.com/tavyra/ComfyUI_Curves": {"author_account_age_days": 2467, "last_update": "2025-05-08 01:48:55", "stars": 2}, "https://github.com/tetsuoo-online/comfyui-too-xmp-metadata": {"author_account_age_days": 2387, "last_update": "2025-06-07 15:59:26", "stars": 4}, "https://github.com/teward/Comfy-Sentry": {"author_account_age_days": 5466, "last_update": "2024-07-31 21:37:42", "stars": 1}, "https://github.com/teward/ComfyUI-Helper-Nodes": {"author_account_age_days": 5466, "last_update": "2024-05-23 01:22:01", "stars": 6}, "https://github.com/thalismind/ComfyUI-Blend-Nodes": {"author_account_age_days": 166, "last_update": "2025-06-02 02:22:19", "stars": 0}, "https://github.com/theAdamColton/ComfyUI-texflow-extension": {"author_account_age_days": 1724, "last_update": "2025-01-16 19:58:24", "stars": 1}, "https://github.com/theUpsider/ComfyUI-Styles_CSV_Loader": {"author_account_age_days": 3090, "last_update": "2025-05-16 11:01:23", "stars": 58}, "https://github.com/thecooltechguy/ComfyUI-ComfyWorkflows": {"author_account_age_days": 2790, "last_update": "2024-05-22 21:33:47", "stars": 70}, "https://github.com/thecooltechguy/ComfyUI-MagicAnimate": {"author_account_age_days": 2790, "last_update": "2024-05-22 21:33:35", "stars": 223}, "https://github.com/thecooltechguy/ComfyUI-Stable-Video-Diffusion": {"author_account_age_days": 2790, "last_update": "2024-05-24 22:14:42", "stars": 361}, "https://github.com/thedivergentai/divergent_nodes": {"author_account_age_days": 827, "last_update": "2025-06-23 16:16:30", "stars": 0}, "https://github.com/theshubzworld/ComfyUI-FaceCalloutNode": {"author_account_age_days": 338, "last_update": "2025-05-09 14:38:15", "stars": 0}, "https://github.com/theshubzworld/ComfyUI-SD3.5-Latent-Size-Picker": {"author_account_age_days": 338, "last_update": "2024-12-25 14:09:38", "stars": 0}, "https://github.com/theshubzworld/ComfyUI-TogetherVision": {"author_account_age_days": 338, "last_update": "2025-06-26 18:48:25", "stars": 3}, "https://github.com/theshubzworld/ComfyUI-ollama_killer": {"author_account_age_days": 338, "last_update": "2025-06-09 09:14:55", "stars": 3}, "https://github.com/thezveroboy/ComfyUI-CSM-Nodes": {"author_account_age_days": 3581, "last_update": "2025-03-17 10:08:12", "stars": 35}, "https://github.com/thezveroboy/ComfyUI-WAN-ClipSkip": {"author_account_age_days": 3581, "last_update": "2025-03-16 21:12:54", "stars": 1}, "https://github.com/thezveroboy/ComfyUI-lut": {"author_account_age_days": 3581, "last_update": "2025-05-24 21:37:06", "stars": 2}, "https://github.com/thezveroboy/ComfyUI_ACE-Step-zveroboy": {"author_account_age_days": 3581, "last_update": "2025-05-12 11:01:16", "stars": 1}, "https://github.com/thezveroboy/comfyui-random-image-loader": {"author_account_age_days": 3581, "last_update": "2025-05-11 18:04:32", "stars": 1}, "https://github.com/thoddnn/ComfyUI-MLX": {"author_account_age_days": 631, "last_update": "2024-10-22 06:41:22", "stars": 141}, "https://github.com/tianguanggliu/Utools": {"author_account_age_days": 2707, "last_update": "2024-08-29 09:45:03", "stars": 0}, "https://github.com/tiankuan93/ComfyUI-V-Express": {"author_account_age_days": 3318, "last_update": "2024-06-26 02:41:00", "stars": 112}, "https://github.com/tianlang0704/ComfyUI-StableProjectorzBridge": {"author_account_age_days": 3692, "last_update": "2024-12-01 11:46:58", "stars": 37}, "https://github.com/tianyuw/ComfyUI-LLM-API": {"author_account_age_days": 3560, "last_update": "2025-01-25 19:31:47", "stars": 6}, "https://github.com/tigeryy2/comfyui-structured-outputs": {"author_account_age_days": 2339, "last_update": "2025-06-19 04:48:05", "stars": 1}, "https://github.com/tighug/comfyui-eagle-feeder": {"author_account_age_days": 2478, "last_update": "2025-05-09 14:26:15", "stars": 0}, "https://github.com/tighug/comfyui-rating-checker": {"author_account_age_days": 2478, "last_update": "2025-05-09 14:22:51", "stars": 1}, "https://github.com/tkreuziger/comfyui-claude": {"author_account_age_days": 903, "last_update": "2025-04-10 18:23:35", "stars": 4}, "https://github.com/tmagara/ComfyUI-Prediction-Boost": {"author_account_age_days": 4743, "last_update": "2024-07-31 13:51:19", "stars": 1}, "https://github.com/tocubed/ComfyUI-AudioReactor": {"author_account_age_days": 4227, "last_update": "2024-05-22 22:21:57", "stars": 8}, "https://github.com/tocubed/ComfyUI-EvTexture": {"author_account_age_days": 4227, "last_update": "2025-01-05 23:21:23", "stars": 15}, "https://github.com/tomudo/ComfyUI-ascii-art": {"author_account_age_days": 3270, "last_update": "2024-11-21 05:24:12", "stars": 3}, "https://github.com/tooldigital/ComfyUI-Yolo-Cropper": {"author_account_age_days": 4685, "last_update": "2024-06-14 13:59:48", "stars": 9}, "https://github.com/toxicwind/ComfyUI-TTools": {"author_account_age_days": 4738, "last_update": "2024-07-04 20:07:35", "stars": 1}, "https://github.com/toyxyz/ComfyUI_rgbx_Wrapper": {"author_account_age_days": 4017, "last_update": "2025-04-03 08:17:10", "stars": 88}, "https://github.com/toyxyz/ComfyUI_toyxyz_test_nodes": {"author_account_age_days": 4017, "last_update": "2025-06-10 14:20:31", "stars": 582}, "https://github.com/traugdor/ComfyUI-Riffusion": {"author_account_age_days": 4188, "last_update": "2025-05-30 20:15:05", "stars": 3}, "https://github.com/traugdor/ComfyUI-UltimateSDUpscale-GGUF": {"author_account_age_days": 4188, "last_update": "2025-06-21 15:15:07", "stars": 13}, "https://github.com/traugdor/ComfyUI-quadMoons-nodes": {"author_account_age_days": 4188, "last_update": "2025-06-23 15:18:42", "stars": 14}, "https://github.com/tritant/ComfyUI_CreaPrompt": {"author_account_age_days": 3508, "last_update": "2025-06-17 03:16:11", "stars": 59}, "https://github.com/tritant/ComfyUI_Flux_Block_Lora_Merger": {"author_account_age_days": 3508, "last_update": "2025-06-10 01:27:00", "stars": 1}, "https://github.com/tritant/ComfyUI_Flux_Lora_Merger": {"author_account_age_days": 3508, "last_update": "2025-05-09 04:39:16", "stars": 2}, "https://github.com/trojblue/trNodes": {"author_account_age_days": 2625, "last_update": "2024-05-22 18:04:36", "stars": 8}, "https://github.com/troyxmccall/ComfyUI-ScaleToTargetMegapixels": {"author_account_age_days": 5756, "last_update": "2024-11-11 00:07:25", "stars": 1}, "https://github.com/trumanwong/ComfyUI-NSFW-Detection": {"author_account_age_days": 3328, "last_update": "2025-04-21 05:38:12", "stars": 35}, "https://github.com/tsogzark/ComfyUI-load-image-from-url": {"author_account_age_days": 1896, "last_update": "2024-06-14 13:59:05", "stars": 20}, "https://github.com/ttulttul/ComfyUI-Iterative-Mixer": {"author_account_age_days": 5125, "last_update": "2025-03-10 03:33:02", "stars": 117}, "https://github.com/ttulttul/ComfyUI-Tensor-Operations": {"author_account_age_days": 5125, "last_update": "2025-02-03 16:57:00", "stars": 6}, "https://github.com/tungdop2/Comfyui_face_restorer": {"author_account_age_days": 1791, "last_update": "2024-11-21 15:53:59", "stars": 2}, "https://github.com/tungdop2/Comfyui_joy-caption-alpha-two": {"author_account_age_days": 1791, "last_update": "2025-04-19 06:00:23", "stars": 6}, "https://github.com/turkyden/ComfyUI-SmartCrop": {"author_account_age_days": 3116, "last_update": "2024-10-08 09:36:34", "stars": 3}, "https://github.com/tusharbhutt/Endless-Nodes": {"author_account_age_days": 3033, "last_update": "2025-06-25 04:14:23", "stars": 42}, "https://github.com/twri/sdxl_prompt_styler": {"author_account_age_days": 4435, "last_update": "2024-05-22 18:16:58", "stars": 857}, "https://github.com/txt2any/ComfyUI-PromptOrganizer": {"author_account_age_days": 452, "last_update": "2024-05-23 01:10:33", "stars": 0}, "https://github.com/ty0x2333/ComfyUI-Dev-Utils": {"author_account_age_days": 4070, "last_update": "2024-10-03 23:26:45", "stars": 134}, "https://github.com/tzwm/comfyui-profiler": {"author_account_age_days": 5136, "last_update": "2024-08-28 14:27:12", "stars": 157}, "https://github.com/uarefans/ComfyUI-Fans": {"author_account_age_days": 1639, "last_update": "2024-07-14 15:00:38", "stars": 17}, "https://github.com/uetuluk/comfyui-webcam-node": {"author_account_age_days": 2681, "last_update": "2024-06-14 08:25:13", "stars": 4}, "https://github.com/uihp/ComfyUI-String-Chain": {"author_account_age_days": 1405, "last_update": "2025-04-12 12:22:14", "stars": 0}, "https://github.com/uinodes/ComfyUI-uinodesDOC": {"author_account_age_days": 1, "last_update": "2025-06-26 04:07:59", "stars": 25}, "https://github.com/umiyuki/comfyui-pad-to-eight": {"author_account_age_days": 4121, "last_update": "2025-01-07 09:58:36", "stars": 0}, "https://github.com/un-seen/comfyui-tensorops": {"author_account_age_days": 1683, "last_update": "2024-10-26 00:04:07", "stars": 28}, "https://github.com/un-seen/comfyui_segment_anything_plus": {"author_account_age_days": 1683, "last_update": "2024-07-29 06:21:54", "stars": 8}, "https://github.com/unicough/comfy_openai_image_api": {"author_account_age_days": 4078, "last_update": "2025-05-02 04:24:34", "stars": 0}, "https://github.com/unwdef/unwdef-nodes-comfyui": {"author_account_age_days": 441, "last_update": "2025-03-27 10:42:15", "stars": 5}, "https://github.com/usrname0/comfyui-holdup": {"author_account_age_days": 2781, "last_update": "2025-06-12 07:26:10", "stars": 0}, "https://github.com/vadimcro/VKRiez-Edge": {"author_account_age_days": 3003, "last_update": "2025-03-18 11:18:27", "stars": 7}, "https://github.com/vahidzxc/va-nodes": {"author_account_age_days": 359, "last_update": "2025-03-22 01:50:08", "stars": 2}, "https://github.com/vahlok-alunmid/ComfyUI-ExtendIPAdapterClipVision": {"author_account_age_days": 2754, "last_update": "2025-02-09 04:06:34", "stars": 12}, "https://github.com/valofey/Openrouter-Node": {"author_account_age_days": 1751, "last_update": "2025-02-13 21:26:22", "stars": 5}, "https://github.com/vanche1212/ComfyUI-ZMG-Nodes": {"author_account_age_days": 3325, "last_update": "2024-06-25 04:48:19", "stars": 3}, "https://github.com/vanillacode314/SimpleWildcardsComfyUI": {"author_account_age_days": 1224, "last_update": "2025-04-02 04:56:25", "stars": 5}, "https://github.com/var1ableX/ComfyUI_Accessories": {"author_account_age_days": 5132, "last_update": "2025-02-09 14:31:19", "stars": 1}, "https://github.com/vault-developer/comfyui-image-blender": {"author_account_age_days": 2981, "last_update": "2025-04-02 19:37:15", "stars": 20}, "https://github.com/veighnsche/comfyui_gr85": {"author_account_age_days": 3468, "last_update": "2024-11-26 17:26:48", "stars": 1}, "https://github.com/vekitan55/SimpleFlux1Merger": {"author_account_age_days": 698, "last_update": "2025-04-23 12:09:47", "stars": 0}, "https://github.com/victorchall/comfyui_webcamcapture": {"author_account_age_days": 3513, "last_update": "2025-04-16 20:39:32", "stars": 14}, "https://github.com/vienteck/ComfyUI-Chat-GPT-Integration": {"author_account_age_days": 3795, "last_update": "2024-05-22 22:11:14", "stars": 31}, "https://github.com/vincentfs/ComfyUI-ArchiGraph": {"author_account_age_days": 4034, "last_update": "2025-01-23 17:29:09", "stars": 2}, "https://github.com/violet-chen/comfyui-psd2png": {"author_account_age_days": 1765, "last_update": "2025-06-04 11:41:34", "stars": 20}, "https://github.com/violet0927/ComfyUI-HuggingFaceLoraUploader": {"author_account_age_days": 147, "last_update": "2025-06-03 05:46:11", "stars": 0}, "https://github.com/viperyl/ComfyUI-RGT": {"author_account_age_days": 2400, "last_update": "2024-06-20 15:33:50", "stars": 8}, "https://github.com/vivax3794/ComfyUI-Sub-Nodes": {"author_account_age_days": 2206, "last_update": "2025-02-21 07:03:30", "stars": 163}, "https://github.com/vivax3794/ComfyUI-Vivax-Nodes": {"author_account_age_days": 2206, "last_update": "2024-09-07 18:42:27", "stars": 3}, "https://github.com/vivi-gomez/ComfyUI-fixnodetranslate": {"author_account_age_days": 4715, "last_update": "2025-06-01 08:42:50", "stars": 0}, "https://github.com/vkff5833/ComfyUI-MobileClient": {"author_account_age_days": 662, "last_update": "2025-02-11 00:34:36", "stars": 4}, "https://github.com/vkff5833/ComfyUI-PromptConverter": {"author_account_age_days": 662, "last_update": "2025-01-27 18:35:41", "stars": 2}, "https://github.com/vladpro3/ComfyUI_BishaNodes": {"author_account_age_days": 2694, "last_update": "2025-06-08 19:23:23", "stars": 1}, "https://github.com/vsevolod-oparin/comfyui-kandinsky22": {"author_account_age_days": 5352, "last_update": "2025-04-02 03:48:05", "stars": 10}, "https://github.com/vuongminh1907/ComfyUI_ZenID": {"author_account_age_days": 939, "last_update": "2025-03-27 00:11:23", "stars": 179}, "https://github.com/wTechArtist/ComfyUI-CustomNodes": {"author_account_age_days": 1728, "last_update": "2024-08-21 03:03:16", "stars": 2}, "https://github.com/wTechArtist/ComfyUI-StableDelight-weiweiliang": {"author_account_age_days": 1728, "last_update": "2025-03-23 07:52:36", "stars": 2}, "https://github.com/wTechArtist/ComfyUI_VVL_VideoCamera_Advanced": {"author_account_age_days": 1728, "last_update": "2025-06-23 10:08:04", "stars": 1}, "https://github.com/wakattac/ComfyUI-AbstractImaGen": {"author_account_age_days": 52, "last_update": "2025-05-09 22:37:03", "stars": 1}, "https://github.com/wallish77/wlsh_nodes": {"author_account_age_days": 2588, "last_update": "2024-06-19 12:01:29", "stars": 125}, "https://github.com/wandbrandon/comfyui-pixel": {"author_account_age_days": 3751, "last_update": "2024-06-14 07:07:09", "stars": 4}, "https://github.com/wasilone11/comfyui-sync-lipsync-node": {"author_account_age_days": 2573, "last_update": "2025-06-25 21:57:56", "stars": 0}, "https://github.com/waterminer/ComfyUI-tagcomplete": {"author_account_age_days": 2479, "last_update": "2025-01-06 00:13:57", "stars": 11}, "https://github.com/web3nomad/ComfyUI_Invisible_Watermark": {"author_account_age_days": 1335, "last_update": "2024-05-23 01:16:54", "stars": 1}, "https://github.com/webfiltered/DebugNode-ComfyUI": {"author_account_age_days": 341, "last_update": "2025-05-06 16:15:33", "stars": 8}, "https://github.com/wei30172/comfygen": {"author_account_age_days": 1980, "last_update": "2024-11-07 22:10:50", "stars": 7}, "https://github.com/weilin9999/WeiLin-Comfyui-Tools": {"author_account_age_days": 2275, "last_update": "2025-05-08 04:25:11", "stars": 166}, "https://github.com/welltop-cn/ComfyUI-TeaCache": {"author_account_age_days": 1939, "last_update": "2025-06-22 07:43:14", "stars": 833}, "https://github.com/wentao-uw/ComfyUI-template-matching": {"author_account_age_days": 2147, "last_update": "2024-11-06 06:52:30", "stars": 1}, "https://github.com/westNeighbor/ComfyUI-ultimate-openpose-editor": {"author_account_age_days": 662, "last_update": "2025-06-16 17:57:31", "stars": 59}, "https://github.com/westNeighbor/ComfyUI-ultimate-openpose-estimator": {"author_account_age_days": 662, "last_update": "2025-06-03 21:06:33", "stars": 12}, "https://github.com/westNeighbor/ComfyUI-ultimate-openpose-render": {"author_account_age_days": 662, "last_update": "2025-01-25 05:54:27", "stars": 8}, "https://github.com/whatbirdisthat/cyberdolphin": {"author_account_age_days": 5854, "last_update": "2024-07-31 13:40:12", "stars": 14}, "https://github.com/whmc76/ComfyUI-Openpose-Editor-Plus": {"author_account_age_days": 819, "last_update": "2024-06-20 13:52:34", "stars": 38}, "https://github.com/whmc76/ComfyUI-RemoveBackgroundSuite": {"author_account_age_days": 819, "last_update": "2025-06-11 10:11:09", "stars": 3}, "https://github.com/whmc76/ComfyUI-UniversalToolkit": {"author_account_age_days": 819, "last_update": "2025-06-24 11:07:09", "stars": 1}, "https://github.com/wildminder/000_ComfyUI-Optim": {"author_account_age_days": 4600, "last_update": "2025-06-02 21:30:04", "stars": 4}, "https://github.com/wildminder/ComfyUI-Chatterbox": {"author_account_age_days": 4600, "last_update": "2025-05-30 06:27:05", "stars": 28}, "https://github.com/wildminder/ComfyUI-KEEP": {"author_account_age_days": 4600, "last_update": "2025-05-30 21:03:54", "stars": 30}, "https://github.com/willchil/ComfyUI-Environment-Visualizer": {"author_account_age_days": 3005, "last_update": "2025-03-29 23:09:07", "stars": 12}, "https://github.com/willmiao/ComfyUI-Lora-Manager": {"author_account_age_days": 3725, "last_update": "2025-06-27 02:12:25", "stars": 321}, "https://github.com/windfancy/zsq_prompt": {"author_account_age_days": 1904, "last_update": "2024-12-15 14:58:52", "stars": 0}, "https://github.com/wings6407/ComfyUI_HBH-image_overlay": {"author_account_age_days": 456, "last_update": "2025-05-12 02:52:38", "stars": 1}, "https://github.com/wirytiox/ComfyUI-SelectStringFromListWithIndex": {"author_account_age_days": 1591, "last_update": "2025-02-16 09:09:34", "stars": 1}, "https://github.com/withmpx/mpx-comfyui-nodes": {"author_account_age_days": 97, "last_update": "2025-04-16 22:08:20", "stars": 2}, "https://github.com/without-ordinary/openoutpaint_comfyui_interface": {"author_account_age_days": 3290, "last_update": "2025-06-13 10:37:31", "stars": 0}, "https://github.com/wjl0313/ComfyUI_KimNodes": {"author_account_age_days": 2239, "last_update": "2025-05-12 03:25:33", "stars": 33}, "https://github.com/wmatson/easy-comfy-nodes": {"author_account_age_days": 4485, "last_update": "2025-04-17 16:26:02", "stars": 18}, "https://github.com/wmpmiles/comfyui-some-image-processing-stuff": {"author_account_age_days": 3398, "last_update": "2025-05-10 05:51:42", "stars": 4}, "https://github.com/wolfden/ComfyUi_PromptStylers": {"author_account_age_days": 6089, "last_update": "2025-02-15 18:38:12", "stars": 95}, "https://github.com/wolfden/ComfyUi_String_Function_Tree": {"author_account_age_days": 6089, "last_update": "2024-05-22 18:29:16", "stars": 10}, "https://github.com/wootwootwootwoot/ComfyUI-RK-Sampler": {"author_account_age_days": 1935, "last_update": "2024-08-17 21:12:43", "stars": 58}, "https://github.com/wqjuser/ComfyUI-Chat-Image": {"author_account_age_days": 3301, "last_update": "2024-12-26 07:00:30", "stars": 0}, "https://github.com/wu12023/ComfyUI-Image-Evaluation": {"author_account_age_days": 684, "last_update": "2024-12-06 06:51:15", "stars": 9}, "https://github.com/wujm424606/ComfyUi-Ollama-YN": {"author_account_age_days": 2630, "last_update": "2024-09-17 13:20:02", "stars": 82}, "https://github.com/wutipong/ComfyUI-TextUtils": {"author_account_age_days": 4552, "last_update": "2024-06-14 09:34:31", "stars": 1}, "https://github.com/wwwins/ComfyUI-Simple-Aspect-Ratio": {"author_account_age_days": 5417, "last_update": "2024-05-22 22:22:25", "stars": 1}, "https://github.com/wywywywy/ComfyUI-pause": {"author_account_age_days": 3296, "last_update": "2025-05-05 21:37:34", "stars": 16}, "https://github.com/xLegende/ComfyUI-Prompt-Formatter": {"author_account_age_days": 1800, "last_update": "2025-06-10 19:29:54", "stars": 2}, "https://github.com/xXAdonesXx/NodeGPT": {"author_account_age_days": 1846, "last_update": "2024-06-20 11:41:30", "stars": 349}, "https://github.com/xfgexo/EXO-Custom-ComfyUI-Nodes": {"author_account_age_days": 796, "last_update": "2024-12-24 14:07:18", "stars": 2}, "https://github.com/xhiroga/ComfyUI-FramePackWrapper_PlusOne": {"author_account_age_days": 3631, "last_update": "2025-06-26 01:39:27", "stars": 20}, "https://github.com/xiaogui8dangjia/Comfyui-imagetoSTL": {"author_account_age_days": 2034, "last_update": "2025-06-06 04:08:30", "stars": 2}, "https://github.com/xiaowc-lib/comfyui-dynamic-params": {"author_account_age_days": 3246, "last_update": "2025-06-09 08:56:11", "stars": 0}, "https://github.com/xiaoxiaodesha/hd_node": {"author_account_age_days": 3242, "last_update": "2024-06-11 02:36:48", "stars": 15}, "https://github.com/xingBaGan/ComfyUI-connect-ui": {"author_account_age_days": 2162, "last_update": "2025-04-07 09:54:46", "stars": 1}, "https://github.com/xlinx/ComfyUI-decadetw-auto-messaging-realtime": {"author_account_age_days": 4866, "last_update": "2024-08-30 17:38:52", "stars": 8}, "https://github.com/xlinx/ComfyUI-decadetw-auto-prompt-llm": {"author_account_age_days": 4866, "last_update": "2025-02-01 18:36:52", "stars": 24}, "https://github.com/xlinx/ComfyUI-decadetw-spout-syphon-im-vj": {"author_account_age_days": 4866, "last_update": "2024-09-03 08:55:08", "stars": 12}, "https://github.com/xliry/ComfyUI_SendDiscord": {"author_account_age_days": 1641, "last_update": "2024-05-23 02:21:38", "stars": 0}, "https://github.com/xmarre/TorchCompileModel_LoRASafe": {"author_account_age_days": 2122, "last_update": "2025-06-06 18:40:09", "stars": 3}, "https://github.com/xobiomesh/ComfyUI_xObiomesh": {"author_account_age_days": 2057, "last_update": "2024-11-08 17:10:40", "stars": 2}, "https://github.com/xs315431/Comfyui_Get_promptId": {"author_account_age_days": 1642, "last_update": "2024-12-02 09:30:53", "stars": 1}, "https://github.com/xuhongming251/ComfyUI-GPEN": {"author_account_age_days": 4473, "last_update": "2025-04-16 21:37:02", "stars": 4}, "https://github.com/xuhongming251/ComfyUI-Jimeng": {"author_account_age_days": 4473, "last_update": "2025-06-11 09:39:59", "stars": 2}, "https://github.com/xuhongming251/ComfyUI-MuseTalkUtils": {"author_account_age_days": 4473, "last_update": "2025-04-16 21:36:46", "stars": 21}, "https://github.com/xuhongming251/ComfyUI_Camera": {"author_account_age_days": 4473, "last_update": "2025-05-05 18:30:40", "stars": 3}, "https://github.com/yanhuifair/comfyui-janus": {"author_account_age_days": 3929, "last_update": "2025-04-08 09:13:57", "stars": 4}, "https://github.com/yanlang0123/ComfyUI_Lam": {"author_account_age_days": 3176, "last_update": "2025-05-24 12:20:05", "stars": 44}, "https://github.com/yasser-baalla/comfyUI-SemanticImageFetch": {"author_account_age_days": 1764, "last_update": "2025-03-22 11:04:33", "stars": 0}, "https://github.com/ycchanau/ComfyUI_Preview_Magnifier": {"author_account_age_days": 2484, "last_update": "2024-07-31 13:59:12", "stars": 2}, "https://github.com/ycyy/ComfyUI-YCYY-LoraInfo": {"author_account_age_days": 3792, "last_update": "2024-09-30 02:33:25", "stars": 5}, "https://github.com/yffyhk/comfyui_auto_danbooru": {"author_account_age_days": 4089, "last_update": "2024-05-22 23:23:03", "stars": 1}, "https://github.com/yhayano-ponotech/ComfyUI-Fal-API-Flux": {"author_account_age_days": 938, "last_update": "2025-01-16 08:47:22", "stars": 56}, "https://github.com/yhayano-ponotech/comfyui-save-image-local": {"author_account_age_days": 938, "last_update": "2025-01-15 12:30:50", "stars": 4}, "https://github.com/yhayano-ponotech/comfyui-stability-ai-api": {"author_account_age_days": 938, "last_update": "2025-02-19 00:38:33", "stars": 0}, "https://github.com/yichengup/ComfyUI-LinearTransition": {"author_account_age_days": 492, "last_update": "2025-05-10 14:50:25", "stars": 1}, "https://github.com/yichengup/ComfyUI-YCNodes": {"author_account_age_days": 492, "last_update": "2025-06-08 17:30:52", "stars": 23}, "https://github.com/yichengup/ComfyUI_Yc_JanusPro": {"author_account_age_days": 492, "last_update": "2025-01-29 22:26:38", "stars": 7}, "https://github.com/yichengup/Comfyui-Deepseek": {"author_account_age_days": 492, "last_update": "2025-02-23 19:36:53", "stars": 32}, "https://github.com/yichengup/Comfyui-Ycanvas": {"author_account_age_days": 492, "last_update": "2024-12-22 01:26:50", "stars": 72}, "https://github.com/yichengup/Comfyui_Flux_Style_Adjust": {"author_account_age_days": 492, "last_update": "2025-02-19 05:08:27", "stars": 295}, "https://github.com/yichengup/Comfyui_Redux_Advanced": {"author_account_age_days": 492, "last_update": "2025-04-10 18:36:47", "stars": 98}, "https://github.com/yichengup/comfyui-face-liquify": {"author_account_age_days": 492, "last_update": "2025-05-08 17:59:05", "stars": 1}, "https://github.com/yiwangsimple/ComfyUI_DW_Chat": {"author_account_age_days": 919, "last_update": "2025-02-06 03:34:59", "stars": 88}, "https://github.com/yiwangsimple/florence_dw": {"author_account_age_days": 919, "last_update": "2025-02-13 01:52:15", "stars": 47}, "https://github.com/yogurt7771/ComfyUI-YogurtNodes": {"author_account_age_days": 3197, "last_update": "2025-06-25 03:25:33", "stars": 0}, "https://github.com/yolain/ComfyUI-Easy-Use": {"author_account_age_days": 1708, "last_update": "2025-06-26 08:45:49", "stars": 1697}, "https://github.com/yolanother/ComfyUI-Save16bitPng": {"author_account_age_days": 5231, "last_update": "2024-12-23 01:50:04", "stars": 3}, "https://github.com/yolanother/DTAIComfyImageSubmit": {"author_account_age_days": 5231, "last_update": "2024-09-25 04:40:23", "stars": 1}, "https://github.com/yolanother/DTAIComfyLoaders": {"author_account_age_days": 5231, "last_update": "2024-11-18 09:35:46", "stars": 1}, "https://github.com/yolanother/DTAIComfyPromptAgent": {"author_account_age_days": 5231, "last_update": "2024-05-22 18:14:18", "stars": 5}, "https://github.com/yolanother/DTAIComfyQRCodes": {"author_account_age_days": 5231, "last_update": "2024-05-22 18:15:09", "stars": 4}, "https://github.com/yolanother/DTAIComfyVariables": {"author_account_age_days": 5231, "last_update": "2024-05-22 18:15:21", "stars": 12}, "https://github.com/yolanother/DTAIImageToTextNode": {"author_account_age_days": 5231, "last_update": "2024-05-22 18:14:31", "stars": 19}, "https://github.com/yondonfu/ComfyUI-Background-Edit": {"author_account_age_days": 4243, "last_update": "2024-12-31 23:15:33", "stars": 22}, "https://github.com/yondonfu/ComfyUI-Torch-Compile": {"author_account_age_days": 4243, "last_update": "2025-04-30 18:46:47", "stars": 4}, "https://github.com/yorkane/ComfyUI-KYNode": {"author_account_age_days": 3757, "last_update": "2025-06-12 16:16:02", "stars": 7}, "https://github.com/younyokel/comfyui_prompt_formatter": {"author_account_age_days": 2162, "last_update": "2025-05-16 16:33:11", "stars": 3}, "https://github.com/youyegit/tdxh_node_comfyui": {"author_account_age_days": 796, "last_update": "2025-03-17 08:22:16", "stars": 2}, "https://github.com/yuan199696/add_text_2_img": {"author_account_age_days": 2810, "last_update": "2025-03-27 14:40:27", "stars": 7}, "https://github.com/yuan199696/chinese_clip_encode": {"author_account_age_days": 2810, "last_update": "2025-03-27 14:39:40", "stars": 9}, "https://github.com/yushan777/ComfyUI-Y7-SBS-2Dto3D": {"author_account_age_days": 885, "last_update": "2025-06-13 18:44:06", "stars": 4}, "https://github.com/yushan777/ComfyUI-Y7Nodes": {"author_account_age_days": 885, "last_update": "2025-06-14 19:55:01", "stars": 3}, "https://github.com/yuvraj108c/ComfyUI-Depth-Anything-Tensorrt": {"author_account_age_days": 2520, "last_update": "2025-05-20 08:34:27", "stars": 105}, "https://github.com/yuvraj108c/ComfyUI-Dwpose-Tensorrt": {"author_account_age_days": 2520, "last_update": "2025-05-03 19:32:24", "stars": 31}, "https://github.com/yuvraj108c/ComfyUI-FLOAT": {"author_account_age_days": 2520, "last_update": "2025-05-28 07:27:16", "stars": 191}, "https://github.com/yuvraj108c/ComfyUI-Facerestore-Tensorrt": {"author_account_age_days": 2520, "last_update": "2024-09-22 13:07:19", "stars": 20}, "https://github.com/yuvraj108c/ComfyUI-PiperTTS": {"author_account_age_days": 2520, "last_update": "2024-05-22 23:17:27", "stars": 28}, "https://github.com/yuvraj108c/ComfyUI-Pronodes": {"author_account_age_days": 2520, "last_update": "2025-01-05 10:06:31", "stars": 3}, "https://github.com/yuvraj108c/ComfyUI-Rife-Tensorrt": {"author_account_age_days": 2520, "last_update": "2024-10-04 10:23:26", "stars": 18}, "https://github.com/yuvraj108c/ComfyUI-Thera": {"author_account_age_days": 2520, "last_update": "2025-05-01 07:52:54", "stars": 35}, "https://github.com/yuvraj108c/ComfyUI-Upscaler-Tensorrt": {"author_account_age_days": 2520, "last_update": "2025-05-03 17:15:09", "stars": 136}, "https://github.com/yuvraj108c/ComfyUI-Video-Depth-Anything": {"author_account_age_days": 2520, "last_update": "2025-05-01 09:04:25", "stars": 28}, "https://github.com/yuvraj108c/ComfyUI-Vsgan": {"author_account_age_days": 2520, "last_update": "2024-05-22 23:17:02", "stars": 3}, "https://github.com/yuvraj108c/ComfyUI-Whisper": {"author_account_age_days": 2520, "last_update": "2025-05-02 07:59:15", "stars": 115}, "https://github.com/yuvraj108c/ComfyUI-YoloNasPose-Tensorrt": {"author_account_age_days": 2520, "last_update": "2024-06-28 15:59:14", "stars": 13}, "https://github.com/yuvraj108c/ComfyUI_InvSR": {"author_account_age_days": 2520, "last_update": "2025-04-30 08:19:02", "stars": 212}, "https://github.com/yvann-ba/ComfyUI_Yvann-Nodes": {"author_account_age_days": 1267, "last_update": "2025-06-02 12:11:14", "stars": 441}, "https://github.com/za-wa-n-go/ComfyUI_Zwng_Nodes": {"author_account_age_days": 955, "last_update": "2025-03-27 23:13:16", "stars": 7}, "https://github.com/zaheenrahman/ComfyUI-ColorCorrection": {"author_account_age_days": 2728, "last_update": "2025-03-21 09:52:29", "stars": 1}, "https://github.com/zakantonio/AvatarGen-experience": {"author_account_age_days": 4144, "last_update": "2025-03-26 20:58:18", "stars": 0}, "https://github.com/zccrs/comfyui-dci": {"author_account_age_days": 3627, "last_update": "2025-06-13 07:35:50", "stars": 1}, "https://github.com/zcfrank1st/Comfyui-Toolbox": {"author_account_age_days": 4783, "last_update": "2024-05-22 22:08:07", "stars": 6}, "https://github.com/zcfrank1st/Comfyui-Yolov8": {"author_account_age_days": 4783, "last_update": "2024-06-14 07:08:40", "stars": 25}, "https://github.com/zcfrank1st/comfyui_visual_anagrams": {"author_account_age_days": 4783, "last_update": "2024-06-14 07:07:27", "stars": 8}, "https://github.com/zentrocdot/ComfyUI-RealESRGAN_Upscaler": {"author_account_age_days": 578, "last_update": "2025-02-09 18:27:16", "stars": 7}, "https://github.com/zentrocdot/ComfyUI-Simple_Image_To_Prompt": {"author_account_age_days": 578, "last_update": "2025-02-20 06:30:19", "stars": 1}, "https://github.com/zentrocdot/ComfyUI_Circle_Detection": {"author_account_age_days": 578, "last_update": "2025-02-07 17:32:46", "stars": 1}, "https://github.com/zer0TF/cute-comfy": {"author_account_age_days": 3038, "last_update": "2024-05-22 21:18:53", "stars": 35}, "https://github.com/zer0thgear/zer0-comfy-utils": {"author_account_age_days": 484, "last_update": "2025-01-26 19:33:59", "stars": 0}, "https://github.com/zeroxoxo/ComfyUI-Fast-Style-Transfer": {"author_account_age_days": 2810, "last_update": "2025-04-07 05:52:19", "stars": 71}, "https://github.com/zfkun/ComfyUI_zfkun": {"author_account_age_days": 5230, "last_update": "2025-06-03 23:57:53", "stars": 21}, "https://github.com/zhangp365/ComfyUI-utils-nodes": {"author_account_age_days": 658, "last_update": "2025-06-26 01:24:18", "stars": 79}, "https://github.com/zhangp365/ComfyUI_photomakerV2_native": {"author_account_age_days": 658, "last_update": "2025-04-07 10:58:52", "stars": 10}, "https://github.com/zhilemann/ComfyUI-moondream2": {"author_account_age_days": 661, "last_update": "2024-12-29 13:17:31", "stars": 1}, "https://github.com/zhiselfly/ComfyUI-Alimama-ControlNet-compatible": {"author_account_age_days": 3717, "last_update": "2024-09-14 13:46:05", "stars": 18}, "https://github.com/zhongpei/ComfyUI-InstructIR": {"author_account_age_days": 3826, "last_update": "2024-05-22 23:19:43", "stars": 71}, "https://github.com/zhuanqianfish/ComfyUI-EasyNode": {"author_account_age_days": 4602, "last_update": "2024-06-14 07:10:18", "stars": 67}, "https://github.com/zhulu111/ComfyUI_Bxb": {"author_account_age_days": 406, "last_update": "2025-02-05 10:33:45", "stars": 1407}, "https://github.com/zichongc/ComfyUI-Attention-Distillation": {"author_account_age_days": 864, "last_update": "2025-03-18 02:48:42", "stars": 110}, "https://github.com/ziwang-com/comfyui-deepseek-r1": {"author_account_age_days": 3743, "last_update": "2025-02-02 14:24:35", "stars": 60}, "https://github.com/zmwv823/ComfyUI_Anytext": {"author_account_age_days": 3636, "last_update": "2025-05-28 01:02:37", "stars": 82}, "https://github.com/zohac/ComfyUI_ZC_DrawShape": {"author_account_age_days": 3029, "last_update": "2024-06-25 15:05:28", "stars": 3}, "https://github.com/zombieyang/sd-ppp": {"author_account_age_days": 4285, "last_update": "2025-06-19 14:40:11", "stars": 1526}, "https://github.com/zubenelakrab/ComfyUI-ASV-Nodes": {"author_account_age_days": 5329, "last_update": "2024-11-04 00:51:29", "stars": 1}, "https://github.com/zygion/comfyui-zygion-util-nodes": {"author_account_age_days": 173, "last_update": "2025-04-26 05:11:35", "stars": 0}, "https://github.com/zzubnik/TT_TextTools": {"author_account_age_days": 3093, "last_update": "2025-04-02 23:40:24", "stars": 0}, "https://github.com/zzw5516/ComfyUI-zw-tools": {"author_account_age_days": 4510, "last_update": "2025-04-16 08:24:48", "stars": 1}}