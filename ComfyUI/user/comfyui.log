## ComfyUI-Manager: installing dependencies done.
[2025-06-28 01:55:00.662] ** ComfyUI startup time: 2025-06-28 01:55:00.662
[2025-06-28 01:55:00.662] ** Platform: Darwin
[2025-06-28 01:55:00.662] ** Python version: 3.11.3 (main, Apr 19 2023, 18:49:55) [Clang 14.0.6 ]
[2025-06-28 01:55:00.662] ** Python executable: /Users/<USER>/anaconda3/bin/python3
[2025-06-28 01:55:00.662] ** ComfyUI Path: /Users/<USER>/Documents/Data/Data_Science/VS_Code/WAN2.1/ComfyUI
[2025-06-28 01:55:00.662] ** ComfyUI Base Folder Path: /Users/<USER>/Documents/Data/Data_Science/VS_Code/WAN2.1/ComfyUI
[2025-06-28 01:55:00.662] ** User directory: /Users/<USER>/Documents/Data/Data_Science/VS_Code/WAN2.1/ComfyUI/user
[2025-06-28 01:55:00.662] ** ComfyUI-Manager config path: /Users/<USER>/Documents/Data/Data_Science/VS_Code/WAN2.1/ComfyUI/user/default/ComfyUI-Manager/config.ini
[2025-06-28 01:55:00.662] ** Log path: /Users/<USER>/Documents/Data/Data_Science/VS_Code/WAN2.1/ComfyUI/user/comfyui.log

Prestartup times for custom nodes:
[2025-06-28 01:55:02.301]    3.3 seconds: /Users/<USER>/Documents/Data/Data_Science/VS_Code/WAN2.1/ComfyUI/custom_nodes/ComfyUI-Manager
[2025-06-28 01:55:02.301] 
[2025-06-28 01:55:03.425] Checkpoint files will always be loaded safely.
[2025-06-28 01:55:03.453] Total VRAM 16384 MB, total RAM 16384 MB
[2025-06-28 01:55:03.453] pytorch version: 2.7.1
[2025-06-28 01:55:03.454] Mac Version (16, 0)
[2025-06-28 01:55:03.454] Set vram state to: SHARED
[2025-06-28 01:55:03.454] Device: mps
