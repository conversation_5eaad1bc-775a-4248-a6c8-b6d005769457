{"https://github.com/BadCafeCode/execution-inversion-demo-comfyui": [["AccumulateNode", "AccumulationGetItemNode", "AccumulationGetLengthNode", "AccumulationHeadNode", "AccumulationSetItemNode", "AccumulationTailNode", "AccumulationToListNode", "BoolOperationNode", "ComponentInput", "ComponentMetadata", "ComponentOutput", "DebugPrint", "ExecutionBlocker", "FloatConditions", "ForLoopClose", "ForLoopOpen", "IntConditions", "IntMathOperation", "InversionDemoAdvancedPromptNode", "InversionDemoLazyConditional", "InversionDemoLazyIndexSwitch", "InversionDemoLazyMixImages", "InversionDemoLazySwitch", "ListToAccumulationNode", "MakeListNode", "StringConditions", "ToBoolNode", "WhileLoopClose", "WhileLoopOpen"], {"title_aux": "execution-inversion-demo-comfyui"}], "https://github.com/BetaDoggo/ComfyUI-Tetris": [["<PERSON><PERSON><PERSON>"], {"title_aux": "ComfyUI Tetris"}], "https://github.com/BoosterCore/ComfyUI-BC-Experimental": [["ClipTextEncodeBC", "ClipTextEncodeBCA", "FluxEmptyLatentSize", "LoraWithTriggerWord", "SaveAnyText", "SimpleText"], {"title_aux": "ComfyUI-BC-Experimental"}], "https://github.com/FlyMyAI/ComfyUI-ExampleNode": [["ExampleT2IFMANode"], {"title_aux": "ComfyUI-ExampleNode"}], "https://github.com/IvanRybakov/comfyui-node-int-to-string-convertor": [["Int To String"], {"title_aux": "comfyui-node-int-to-string-convertor"}], "https://github.com/LarryJane491/Custom-Node-Base": [["My First Node"], {"title_aux": "Custom-Node-Base"}], "https://github.com/OuticNZ/ComfyUI-Simple-Of-Complex": [["Pipe From Parameters", "Pipe To Parameters", "Prompt Tidy", "Text Switch 2 Way", "Text With Context"], {"title_aux": "ComfyUI-Simple-Of-Complex"}], "https://github.com/Suzie1/ComfyUI_Guide_To_Making_Custom_Nodes": [["Concatenate Hello World", "Hello World Overlay Text", "Print Hello World"], {"title_aux": "Guide To Making Custom Nodes in ComfyUI"}], "https://github.com/Wanghanying/ComfyUI_RAGDemo": [["testRAG"], {"title_aux": "ComfyUI_RAGDemo"}], "https://github.com/azure-dragon-ai/ComfyUI-HPSv2-Nodes": [["GetImageSize", "HaojihuiHPSv2ImageProcessor", "HaojihuiHPSv2ImageScore", "HaojihuiHPSv2ImageScores", "HaojihuiHPSv2Loader", "HaojihuiHPSv2SaveAnimatedWEBP", "HaojihuiHPSv2SaveImage", "HaojihuiHPSv2SaveWEBP", "HaojihuiHPSv2SaveWebpImage", "HaojihuiHPSv2TextProcessor", "SaveImageWebp", "ScaleShort"], {"title_aux": "ComfyUI-HPSv2-Nodes"}], "https://github.com/bamboodia/BAM_Nodes": [["BAM Crop To Ratio", "BAM Empty Latent By Ratio", "BAM Get Shortest Side", "BAM OnOff INT", "BAM Random Float", "BAM Random Image From Folder"], {"title_aux": "BAM Nodes"}], "https://github.com/boricuapab/ComfyUI_BoricuapabWFNodePack": [["BoricuapabWF Concatenate Hello World", "BoricuapabWF Integer", "BoricuapabWF Print Hello Puerto Rican World", "BoricuapabWF Print Puerto Rican"], {"title_aux": "ComfyUI_BoricuapabWFNodePack"}], "https://github.com/comfyanonymous/ComfyUI": [["AddNoise", "AlignYourStepsScheduler", "BasicGuider", "BasicScheduler", "BetaSamplingScheduler", "CFGGuider", "CLIPAttentionMultiply", "CLIPLoader", "CLIPMergeAdd", "CLIPMergeSimple", "CLIPMergeSubtract", "CLIPSave", "CLIPSetLastLayer", "CLIPTextEncode", "CLIPTextEncodeControlnet", "CLIPTextEncodeFlux", "CLIPTextEncodeHunyuanDiT", "CLIPTextEncodePixArtAlpha", "CLIPTextEncodeSD3", "CLIPTextEncodeSDXL", "CLIPTextEncodeSDXLRefiner", "CLIPVisionEncode", "CLIPVisionLoader", "<PERSON><PERSON>", "CheckpointLoader", "CheckpointLoaderSimple", "CheckpointSave", "ConditioningAverage", "Conditioning<PERSON><PERSON><PERSON>", "ConditioningConcat", "ConditioningSetArea", "ConditioningSetAreaPercentage", "ConditioningSetAreaStrength", "ConditioningSetMask", "ConditioningSetTimestepRange", "ConditioningStableAudio", "ConditioningZeroOut", "ControlNetApply", "ControlNetApplyAdvanced", "ControlNetApplySD3", "ControlNetInpaintingAliMamaApply", "ControlNetLoader", "CropMask", "DiffControlNetLoader", "DifferentialDiffusion", "Diff<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "DualCFGGuider", "DualCLIPLoader", "EmptyHunyuanLatentVideo", "EmptyImage", "EmptyLTXVLatentVideo", "EmptyLatentAudio", "EmptyLatentImage", "EmptyMochiLatentVideo", "EmptySD3LatentImage", "ExponentialScheduler", "FeatherMask", "FlipSigmas", "FluxGuidance", "FreeU", "FreeU_V2", "GITSScheduler", "GLIGEN<PERSON><PERSON>der", "GLIGENTextBoxApply", "GrowMask", "HyperTile", "HypernetworkLoader", "ImageBatch", "ImageBlend", "ImageBlur", "ImageColorToMask", "ImageCompositeMasked", "ImageCrop", "ImageFromBatch", "ImageInvert", "ImageOnlyCheckpointLoader", "ImageOnlyCheckpointSave", "ImagePadForOutpaint", "ImageQuantize", "ImageScale", "ImageScaleBy", "ImageScaleToTotalPixels", "ImageSharpen", "ImageToMask", "ImageUpscaleWithModel", "InpaintModelConditioning", "InstructPixToPixConditioning", "InvertMask", "JoinImageWithAlpha", "K<PERSON><PERSON><PERSON>", "KSamplerAdvanced", "KSamplerSelect", "KarrasScheduler", "LTXVConditioning", "LTXVImgToVideo", "LTXVScheduler", "LaplaceScheduler", "LatentAdd", "LatentApplyOperation", "LatentApplyOperationCFG", "LatentBatch", "LatentBatchSeedBehavior", "LatentBlend", "LatentComposite", "LatentCompositeMasked", "LatentCrop", "LatentFlip", "LatentFromBatch", "LatentInterpolate", "Latent<PERSON><PERSON><PERSON>ly", "LatentOperationSharpen", "LatentOperationTonemapReinhard", "LatentRotate", "LatentSubtract", "LatentUpscale", "LatentUpscaleBy", "Load3D", "Load3DAnimation", "LoadAudio", "LoadImage", "LoadImageMask", "LoadLatent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LoraLoaderModelOnly", "LoraSave", "<PERSON><PERSON>", "MaskComposite", "MaskToImage", "ModelMergeAdd", "ModelMergeAuraflow", "ModelMergeBlocks", "ModelMergeFlux1", "ModelMergeLTXV", "ModelMergeMochiPreview", "ModelMergeSD1", "ModelMergeSD2", "ModelMergeSD35_Large", "ModelMergeSD3_2B", "ModelMergeSDXL", "ModelMergeSimple", "ModelMergeSubtract", "ModelSamplingAuraFlow", "ModelSamplingContinuousEDM", "ModelSamplingContinuousV", "ModelSamplingDiscrete", "ModelSamplingFlux", "ModelSamplingLTXV", "ModelSamplingSD3", "ModelSamplingStableCascade", "ModelSave", "Morphology", "PatchModelAddDownscale", "PerpNeg", "PerpNegGuider", "PerturbedAttentionGuidance", "PhotoMakerEncode", "PhotoMaker<PERSON><PERSON>der", "PolyexponentialScheduler", "PorterDuffImageComposite", "Preview3D", "PreviewAudio", "PreviewImage", "RandomNoise", "RebatchImages", "RebatchLatents", "RepeatImageBatch", "RepeatLatentBatch", "RescaleCFG", "SDTurboScheduler", "SD_4XUpscale_Conditioning", "SV3D_Conditioning", "SVD_img2vid_Conditioning", "SamplerCustom", "SamplerCustomAdvanced", "SamplerDPMAdaptative", "SamplerDPMPP_2M_SDE", "SamplerDPMPP_2S_Ancestral", "SamplerDPMPP_3M_SDE", "SamplerDPMPP_SDE", "SamplerEulerAncestral", "SamplerEulerAncestralCFGPP", "SamplerEulerCFGpp", "SamplerLCMUpscale", "SamplerLMS", "SaveAnimatedPNG", "SaveAnimatedWEBP", "SaveAudio", "SaveImage", "SaveImageWebsocket", "SaveLatent", "SelfAttentionGuidance", "SetLatentNoiseMask", "SetUnionControlNetType", "SkipLayerGuidanceDiT", "SkipLayerGuidanceSD3", "SolidMask", "SplitImageWithAlpha", "SplitSigmas", "SplitSigmasDenoise", "StableCascade_EmptyLatentImage", "StableCascade_StageB_Conditioning", "StableCascade_StageC_VAEEncode", "StableCascade_SuperResolutionControlnet", "StableZero123_Conditioning", "StableZero123_Conditioning_Batched", "StubConstantImage", "StubFloat", "StubImage", "StubInt", "StubMask", "StyleModelApply", "StyleModelLoader", "TestAccumulateNode", "TestAccumulationGetItemNode", "TestAccumulationGetLengthNode", "TestAccumulationHeadNode", "TestAccumulationSetItemNode", "TestAccumulationTailNode", "TestAccumulationToListNode", "TestBoolOperationNode", "TestCustomIsChanged", "TestCustomValidation1", "TestCustomValidation2", "TestCustomValidation3", "TestCustomValidation4", "TestCustomValidation5", "TestDynamicDependencyCycle", "TestExecutionBlocker", "TestFloatConditions", "TestForLoopClose", "TestForLoopOpen", "TestIntConditions", "TestIntMathOperation", "TestIsChangedWithConstants", "TestLazyMixImages", "TestListToAccumulationNode", "TestMakeListNode", "TestMixedExpansionReturns", "TestStringConditions", "TestToBoolNode", "TestVariadicAverage", "TestWhileLoopClose", "TestWhileLoopOpen", "ThresholdMask", "TomePatchModel", "TorchCompileModel", "TripleCLIPLoader", "UNETLoader", "UNetCrossAttentionMultiply", "UNetSelfAttentionMultiply", "UNetTemporalAttentionMultiply", "UpscaleModelLoader", "VAEDecode", "VAEDecodeAudio", "VAEDecodeTiled", "VAEEncode", "VAEEncodeAudio", "VAEEncodeForInpaint", "VAEEncodeTiled", "VAELoader", "VAESave", "VPScheduler", "VideoLinearCFGGuidance", "VideoTriangleCFGGuidance", "WebcamCapture", "unCLIPCheckpointLoader", "unCLIPConditioning"], {"title_aux": "ComfyUI"}], "https://github.com/dynamixar/Atluris": [["RandomLine"], {"title_aux": "<PERSON><PERSON><PERSON>"}], "https://github.com/ecjojo/ecjojo-example-nodes": [["BiggerNote_Example", "DisplayTextNode_Example", "EmptyNode_Example", "ExampleNode_Example", "FilePrefixNode_Example", "HelloWorldNode_Example", "RandomSizeNode_Example", "StringNode_Example", "TextOverlayNode_Example"], {"title_aux": "ecjojo_example_nodes"}], "https://github.com/et118/ComfyUI-ElGogh-Nodes": [["ElGoghCLIPSetLastLayer", "ElGoghCheckpointLoaderSimple", "ElGoghEmptyLatentImage", "ElGoghKSamplerAdvanced", "ElGoghNegativePrompt", "ElGoghPositivePrompt", "ElGoghPrimaryLoraLoader", "ElGoghSecondaryLoraLoader", "ElGoghSendWebsocketNSFWBool", "ElGoghTertiaryLoraLoader", "ElGoghVAELoader"], {"title_aux": "ComfyUI-ElGogh-Nodes"}], "https://github.com/foxtrot-roger/comfyui-custom-nodes": [["RF_Tutorial"], {"title_aux": "comfyui-custom-nodes"}], "https://github.com/jhj0517/ComfyUI-CustomNodes-Template": [["(Down)Load My Model", "<PERSON><PERSON> Minus", "Calculate Plus", "Example Output Node"], {"title_aux": "ComfyUI-CustomNodes-Template"}], "https://github.com/jtong/comfyui-jtong-workflow": [["Example", "high_workflow_caller", "jtong.<PERSON>", "jtong.Highway"], {"author": "Trung0246", "description": "Random nodes for ComfyUI I made to solve my struggle with ComfyUI (ex: pipe, process). Have varying quality.", "nickname": "ComfyUI-0246", "title": "ComfyUI-0246", "title_aux": "comfyui-jtong-workflow"}], "https://github.com/kappa54m/ComfyUI_Usability": [["KLoadImageByPath", "KLoadImageByPathAdvanced", "KLoadImageDedup"], {"title_aux": "ComfyUI-HPSv2-Nodes"}], "https://github.com/mira-6/mira-wildcard-node": [["MiraWildcard"], {"author": "mira-6", "description": "Single-node wildcard implementation.", "nickname": "mira-wildcard-node", "title": "mira-wildcard-node", "title_aux": "mira-wildcard-node"}], "https://github.com/sonyeon-sj/ComfyUI-easy_ImageSize_Selecter": [["ImageSizer", "promptSelecter"], {"title_aux": "ComfyUI-easy_ImageSize_Selecter"}], "https://github.com/thinkthinking/ComfyUI-Ye": [["CheckpointLoader|Ye", "OllamaVision|Ye", "PrintHelloWorld|Ye", "Signature|Ye"], {"title_aux": "ComfyUI-Ye"}], "https://github.com/wailovet/ComfyUI-WW": [["WW_AccumulationPreviewImages", "WW_AppendString", "WW_CurrentPreviewImages", "WW_ImageResize", "WW_PreviewTextNode", "WW_RandString"], {"title_aux": "ComfyUI-WW"}], "https://github.com/yowipr/ComfyUI-Manual": [["EXAMPLE", "<PERSON><PERSON><PERSON><PERSON>", "M_Output", "M_RenderArea"], {"title_aux": "ComfyUI-Manual"}]}