{"cells": [{"cell_type": "markdown", "metadata": {"id": "aaaaaaaaaa"}, "source": ["Git clone the repo and install the requirements. (ignore the pip errors about protobuf)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bbbbbbbbbb"}, "outputs": [], "source": ["# #@title Environment Setup\n", "\n", "from pathlib import Path\n", "\n", "OPTIONS = {}\n", "\n", "USE_GOOGLE_DRIVE = True  #@param {type:\"boolean\"}\n", "UPDATE_COMFY_UI = True  #@param {type:\"boolean\"}\n", "USE_COMFYUI_MANAGER = True  #@param {type:\"boolean\"}\n", "INSTALL_CUSTOM_NODES_DEPENDENCIES = True  #@param {type:\"boolean\"}\n", "OPTIONS['USE_GOOGLE_DRIVE'] = USE_GOOGLE_DRIVE\n", "OPTIONS['UPDATE_COMFY_UI'] = UPDATE_COMFY_UI\n", "OPTIONS['USE_COMFYUI_MANAGER'] = USE_COMFYUI_MANAGER\n", "OPTIONS['INSTALL_CUSTOM_NODES_DEPENDENCIES'] = INSTALL_CUSTOM_NODES_DEPENDENCIES\n", "\n", "current_dir = !pwd\n", "WORKSPACE = f\"{current_dir[0]}/ComfyUI\"\n", "\n", "if OPTIONS['USE_GOOGLE_DRIVE']:\n", "    !echo \"Mounting Google Drive...\"\n", "    %cd /\n", "\n", "    from google.colab import drive\n", "    drive.mount('/content/drive')\n", "\n", "    WORKSPACE = \"/content/drive/MyDrive/ComfyUI\"\n", "    %cd /content/drive/MyDrive\n", "\n", "![ ! -d $WORKSPACE ] && echo -= Initial setup ComfyUI =- && git clone https://github.com/comfyanonymous/ComfyUI\n", "%cd $WORKSPACE\n", "\n", "if OPTIONS['UPDATE_COMFY_UI']:\n", "  !echo -= Updating ComfyUI =-\n", "\n", "  # Correction of the issue of permissions being deleted on Google Drive.\n", "  ![ -f \".ci/nightly/update_windows/update_comfyui_and_python_dependencies.bat\" ] && chmod 755 .ci/nightly/update_windows/update_comfyui_and_python_dependencies.bat\n", "  ![ -f \".ci/nightly/windows_base_files/run_nvidia_gpu.bat\" ] && chmod 755 .ci/nightly/windows_base_files/run_nvidia_gpu.bat\n", "  ![ -f \".ci/update_windows/update_comfyui_and_python_dependencies.bat\" ] && chmod 755 .ci/update_windows/update_comfyui_and_python_dependencies.bat\n", "  ![ -f \".ci/update_windows_cu118/update_comfyui_and_python_dependencies.bat\" ] && chmod 755 .ci/update_windows_cu118/update_comfyui_and_python_dependencies.bat\n", "  ![ -f \".ci/update_windows/update.py\" ] && chmod 755 .ci/update_windows/update.py\n", "  ![ -f \".ci/update_windows/update_comfyui.bat\" ] && chmod 755 .ci/update_windows/update_comfyui.bat\n", "  ![ -f \".ci/update_windows/README_VERY_IMPORTANT.txt\" ] && chmod 755 .ci/update_windows/README_VERY_IMPORTANT.txt\n", "  ![ -f \".ci/update_windows/run_cpu.bat\" ] && chmod 755 .ci/update_windows/run_cpu.bat\n", "  ![ -f \".ci/update_windows/run_nvidia_gpu.bat\" ] && chmod 755 .ci/update_windows/run_nvidia_gpu.bat\n", "\n", "  !git pull\n", "\n", "!echo -= Install dependencies =-\n", "!pip3 install accelerate\n", "!pip3 install einops transformers>=4.28.1 safetensors>=0.4.2 aiohttp pyyaml Pillow scipy tqdm psutil tokenizers>=0.13.3\n", "!pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121\n", "!pip3 install torchsde\n", "!pip3 install kornia>=0.7.1 spandrel soundfile sentencepiece\n", "\n", "if OPTIONS['USE_COMFYUI_MANAGER']:\n", "  %cd custom_nodes\n", "\n", "  # Correction of the issue of permissions being deleted on Google Drive.\n", "  ![ -f \"ComfyUI-Manager/check.sh\" ] && chmod 755 ComfyUI-Manager/check.sh\n", "  ![ -f \"ComfyUI-Manager/scan.sh\" ] && chmod 755 ComfyUI-Manager/scan.sh\n", "  ![ -f \"ComfyUI-Manager/node_db/dev/scan.sh\" ] && chmod 755 ComfyUI-Manager/node_db/dev/scan.sh\n", "  ![ -f \"ComfyUI-Manager/node_db/tutorial/scan.sh\" ] && chmod 755 ComfyUI-Manager/node_db/tutorial/scan.sh\n", "  ![ -f \"ComfyUI-Manager/scripts/install-comfyui-venv-linux.sh\" ] && chmod 755 ComfyUI-Manager/scripts/install-comfyui-venv-linux.sh\n", "  ![ -f \"ComfyUI-Manager/scripts/install-comfyui-venv-win.bat\" ] && chmod 755 ComfyUI-Manager/scripts/install-comfyui-venv-win.bat\n", "\n", "  ![ ! -d ComfyUI-Manager ] && echo -= Initial setup ComfyUI-Manager =- && git clone https://github.com/ltdrdata/ComfyUI-Manager\n", "  %cd ComfyUI-Manager\n", "  !git pull\n", "\n", "%cd $WORKSPACE\n", "\n", "if OPTIONS['INSTALL_CUSTOM_NODES_DEPENDENCIES']:\n", "  !echo -= Install custom nodes dependencies =-\n", "  !pip install GitPython\n", "  !python custom_nodes/ComfyUI-Manager/cm-cli.py restore-dependencies\n"]}, {"cell_type": "markdown", "metadata": {"id": "cccccccccc"}, "source": ["Download some models/checkpoints/vae or custom comfyui nodes (uncomment the commands for the ones you want)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "dddddddddd"}, "outputs": [], "source": ["# Checkpoints\n", "\n", "### SDXL\n", "### I recommend these workflow examples: https://comfyanonymous.github.io/ComfyUI_examples/sdxl/\n", "\n", "#!wget -c https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_base_1.0.safetensors -P ./models/checkpoints/\n", "#!wget -c https://huggingface.co/stabilityai/stable-diffusion-xl-refiner-1.0/resolve/main/sd_xl_refiner_1.0.safetensors -P ./models/checkpoints/\n", "\n", "# SDXL ReVision\n", "#!wget -c https://huggingface.co/comfyanonymous/clip_vision_g/resolve/main/clip_vision_g.safetensors -P ./models/clip_vision/\n", "\n", "# SD1.5\n", "!wget -c https://huggingface.co/runwayml/stable-diffusion-v1-5/resolve/main/v1-5-pruned-emaonly.ckpt -P ./models/checkpoints/\n", "\n", "# SD2\n", "#!wget -c https://huggingface.co/stabilityai/stable-diffusion-2-1-base/resolve/main/v2-1_512-ema-pruned.safetensors -P ./models/checkpoints/\n", "#!wget -c https://huggingface.co/stabilityai/stable-diffusion-2-1/resolve/main/v2-1_768-ema-pruned.safetensors -P ./models/checkpoints/\n", "\n", "# Some SD1.5 anime style\n", "#!wget -c https://huggingface.co/WarriorMama777/OrangeMixs/resolve/main/Models/AbyssOrangeMix2/AbyssOrangeMix2_hard.safetensors -P ./models/checkpoints/\n", "#!wget -c https://huggingface.co/WarriorMama777/OrangeMixs/resolve/main/Models/AbyssOrangeMix3/AOM3A1_orangemixs.safetensors -P ./models/checkpoints/\n", "#!wget -c https://huggingface.co/WarriorMama777/OrangeMixs/resolve/main/Models/AbyssOrangeMix3/AOM3A3_orangemixs.safetensors -P ./models/checkpoints/\n", "#!wget -c https://huggingface.co/Linaqruf/anything-v3.0/resolve/main/anything-v3-fp16-pruned.safetensors -P ./models/checkpoints/\n", "\n", "# Waifu Diffusion 1.5 (anime style SD2.x 768-v)\n", "#!wget -c https://huggingface.co/waifu-diffusion/wd-1-5-beta3/resolve/main/wd-illusion-fp16.safetensors -P ./models/checkpoints/\n", "\n", "\n", "# unCLIP models\n", "#!wget -c https://huggingface.co/comfyanonymous/illuminatiDiffusionV1_v11_unCLIP/resolve/main/illuminatiDiffusionV1_v11-unclip-h-fp16.safetensors -P ./models/checkpoints/\n", "#!wget -c https://huggingface.co/comfyanonymous/wd-1.5-beta2_unCLIP/resolve/main/wd-1-5-beta2-aesthetic-unclip-h-fp16.safetensors -P ./models/checkpoints/\n", "\n", "\n", "# VAE\n", "!wget -c https://huggingface.co/stabilityai/sd-vae-ft-mse-original/resolve/main/vae-ft-mse-840000-ema-pruned.safetensors -P ./models/vae/\n", "#!wget -c https://huggingface.co/WarriorMama777/OrangeMixs/resolve/main/VAEs/orangemix.vae.pt -P ./models/vae/\n", "#!wget -c https://huggingface.co/hakurei/waifu-diffusion-v1-4/resolve/main/vae/kl-f8-anime2.ckpt -P ./models/vae/\n", "\n", "\n", "# Loras\n", "#!wget -c https://civitai.com/api/download/models/10350 -O ./models/loras/theovercomer8sContrastFix_sd21768.safetensors #theovercomer8sContrastFix SD2.x 768-v\n", "#!wget -c https://civitai.com/api/download/models/10638 -O ./models/loras/theovercomer8sContrastFix_sd15.safetensors #theovercomer8sContrastFix SD1.x\n", "#!wget -c https://huggingface.co/stabilityai/stable-diffusion-xl-base-1.0/resolve/main/sd_xl_offset_example-lora_1.0.safetensors -P ./models/loras/ #SDXL offset noise lora\n", "\n", "\n", "# T2I-Adapter\n", "#!wget -c https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_depth_sd14v1.pth -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_seg_sd14v1.pth -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_sketch_sd14v1.pth -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_keypose_sd14v1.pth -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_openpose_sd14v1.pth -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_color_sd14v1.pth -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_canny_sd14v1.pth -P ./models/controlnet/\n", "\n", "# T2I Styles Model\n", "#!wget -c https://huggingface.co/TencentARC/T2I-Adapter/resolve/main/models/t2iadapter_style_sd14v1.pth -P ./models/style_models/\n", "\n", "# CLIPVision model (needed for styles model)\n", "#!wget -c https://huggingface.co/openai/clip-vit-large-patch14/resolve/main/pytorch_model.bin -O ./models/clip_vision/clip_vit14.bin\n", "\n", "\n", "# ControlNet\n", "#!wget -c https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11e_sd15_ip2p_fp16.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11e_sd15_shuffle_fp16.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_canny_fp16.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11f1p_sd15_depth_fp16.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_inpaint_fp16.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_lineart_fp16.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_mlsd_fp16.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_normalbae_fp16.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_openpose_fp16.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_scribble_fp16.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_seg_fp16.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15_softedge_fp16.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11p_sd15s2_lineart_anime_fp16.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/comfyanonymous/ControlNet-v1-1_fp16_safetensors/resolve/main/control_v11u_sd15_tile_fp16.safetensors -P ./models/controlnet/\n", "\n", "# ControlNet SDXL\n", "#!wget -c https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank256/control-lora-canny-rank256.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank256/control-lora-depth-rank256.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank256/control-lora-recolor-rank256.safetensors -P ./models/controlnet/\n", "#!wget -c https://huggingface.co/stabilityai/control-lora/resolve/main/control-LoRAs-rank256/control-lora-sketch-rank256.safetensors -P ./models/controlnet/\n", "\n", "# Controlnet Preprocessor nodes by Fannovel16\n", "#!cd custom_nodes && git clone https://github.com/Fannovel16/comfy_controlnet_preprocessors; cd comfy_controlnet_preprocessors && python install.py\n", "\n", "\n", "# G<PERSON><PERSON><PERSON>\n", "#!wget -c https://huggingface.co/comfyanonymous/GLIGEN_pruned_safetensors/resolve/main/gligen_sd14_textbox_pruned_fp16.safetensors -P ./models/gligen/\n", "\n", "\n", "# ESRGAN upscale model\n", "#!wget -c https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth -P ./models/upscale_models/\n", "#!wget -c https://huggingface.co/sberbank-ai/Real-ESRGAN/resolve/main/RealESRGAN_x2.pth -P ./models/upscale_models/\n", "#!wget -c https://huggingface.co/sberbank-ai/Real-ESRGAN/resolve/main/RealESRGAN_x4.pth -P ./models/upscale_models/\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "kkkkkkkkkkkkkkk"}, "source": ["### Run ComfyUI with cloudflared (Recommended Way)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jjjjjjjjjjjjjj"}, "outputs": [], "source": ["!wget -P ~ https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb\n", "!dpkg -i ~/cloudflared-linux-amd64.deb\n", "\n", "import subprocess\n", "import threading\n", "import time\n", "import socket\n", "import urllib.request\n", "\n", "def iframe_thread(port):\n", "  while True:\n", "      time.sleep(0.5)\n", "      sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "      result = sock.connect_ex(('127.0.0.1', port))\n", "      if result == 0:\n", "        break\n", "      sock.close()\n", "  print(\"\\nComfyUI finished loading, trying to launch cloudflared (if it gets stuck here cloudflared is having issues)\\n\")\n", "\n", "  p = subprocess.Popen([\"cloudflared\", \"tunnel\", \"--url\", \"http://127.0.0.1:{}\".format(port)], stdout=subprocess.PIPE, stderr=subprocess.PIPE)\n", "  for line in p.stderr:\n", "    l = line.decode()\n", "    if \"trycloudflare.com \" in l:\n", "      print(\"This is the URL to access ComfyUI:\", l[l.find(\"http\"):], end='')\n", "    #print(l, end='')\n", "\n", "\n", "threading.Thread(target=iframe_thread, daemon=True, args=(8188,)).start()\n", "\n", "!python main.py --dont-print-server"]}, {"cell_type": "markdown", "metadata": {"id": "kkkkkkkkkkkkkk"}, "source": ["### Run ComfyUI with localtunnel\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jjjjjjjjjjjjj"}, "outputs": [], "source": ["!npm install -g localtunnel\n", "\n", "import subprocess\n", "import threading\n", "import time\n", "import socket\n", "import urllib.request\n", "\n", "def iframe_thread(port):\n", "  while True:\n", "      time.sleep(0.5)\n", "      sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "      result = sock.connect_ex(('127.0.0.1', port))\n", "      if result == 0:\n", "        break\n", "      sock.close()\n", "  print(\"\\nComfyUI finished loading, trying to launch localtunnel (if it gets stuck here localtunnel is having issues)\\n\")\n", "\n", "  print(\"The password/enpoint ip for localtunnel is:\", urllib.request.urlopen('https://ipv4.icanhazip.com').read().decode('utf8').strip(\"\\n\"))\n", "  p = subprocess.Popen([\"lt\", \"--port\", \"{}\".format(port)], stdout=subprocess.PIPE)\n", "  for line in p.stdout:\n", "    print(line.decode(), end='')\n", "\n", "\n", "threading.Thread(target=iframe_thread, daemon=True, args=(8188,)).start()\n", "\n", "!python main.py --dont-print-server"]}, {"cell_type": "markdown", "metadata": {"id": "gggggggggg"}, "source": ["### Run ComfyUI with colab iframe (use only in case the previous way with localtunnel doesn't work)\n", "\n", "You should see the ui appear in an iframe. If you get a 403 error, it's your firefox settings or an extension that's messing things up.\n", "\n", "If you want to open it in another window use the link.\n", "\n", "Note that some UI features like live image previews won't work because the colab iframe blocks websockets."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hhhhhhhhhh"}, "outputs": [], "source": ["import threading\n", "import time\n", "import socket\n", "def iframe_thread(port):\n", "  while True:\n", "      time.sleep(0.5)\n", "      sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "      result = sock.connect_ex(('127.0.0.1', port))\n", "      if result == 0:\n", "        break\n", "      sock.close()\n", "  from google.colab import output\n", "  output.serve_kernel_port_as_iframe(port, height=1024)\n", "  print(\"to open it in a window you can open this link here:\")\n", "  output.serve_kernel_port_as_window(port)\n", "\n", "threading.Thread(target=iframe_thread, daemon=True, args=(8188,)).start()\n", "\n", "!python main.py --dont-print-server"]}], "metadata": {"accelerator": "GPU", "colab": {"provenance": []}, "gpuClass": "standard", "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}