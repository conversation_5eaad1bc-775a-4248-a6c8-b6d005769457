var t={915:(t,e,i)=>{i.d(e,{A:()=>l});var o=i(256),n=i.n(o),s=i(505),r=i.n(s)()(n());r.push([t.id,'.tg-turbogrid{position:relative;z-index:0;width:100%;height:100%;margin:0;padding:0;box-sizing:border-box;font-size:14px;font-family:arial,sans-serif;outline:0;cursor:default;overflow:hidden}.tg-turbogrid *,.tg-turbogrid *::before,.tg-turbogrid *::after{box-sizing:border-box}.tg-text-unselectable.tg-turbogrid{user-select:none}.tg-turbogrid svg{display:block;pointer-events:none}.tg-turbogrid .tg-symbols{font-family:webdings,sans-serif}.tg-turbogrid .tg-nowrap{white-space:nowrap}.tg-turbogrid .tg-align-left{text-align:left}.tg-turbogrid .tg-align-center{text-align:center}.tg-turbogrid .tg-align-right{text-align:right}@keyframes tg-fade-in{from{opacity:0}to{opacity:1}}@keyframes tg-fade-out{from{opacity:1}to{opacity:0}}.tg-turbogrid .tg-fade-in{animation-name:tg-fade-in;animation-duration:.2s;animation-fill-mode:both}.tg-turbogrid .tg-fade-in .tg-scrollbar-track{display:none}.tg-turbogrid .tg-fade-out{animation-name:tg-fade-out;animation-duration:.2s;animation-fill-mode:both}.tg-turbogrid .tg-fade-out .tg-scrollbar-track{display:none}.tg-turbogrid .tg-mask{position:absolute;top:0;left:0;z-index:200;display:none;width:100%;height:100%;background-color:#000;opacity:.1}@keyframes tg-loading-animation{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.tg-turbogrid .tg-loading{position:absolute;top:50%;left:50%;z-index:300;display:none;transform:translate(-50%, -50%);pointer-events:none}.tg-turbogrid .tg-loading-default{width:35px;height:35px;color:#0077cf;animation:1s tg-loading-animation linear infinite}.tg-turbogrid .tg-loading-fast{animation:.382s tg-loading-animation linear infinite}.tg-turbogrid .tg-checkbox{width:100%;height:100%;cursor:pointer;overflow:hidden}.tg-turbogrid .tg-checkbox:hover .tg-checkbox-item{fill:#005ba1}.tg-turbogrid .tg-checkbox.tg-select-icon-all{height:18px}.tg-turbogrid .tg-checkbox .tg-icon-checkbox{position:absolute;top:50%;left:50%;display:block;width:16px;height:16px;transform:translate(-50%, -50%)}.tg-turbogrid .tg-checkbox .tg-checkbox-item{display:none;fill:gray}.tg-turbogrid .tg-checkbox .tg-checkbox-none{display:block}.tg-turbogrid .tg-checkbox.tg-selected .tg-checkbox-selected{display:block;fill:#0077cf}.tg-turbogrid .tg-checkbox.tg-mixed .tg-checkbox-mixed{display:block;fill:#0077cf}.tg-turbogrid .tg-radio{cursor:pointer;overflow:hidden}.tg-turbogrid .tg-radio:hover .tg-icon-radio::before{border-color:#005ba1}.tg-turbogrid .tg-radio .tg-icon-radio{position:absolute;top:50%;left:50%;width:16px;height:16px;transform:translate(-50%, -50%)}.tg-turbogrid .tg-radio .tg-icon-radio::before{position:absolute;top:50%;left:50%;content:"";display:block;width:16px;height:16px;border:thin solid gray;border-radius:50%;background:#fff;transform:translate(-50%, -50%)}.tg-turbogrid .tg-radio .tg-icon-radio::after{position:absolute;top:50%;left:50%;content:"";display:none;width:10px;height:10px;border-radius:50%;background:#0077cf;transform:translate(-50%, -50%)}.tg-turbogrid .tg-radio.tg-selected .tg-icon-radio::after{display:block;border-color:#0077cf}.tg-turbogrid .tg-scrollbar{position:absolute;z-index:100;overflow:hidden;user-select:none}.tg-turbogrid .tg-scrollbar-v{top:0;right:0}.tg-turbogrid .tg-scrollbar-h{left:0;bottom:0}.tg-turbogrid .tg-scrollbar-track{position:relative;width:100%;height:100%;background:#f9f9f9;overflow:hidden;user-select:none}.tg-turbogrid .tg-scrollbar-thumb{position:absolute;top:0;left:0;border-radius:1px;background:#999;overflow:hidden;user-select:none}.tg-turbogrid .tg-scrollbar-thumb:hover{background:#888}.tg-turbogrid .tg-scrollbar-thumb-hold{background:#666}.tg-turbogrid .tg-scrollbar-thumb-hold:hover{background:#666}.tg-turbogrid .tg-scrollbar-round .tg-scrollbar-track{border-radius:10px}.tg-turbogrid .tg-scrollbar-round .tg-scrollbar-thumb{border-radius:10px}.tg-turbogrid .tg-scroll-pane{position:relative;margin:0;padding:0;border:none;outline:none;overflow:hidden}.tg-turbogrid .tg-scroll-view{position:relative;width:100%;height:100%;margin:0;padding:0;border:none;overflow:hidden}.tg-turbogrid .tg-scroll-body{position:absolute}.tg-turbogrid .tg-header{position:relative;width:10000px;border-left:0;overflow:hidden}.tg-turbogrid .tg-header-table{position:relative;color:#5e5e5e;font-weight:bold;font-size:14px;line-height:16px;border-bottom:thin solid #e5e5e5;overflow:hidden}.tg-turbogrid .tg-header-item{position:absolute;bottom:0}.tg-turbogrid .tg-header-group-item{overflow:hidden}.tg-turbogrid .tg-header-group-item::after{position:absolute;left:5px;bottom:0;content:"";display:block;width:calc(100% - 10px);height:1px;border-bottom:thin solid #ccc}.tg-turbogrid .tg-column-header{position:absolute;bottom:0;overflow:hidden}.tg-turbogrid .tg-column-header .tg-column-name{padding:10px 5px;text-overflow:ellipsis;overflow:hidden}.tg-turbogrid .tg-column-header .tg-column-name.tg-header-group-name{margin:0 5px;padding:5px 0}.tg-turbogrid .tg-column-resizing{position:absolute;top:0;right:-5px;z-index:100;width:10px;height:100%;background:#ccc;cursor:ew-resize;opacity:0}.tg-turbogrid .tg-header-column-last .tg-column-resizing{right:0}.tg-turbogrid .tg-column-sortable .tg-column-name{cursor:pointer}.tg-turbogrid .tg-column-sortable .tg-sort-indicator{cursor:pointer}.tg-turbogrid .tg-column-sorted{color:#000}.tg-turbogrid .tg-header-sort-h .tg-column-name{padding:12px 5px 15px}.tg-turbogrid .tg-header-sort-h .tg-column-sort{width:100%;height:15px;margin-top:-15px;padding:0 5px;overflow:hidden}.tg-turbogrid .tg-header-sort-h .tg-sort-indicator{position:relative;display:none;width:100%;height:100%}.tg-turbogrid .tg-header-sort-h .tg-column-sorted .tg-column-sort .tg-sort-indicator{display:block}.tg-turbogrid .tg-header-sort-h .tg-sort-indicator-line{position:absolute;top:1px;width:100%;height:0;border-top:thin solid #1e1e1e;overflow:hidden}.tg-turbogrid .tg-header-sort-h .tg-sort-indicator-icon{position:absolute;top:5px;left:0;right:inherit}.tg-turbogrid .tg-header-sort-h .tg-align-right .tg-sort-indicator-icon{left:inherit;right:0}.tg-turbogrid .tg-header-sort-h .tg-align-center .tg-sort-indicator-icon{left:50%;transform:translateX(-50%)}.tg-turbogrid .tg-header-sort-h .tg-sort-indicator-icon .tg-icon-sort-h{display:block;width:19px;height:6px}.tg-turbogrid .tg-header-sort-h .tg-sort-indicator-icon .tg-icon-item{display:none;fill:#1e1e1e}.tg-turbogrid .tg-header-sort-h .tg-sort-indicator-icon .tg-icon-item-light{fill:#ababab}.tg-turbogrid .tg-column-sort-v{display:flex;flex-direction:row;align-items:center}.tg-turbogrid .tg-column-sort-v .tg-column-name{white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.tg-turbogrid .tg-column-sort-v.tg-align-right{justify-content:right}.tg-turbogrid .tg-column-sort-v .tg-sort-indicator{position:relative;width:16px;height:16px}.tg-turbogrid .tg-column-sort-v .tg-sort-indicator-icon{position:absolute}.tg-turbogrid .tg-column-sort-v .tg-sort-indicator-icon .tg-icon-sort-v{display:block;width:10px;height:16px}.tg-turbogrid .tg-column-sort-v .tg-sort-indicator-icon .tg-icon-item{fill:#ababab}.tg-turbogrid .tg-column-sort-v .tg-sort-indicator-icon .tg-icon-item-light{fill:#ababab}.tg-turbogrid .tg-column-sort-v.tg-column-sorted .tg-sort-indicator-icon .tg-icon-item{fill:#1e1e1e}.tg-turbogrid .tg-column-sort-v.tg-column-sorted .tg-sort-indicator-icon .tg-icon-item-light{fill:#ababab}.tg-turbogrid .tg-sort-desc .tg-sort-indicator .tg-sort-indicator-icon .tg-desc{display:block}.tg-turbogrid .tg-sort-desc .tg-sort-indicator .tg-sort-indicator-icon .tg-asc{display:none}.tg-turbogrid .tg-sort-asc .tg-sort-indicator .tg-sort-indicator-icon .tg-desc{display:none}.tg-turbogrid .tg-sort-asc .tg-sort-indicator .tg-sort-indicator-icon .tg-asc{display:block}.tg-turbogrid .tg-column-line{position:absolute;top:0;left:0;z-index:100;display:none;height:100%;pointer-events:none}.tg-turbogrid .tg-column-line-item{position:absolute;top:0;bottom:0;display:block;width:0;height:100%;border-left:thin solid #ccc}.tg-turbogrid .tg-column-line-item.tg-active{border-left:thin solid #0077cf}.tg-turbogrid .tg-column-dragging{cursor:ew-resize}.tg-turbogrid .tg-column-dragging .tg-column-name{cursor:ew-resize}.tg-turbogrid .tg-column-dragging .tg-column-resizing:not(.tg-resizing-active){display:none}.tg-turbogrid .tg-tree{position:relative;display:flex;flex-direction:row;place-items:center left;width:100%;height:100%;overflow:hidden}.tg-turbogrid .tg-tree-icon{position:relative;width:15px;height:100%;min-height:9px;text-align:left;cursor:pointer;overflow:hidden}.tg-turbogrid .tg-tree-icon .tg-icon-tree{position:absolute;top:50%;left:0;display:block;width:9px;height:9px;transform:translate(0, -50%);overflow:hidden}.tg-turbogrid .tg-tree-icon .tg-tree-item{display:none}.tg-turbogrid .tg-tree-icon-collapsed .tg-tree-collapsed{display:block}.tg-turbogrid .tg-tree-icon-empty .tg-tree-collapsed{opacity:.5}.tg-turbogrid .tg-tree-icon-expanded .tg-tree-expanded{display:block}.tg-turbogrid .tg-tree-name{flex:1;text-overflow:ellipsis;overflow:hidden}.tg-turbogrid .tg-tree-header .tg-tree .tg-tree-icon{display:none}.tg-turbogrid .tg-tree-icon-all{position:relative;height:17px}.tg-turbogrid .tg-tree-header-indent .tg-tree{padding-left:5px}.tg-turbogrid .tg-tree-header-indent .tg-tree .tg-tree-icon{display:block}.tg-turbogrid .tg-tree-header-indent.tg-column-sort-h .tg-column-sort{width:calc(100% - 20px);margin-left:20px}.tg-turbogrid .tg-pane{position:absolute;width:100%;outline:0;overflow:hidden}.tg-turbogrid .tg-header-frame{position:relative;display:block;outline:0;overflow:hidden}.tg-turbogrid .tg-header-frame .tg-pane{height:100%}.tg-turbogrid .tg-body-frame{position:relative;width:100%;outline:0}.tg-turbogrid .tg-body-message{position:absolute;display:none;width:100%;height:100%;padding:10px;overflow:hidden}.tg-turbogrid .tg-body-message img,.tg-turbogrid .tg-body-message div{position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.tg-turbogrid .tg-body{position:absolute;outline:0}.tg-turbogrid .tg-cell-hover-icon{display:none}.tg-touch-device.tg-turbogrid .tg-cell-hover-icon{display:inherit}.tg-turbogrid .tg-cell-row-number{font-weight:normal}.tg-turbogrid .tg-cell-row-drag .tg-row-drag-icon{position:absolute;top:50%;left:50%;width:24px;height:24px;cursor:move;opacity:.8;transform:translate(-50%, -50%)}.tg-turbogrid .tg-cell-row-drag .tg-row-drag-icon:hover{opacity:1}.tg-turbogrid .tg-cell{position:absolute;z-index:1;height:100%;margin:0;padding:0 5px;color:#1e1e1e;white-space:nowrap;text-overflow:ellipsis;vertical-align:middle;overflow:hidden}.tg-turbogrid .tg-cell:focus{outline:none}.tg-turbogrid .tg-cell.tg-flashing{border:1px solid red !important}.tg-turbogrid .tg-cell.tg-selected{background-color:beige}.tg-turbogrid .tg-cell.tg-align-left.tg-cell-negative{padding-left:1px}.tg-turbogrid .tg-cell.tg-align-right.tg-cell-negative{padding-right:1px}.tg-turbogrid .tg-cell.tg-cell-observer{display:flex;flex-direction:column;justify-content:center;padding:5px;line-height:normal;white-space:normal;text-overflow:ellipsis;overflow:hidden}.tg-turbogrid .tg-cell.tg-cell-observer .tg-observer{position:relative;margin:0;padding:0;border:none}.tg-turbogrid .tg-row{position:absolute;width:100%;border:0;border-bottom:thin solid #e5e5e5}.tg-turbogrid .tg-row.tg-group-line{border-bottom:thin solid #999}.tg-turbogrid .tg-row.tg-none-line{border-bottom:none}.tg-turbogrid .tg-row.tg-top-line{border-top:thin solid #e5e5e5}.tg-turbogrid .tg-row.tg-group{font-weight:bold;overflow:hidden}.tg-turbogrid .tg-row.tg-group .tg-cell.tg-align-left.tg-cell-negative{padding-left:0}.tg-turbogrid .tg-row.tg-group .tg-cell.tg-align-right.tg-cell-negative{padding-right:0}.tg-turbogrid .tg-row.tg-hover .tg-cell .tg-cell-hover-icon{display:inherit}.tg-turbogrid .tg-row.tg-dragging{opacity:.3}.tg-turbogrid .tg-row.tg-clone{z-index:1000;border:1px dashed #ccc;border-right:none;border-left:none;background:#fff;cursor:move;opacity:.5}.tg-turbogrid .tg-row.tg-clone *{cursor:move}.tg-turbogrid .tg-row-placeholder{position:absolute;z-index:9999;width:100%;border-top:2px solid #00a8e1;pointer-events:none}.tg-turbogrid .tg-row::before,.tg-turbogrid .tg-row::after{position:absolute;top:0;left:0;content:"";z-index:100;display:none;width:100%;height:100%;pointer-events:none}.tg-turbogrid .tg-hover.tg-row::before{display:block;background:rgba(0,0,0,.08)}.tg-turbogrid .tg-selected.tg-row::after{display:block;background:rgba(0,0,0,.13)}.tg-lightblue .tg-header-item{border-top:thin solid #e8eaf0;border-right:thin solid #e8eaf0}.tg-lightblue .tg-column-name{padding:5px;color:#304265}.tg-lightblue .tg-header-group-item::after{display:none}.tg-lightblue .tg-checkbox .tg-icon-item{fill:#d4d7e0}.tg-lightblue .tg-checkbox:hover .tg-icon-item{fill:#107fff}.tg-lightblue .tg-checkbox.tg-selected .tg-select-checkbox{fill:#107fff}.tg-lightblue .tg-checkbox.tg-mixed .tg-select-mixed{fill:#107fff}.tg-lightblue .tg-cell{color:#304265;border-right:thin solid #e8eaf0}.tg-lightblue .tg-row{border-bottom:thin solid #e8eaf0}.tg-lightblue .tg-row.tg-group-line{border-bottom:thin solid #c9ccd8}.tg-lightblue .tg-row.tg-selected{background:rgba(58,116,213,.05)}.tg-lightblue .tg-row.tg-hover{background:rgba(58,116,213,.05)}.tg-lightblue .tg-row.tg-even{background:#fbfcfe}.tg-lightblue .tg-row.tg-odd{background:#fff}.tg-lightblue .tg-hover.tg-row::before{background:rgba(58,116,213,.05)}.tg-lightblue .tg-selected.tg-row::after{background:rgba(58,116,213,.1)}.tg-lightblue .tg-header-frame{border-bottom:thin solid #e8eaf0}.tg-lightblue .tg-row-not-found .tg-frozen-line-v{border-right:none}.tg-lightblue .tg-scrollbar-track{background:#fff}.tg-lightblue .tg-scrollbar-thumb{background:rgba(48,66,101,.35)}.tg-lightblue .tg-scrollbar-thumb:hover{background-color:#a8a8a8}.tg-lightblue .tg-scrollbar-thumb:active{background-color:#787878}.tg-dark{background:#1e1e1e}.tg-dark .tg-checkbox .tg-icon-item{fill:#ababab}.tg-dark .tg-header-table{color:#ccc;border-bottom:thin solid #333}.tg-dark .tg-header-group-item::after{border-bottom:1px solid #999}.tg-dark .tg-column-sorted{color:#fff}.tg-dark .tg-column-sorted .tg-tree-icon-all .tg-icon-item{fill:#fff}.tg-dark .tg-header-sort-h .tg-sort-indicator-line{border-top:thin solid #eee}.tg-dark .tg-header-sort-h .tg-sort-indicator-icon .tg-icon-item{fill:#eee}.tg-dark .tg-header-sort-h .tg-sort-indicator-icon .tg-icon-item-light{fill:#666}.tg-dark .tg-column-sort-v .tg-sort-indicator-icon .tg-icon-item{fill:#666}.tg-dark .tg-column-sort-v .tg-sort-indicator-icon .tg-icon-item-light{fill:#666}.tg-dark .tg-column-sort-v.tg-column-sorted .tg-sort-indicator-icon .tg-icon-item{fill:#fff}.tg-dark .tg-column-sort-v.tg-column-sorted .tg-sort-indicator-icon .tg-icon-item-light{fill:#666}.tg-dark .tg-tree-icon .tg-icon-item{fill:#fff}.tg-dark .tg-tree-icon-all .tg-icon-item{fill:#999}.tg-dark .tg-header-item .tg-tree-icon .tg-icon-item{fill:#999}.tg-dark .tg-header-item .tg-column-sorted .tg-tree-icon .tg-icon-item{fill:#fff}.tg-dark .tg-row{border-bottom:thin solid #333}.tg-dark .tg-row.tg-group-line{border-bottom:thin solid #666}.tg-dark .tg-row.tg-clone{border:1px dashed #1e1e1e;opacity:.1}.tg-dark .tg-cell{color:#eee}.tg-dark .tg-body-message{color:#eee}.tg-dark .tg-hover.tg-row::before{background:rgba(255,255,255,.1)}.tg-dark .tg-selected.tg-row::after{background:rgba(255,255,255,.2)}.tg-dark .tg-mask{background-color:#fff}.tg-dark .tg-scrollbar-track{background:#333}.tg-dark .tg-scrollbar-thumb{background:#bbb}.tg-dark .tg-scrollbar-thumb:hover{background:#ddd}.tg-dark .tg-scrollbar-thumb-hold{background:#eee}.tg-dark .tg-scrollbar-thumb-hold:hover{background:#eee}.tg-pointer-events-none{pointer-events:none}',""]);const l=r},505:t=>{t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var i="",o=void 0!==e[5];return e[4]&&(i+="@supports (".concat(e[4],") {")),e[2]&&(i+="@media ".concat(e[2]," {")),o&&(i+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),i+=t(e),o&&(i+="}"),e[2]&&(i+="}"),e[4]&&(i+="}"),i})).join("")},e.i=function(t,i,o,n,s){"string"==typeof t&&(t=[[null,t,void 0]]);var r={};if(o)for(var l=0;l<this.length;l++){var h=this[l][0];null!=h&&(r[h]=!0)}for(var a=0;a<t.length;a++){var c=[].concat(t[a]);o&&r[c[0]]||(void 0!==s&&(void 0===c[5]||(c[1]="@layer".concat(c[5].length>0?" ".concat(c[5]):""," {").concat(c[1],"}")),c[5]=s),i&&(c[2]?(c[1]="@media ".concat(c[2]," {").concat(c[1],"}"),c[2]=i):c[2]=i),n&&(c[4]?(c[1]="@supports (".concat(c[4],") {").concat(c[1],"}"),c[4]=n):c[4]="".concat(n)),e.push(c))}},e}},256:t=>{t.exports=function(t){return t[1]}}},e={};function i(o){var n=e[o];if(void 0!==n)return n.exports;var s=e[o]={id:o,exports:{}};return t[o](s,s.exports,i),s.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var o in e)i.o(e,o)&&!i.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var o={};(()=>{i.d(o,{$:()=>w,MP:()=>e,_d:()=>k,xA:()=>Xt,In:()=>X,T8:()=>B,Gr:()=>kt,ht:()=>Kt,J0:()=>d,xv:()=>Yt,Ay:()=>qt});const t="turbogrid",e={ID:t,NS:`tg-${t}`,VERSION:"3.2.0",TIMESTAMP:"2024-06-20T10:33:49.165Z",UP:"up",DOWN:"down",LEFT:"left",RIGHT:"right",TREE_INDENT:15},n=function(t){if(!t||"object"!=typeof t)return!1;const e=Object.prototype.toString.call(t);return!!["[object Object]","[object Array]"].includes(e)&&(!t.constructor||!![Object,Array].includes(t.constructor))},s=function(t,e){let i;return t.forEach((t=>{n(t)&&(i||(i=t instanceof Array?[]:{}),t instanceof Array?function(t,e,i){const o=e.length;for(let s=0;s<o;s++){const o=e[s];i&&n(o)?t[s]=r(t[s],o):t[s]=o}t instanceof Array&&(t.length=o)}(i,t,e):function(t,e,i){Object.keys(e).forEach((function(o){const s=e[o];i&&n(s)&&Object.prototype.hasOwnProperty.call(t,o)?t[o]=r(t[o],s):t[o]=s}))}(i,t,e))})),i||{}},r=function(){const t=Array.from(arguments),e=t.length;if(!e)return{};let i=!0;return!1===t[e-1]&&(i=!1),s(t,i)},l=r;class h{start(t){this.callback=t,this.started||(this.started=!0,this.create())}create(){"function"==typeof window.queueMicrotask?window.queueMicrotask((()=>{this.execute()})):Promise.resolve().then((()=>{this.execute()}))}execute(){if(!this.started)return;this.started=!1;const t=this.callback;this.callback=null,"function"==typeof t&&t.call(this)}cancel(){this.started=!1,this.callback=null}}const a=new WeakMap,c={isObject:n,merge:l,hasOwn:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},uid:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:8;const e="0123456789abcdefghijklmnopqrstuvwxyz";let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";for(;t--;)i+=e[36*Math.random()|0];return i},isNum:function(t){if("number"!=typeof t||isNaN(t))return!1;return(e=t)!==Number.MAX_VALUE&&e!==Number.MIN_VALUE&&e!==Number.NEGATIVE_INFINITY&&e!==Number.POSITIVE_INFINITY;var e},toNum:function(t,e){return"number"!=typeof t&&(t=parseFloat(t)),isNaN(t)&&(t=0),e&&!Number.isInteger(t)&&(t=Math.round(t)),t},convertNum:function(t){if("string"==typeof t){if(/^[-+]?\d+(\.\d+)?$/gi.test(t))return parseFloat(t)}return t},clamp:function(t,e,i){return Math.max(Math.min(t,i),e)},per:function(t){return t=c.toNum(t),t=c.clamp(t,0,1)},replace:function(t,e){return t=`${t}`,e?t=t.replace(/\{([^}]+)\}/g,(function(t,i){return c.hasOwn(e,i)?e[i]:t})):t},isArray:function(t){return!!(t&&t instanceof Array)},toList:function(t){return t instanceof Array?t:void 0===t?[]:"string"==typeof t?[t]:t&&c.hasOwn(t,"length")?Array.from(t):[t]},isList:function(t){return!!(c.isArray(t)&&t.length>0)},inList:function(t,e){if(!c.isList(e))return!1;for(let i=0,o=e.length;i<o;i++)if(e[i]===t)return!0;return!1},isDate:function(t){return!!(t&&t instanceof Date)&&!isNaN(t.getTime())},isPromise:function(t){return Boolean(t)&&("object"==typeof t||"function"==typeof t)&&"function"==typeof t.then},getValue:function(t,e,i){if(!e)return i;let o=t;const n=e.split("."),s=n.pop();for(;o&&n.length;){o=o[n.shift()]}if(o&&c.hasOwn(o,s)){const t=o[s];if(void 0!==t)return t}return i},forEachTree:function(t,e){const i=(t,o)=>{if(!c.isList(t))return;let n=0;const s=t.length;for(;n<s;){const s=t[n];if(!1===e(s,n,o))return!1;if(!1===i(s.subs,s))return!1;n++}};i(t)},removePreProps:function(t,e){t&&e&&Object.keys(t).filter((t=>t.startsWith(e))).forEach((e=>{t[e]=null}))},hasShiftKey:function(t){let e=!1;return t&&(e=t.shiftKey),e},isTouchDevice:function(){return"ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0},contains:function(t,e){if(!t||!e)return!1;if(t===e)return!0;if("function"==typeof t.contains)return t.contains(e);let i=e.parentNode;for(;i;){if(i===t)return!0;i=i.parentNode}return!1},isNarrowCharacter:function(t){const e=t.codePointAt(0);return e>=32&&e<=126||162===e||163===e||165===e||166===e||172===e||175===e||8361===e||e>=10214&&e<=10221||10629===e||10630===e||e>=65377&&e<=65470||e>=65474&&e<=65479||e>=65482&&e<=65487||e>=65490&&e<=65495||e>=65498&&e<=65500||e>=65512&&e<=65518},getCharLen:function(t){let e=0;if(!t)return e;for(const i of String(t))e+=c.isNarrowCharacter(i)?1:2;return e},pascalToKebabCase:function(t){return`${t}`.trim().replace(/([a-z])([A-Z])/g,"$1-$2").replace(/\W/g,(t=>/[À-ž]/.test(t)?t:"-")).replace(/^-+|-+$/g,"").replace(/-{2,}/g,"-").toLowerCase()},classMap:function(t){if("string"==typeof t)return t.trim();if(Array.isArray(t)){let e=t.filter((t=>t));return e=e.map((t=>t&&"object"==typeof t?c.classMap(t):String(t).trim())),e=e.filter((t=>t)),e=Array.from(new Set(e)),e.join(" ")}if(t&&"object"==typeof t){const e=[];return Object.keys(t).forEach((i=>{t[i]&&e.push(i)})),e.join(" ")}return""},styleMap:function(t){if("string"==typeof t)return t.trim();if(Array.isArray(t)){let e=t.filter((t=>t));return e=e.map((t=>{const e=String(t).trim();return e?-1===e.indexOf(":")?"":e.endsWith(";")?e:`${e};`:""})),e=e.filter((t=>t)),e=Array.from(new Set(e)),e.join(" ")}if(t&&"object"==typeof t){const e=[];return Object.keys(t).forEach((i=>{const o=t[i];if(o||0===o){const t=String(o).trim();t&&e.push(`${c.pascalToKebabCase(i)}: ${t};`)}})),e.join(" ")}return""},getInstance:function(t){if(t){const e=document.getElementById(t);if(e)return a.get(e)}},setInstance:function(t,e){t&&a.set(t,e)},bindEvents:function(t,e){t&&(c.unbindEvents(t),Object.keys(t).forEach((i=>{const o=t[i];o.target=o.target||e,o.target.addEventListener(i,o.handler,o.options)})))},unbindEvents:function(t){t&&Object.keys(t).forEach((e=>{const i=t[e];i.target&&i.target.removeEventListener(e,i.handler,i.options)}))},preventDefault:function(t){t&&"function"==typeof t.preventDefault&&t.cancelable&&t.preventDefault()},debounce:function(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;const o=function(){clearTimeout(e),e=setTimeout((()=>{t.apply(this,arguments)}),i)};return o.cancel=()=>{clearTimeout(e)},o},throttle:function(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,o=0;const n=function(){const n=Date.now();if(n>o+i)return clearTimeout(e),o=n,void t.apply(this,arguments);clearTimeout(e),e=setTimeout((()=>{o=n,t.apply(this,arguments)}),i)};return n.cancel=()=>{clearTimeout(e),o=0},n},microtask:function(t){const e=new h,i=function(){e.start((()=>{t.apply(this,arguments)}))};return i.cancel=()=>{e.cancel()},i},nextTick:function(t){"function"==typeof window.queueMicrotask?window.queueMicrotask((()=>{t()})):Promise.resolve().then((()=>{t()}))},cancelAsync:function(t){t&&(Object.keys(t).filter((e=>e.startsWith("async")&&"function"==typeof t[e])).forEach((e=>{const i=t[e];"function"==typeof i.cancel&&(i.cancel(),t[e]=null)})),Object.keys(t).filter((t=>t.startsWith("timeout"))).forEach((e=>{clearTimeout(t[e])})))}},d=c,u={animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},g=function(t){return null!==t&&1===t.nodeType},f=function(t){let e=t.ownerDocument.defaultView;return e&&e.opener||(e=window),e.getComputedStyle(t)},p={},m=function(t,e){return null!=(i=t)&&i===i.window?t[`inner${e}`]:(function(t){return null!==t&&9===t.nodeType}(t)&&(t=t.body),t[`client${e}`]);var i},b=function(t){return this.list=[],t?this.create(t):this};function w(t){return new b(t)}b.prototype={constructor:b,Query:"Query",list:[],create:function(t){return t instanceof b?t:"string"==typeof t?this.createFromString(t):((t.nodeType||t===window)&&(this.list=[t]),this)},createFromString:function(t){if("<"===(t=t.trim())[0]&&">"===t[t.length-1]&&t.length>=3)this.parseHTML(t);else{const e=document.querySelectorAll(t);for(let t=0,i=e.length;t<i;t++)this.list[t]=e[t]}return this},parseHTML:function(t){const e=document.createElement("div");e.innerHTML=t;let i=e.firstChild;for(;i;)g(i)&&this.list.push(i),i=i.nextSibling;return this},get:function(t){return this.list[t]},each:function(t){if("function"!=typeof t)return this;const e=this.list;for(let i=0,o=e.length;i<o;i++){const o=e[i];if(!1===t.call(this,o,i))break}return this},add:function(t){if(!t)return this;const e=this.list;return t instanceof b?(t.each((function(t){e.push(t)})),this):(t.nodeType&&e.push(t),this)},empty:function(){return this.each((function(t){t.innerHTML=""})),this},remove:function(){return this.each((function(t,e){t&&t.parentNode&&t.parentNode.removeChild(t)})),this.list=[],this},find:function(t){const e=new b;return t&&"string"==typeof t?(this.each((function(i){if(i&&i.querySelectorAll){const o=i.querySelectorAll(t);for(let t=0,i=o.length;t<i;t++)e.add(o[t])}})),e):e},prepend:function(t){if(!t)return this;const e=new b(t);return this.each((function(t){e.each((function(e){t.insertBefore(e,t.firstChild)}))})),this},append:function(t){if(!t)return this;const e=new b(t);return this.each((function(t){e.each((function(e){t.appendChild(e)}))})),this},appendTo:function(t){if(!t)return this;const e=new b(t);return this.each((function(t){e.append(t)})),this},html:function(t){if(0===arguments.length){const t=this.get(0);return t?t.innerHTML:""}return this.each((function(e){e.innerHTML=t})),this},width:function(t){if(0===arguments.length){const t=this.get(0);return t?m(t,"Width"):0}return this.css("width",t),this},height:function(t){if(0===arguments.length){const t=this.get(0);return t?m(t,"Height"):0}return this.css("height",t),this},css:function(t,e){if(!t)return this;if(1===arguments.length){if("object"!=typeof t){const e=this.get(0);if(e){return f(e)[(i=t,i.replace(/-([a-z])/g,(function(t,e){return e.toUpperCase()})))]}return}Object.keys(t).forEach((e=>{this.css(e,t[e])}))}var i;return this.each((function(i){let o=e;"number"!=typeof o||u[t]||(o+="px"),i.style[t]=o})),this},attr:function(t,e){if(!t)return this;if(1===arguments.length){if("object"==typeof t)return Object.keys(t).forEach((e=>{this.attr(e,t[e])})),this;const e=this.get(0);return e?e.getAttribute(t):void 0}return this.each((function(i){i.setAttribute(t,e)})),this},removeAttr:function(t){return t?(this.each((function(e){e.hasAttribute(t)&&e.removeAttribute(t)})),this):this},removeClass:function(t){if(!arguments.length)return this.each((function(t){t.className=""})),this;if(!t||"string"!=typeof t)return this;const e=t.split(" ");return this.each((function(t){e.forEach((function(e){e&&t.classList.remove(e)}))})),this},addClass:function(t){if(!t||"string"!=typeof t)return this;const e=t.split(" ");return this.each((function(t){e.forEach((function(e){e&&t.classList.add(e)}))})),this},hasClass:function(t){if(!t||"string"!=typeof t)return!1;let e=!1;return this.each((function(i){if(i.classList.contains(t))return e=!0,!1})),e},show:function(){return this.each((function(t){if(!g(t))return;const e=function(t){if(!p[t]){const e=document.createElement(t);document.body.appendChild(e);const i=f(e).display;e.parentNode.removeChild(e),p[t]=i}return p[t]}(t.nodeName);t.style.display=e})),this},hide:function(){return this.each((function(t){if(!g(t))return;"none"!==t.style.display&&(t.style.display="none")})),this},click:function(){const t=this.get(0);return t&&"function"==typeof t.click&&t.click(),this},offset:function(){const t={left:0,top:0},e=this.get(0);if(e){const i=e.getBoundingClientRect();t.left=i.left+window.scrollX,t.top=i.top+window.scrollY}return t},clone:function(){const t=new b;return this.each((function(e){if(e&&e.cloneNode){const i=e.cloneNode(!0);t.add(i)}})),t},children:function(){const t=new b;return this.each((function(e){let i=e.firstChild;for(;i;)t.add(i),i=i.nextSibling})),t},parent:function(){const t=this.get(0);return t?new b(t.parentNode):new b},is:function(t){if(!t)return!1;const e=t.split(",");let i=!0;return this.each((function(t){if(!t.nodeName)return i=!1,!1;const o=t.nodeName.toLowerCase();return d.inList(o,e)?void 0:(i=!1,!1)})),i}},Object.defineProperty(b.prototype,"length",{get:function(){return this.list.length}});const v={createCache:function(){this.headerCache=new Map,this.rowsCache=new Map,this.dataCache=new WeakMap,this.cellResizeObserver=this.createResizeObserver((t=>{this.cellResizeHandler(t)}))},setHeaderCache:function(t,e){this.headerCache.set(t,e)},getHeaderCache:function(t){return this.headerCache.get(t)},clearHeaderCache:function(){this.headerCache.clear()},setRowCache:function(t,e){this.rowsCache.set(t,{rowNodes:e,cellNodes:new Map,observerNodes:new Map})},getRowCache:function(t){return this.rowsCache.get(t)},deleteRowCache:function(t){const e=this.getRowCache(t);if(!e)return;this.rowsCache.delete(t);const i=e.observerNodes;i&&i.forEach((t=>{t&&this.cellResizeObserver.unobserve(t)}));const o=e.rowNodes;o&&o.each((t=>{this.removeNode(t)}))},deleteCellCache:function(t,e,i){if(i){const e=i.get(t);e&&this.cellResizeObserver.unobserve(e),i.delete(t)}e&&(this.removeNode(e.get(t)),e.delete(t))},getRowNodesByIndex:function(t){const e=this.getRowCache(t);if(e)return e.rowNodes},getCellNodeByIndex:function(t,e){const i=this.getRowCache(t);if(i)return i.cellNodes.get(e)},forEachRowsCache:function(t){this.rowsCache.forEach(((e,i)=>{t.call(this,i,e.rowNodes,e.cellNodes,e.observerNodes)}))},updateRowCacheTopOffset:function(){const t=this.frozenInfo.row;this.forEachRowsCache(((e,i)=>{if(!(e<=t)&&i){const t=this.getViewRowItem(e),o=this.getViewRowTop(t);i.css("top",o)}}))},updateRowCacheTopAndHeight:function(){this.forEachRowsCache(((t,e)=>{if(e){const i=this.getViewRowItem(t),o=this.getViewRowTop(i),n=this.getRowHeight(i);e.css({top:o,height:n,"line-height":n})}}))},setNodeDataCache:function(t,e){if(t)return this.dataCache.set(t,e)},getNodeDataCache:function(t){if(t)return this.dataCache.get(t)},removeCache:function(){this.headerCache=null,this.rowsCache=null,this.dataCache=null,this.cellResizeObserver&&(this.cellResizeObserver.disconnect(),this.cellResizeObserver=null)}},H=["onUpdated","onFirstUpdated","onHeaderUpdated","onSort","onColumnAdded","onColumnRemoved","onColumnWidthChanged","onRowAdded","onRowRemoved","onRowExpanded","onRowCollapsed","onRowSubsRequest","onRowDragged","onRowDropped","onRowMoved","onRowMouseEnter","onRowMouseLeave","onSelectChanged","onCellUpdated","onCellMouseEnter","onCellMouseLeave","onClick","onDblClick","onContextMenu","onMouseOver","onMouseOut","onTouchStart","onTouchMove","onTouchEnd","onScroll","onScrollStateChanged","onMouseWheel","onResize","onLayout","onKeyDown","onDestroy"],C={};H.forEach((t=>{C[t]=t}));const y=C,R={renderCells:function(t,e){t.forEach((t=>{this.drawRowCells(t,e)}))},getCellValue:function(t,e){return t[e.id]},renderCell:function(t){const{rowItem:e,columnItem:i,cellNode:o,observerNode:n}=t,s=this.getCellValue(e,i);let r=s;this.nullFormatter&&(r=this.nullFormatter.call(this,r,e,i,o,n));const l=e.tg_formatter||i.tg_formatter;"function"==typeof l&&(r=l.call(this,r,e,i,o,n));const h=n||o;this.renderNodeContent(h,r);const{highlightKey:a}=this.options.highlightKeywords;e[a+i.id]&&this.renderSettings.highlightCells.push(o),this.trigger(y.onCellUpdated,{value:s,rowItem:e,columnItem:i,node:o})},getPreRenderColumnList:function(t,e){const i=[];if(!e.length)return i;for(let o=0,n=e.length;o<n;o++){const n=e[o];this.getCellNodeByIndex(t,n)||i.push(n)}return i},drawRowCells:function(t,e){const i=this.getPreRenderColumnList(t,e);i.length&&i.forEach((e=>{this.createCellNode(t,e)}))},getCellClass:function(t,e,i){const o=e.tg_view_index,n=["tg-cell"];return i&&n.push("tg-cell-observer"),n.push(`tg-c-${o}`),e.align&&n.push(`tg-align-${e.align}`),0===e.tg_list_index&&n.push("tg-list-first"),e.tg_list_last&&n.push("tg-list-last"),n.push(d.classMap(e.classMap)),n.push(d.classMap(t[`${e.id}ClassMap`])),d.classMap(n)},cellResizeObserverHandler:function(t,e){const i=this.options.cellResizeObserver;if("function"==typeof i)return i.apply(this,[t,e])},cellResizeHandler:function(t){const e=new Map;t.forEach((t=>{const{target:i}=t,o=this.getNodeDataCache(i.parentNode);if(!o)return;const{row:n,rowItem:s}=o;e.set(n,s)}));let i=!1;e.forEach(((t,e)=>{const o=this.getRowCache(e);if(!o)return;const n=o.observerNodes;if(!n)return;const{rowHeight:s,rowMinHeight:r}=this.options;let l=Math.max(r||s,1);n.forEach((t=>{if(t){const e=t.clientHeight+11;e>l&&(l=e)}}));this.getRowHeight(t)!==l&&(t.tg_height=l,i=!0)})),i&&this.render("rows_cache")},createCellNode:function(t,e){const i=this.getRowCache(t);if(!i)return;const o=this.getViewRowItem(t),n=this.getViewColumnItem(e);if(!o||!n)return;const s=this.cellResizeObserverHandler(o,n),r=document.createElement("div");r.setAttribute("column",e);const l=this.getCellClass(o,n,s);r.className=l;const h=d.styleMap(n.styleMap)+d.styleMap(o[`${n.id}StyleMap`]);h&&(r.style.cssText=h);const a=i.rowNodes,c=n.tg_frozen,u=this.getCellRowNode(a,c);let g;this.appendNode(u,r),s&&(g=document.createElement("div"),g.className="tg-observer",r.appendChild(g),this.cellResizeObserver.observe(g),i.observerNodes.set(e,g));const f={row:t,rowItem:o,rowNode:u,column:e,columnItem:n,cellNode:r,observerNode:g};this.setNodeDataCache(r,f),i.cellNodes.set(e,r),this.renderCell(f)},getCellRowNode:function(t,e){const i=t.get(0);if(this.frozenInfo.columns){const o=t.get(1);return this.frozenInfo.right?e?o:i:e?i:o}return i}},S={addColumn:function(t,e,i){let o=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];const n=this.getToBeAddedItemList(t);if(!n.length)return!1;let s;if(null!=e&&(s=this.getColumnItem(e),!s))return!1;const r=this.getToBeAddedParentSubs(s,this.columns),l=[this.getToBeAddedPositionIndex(i,r),0].concat(n);r.splice.apply(r,l),this.onNextUpdated((function(){this.trigger(y.onColumnAdded,n)}));const h={type:"columns"};return o&&(h.scrollColumn=n[n.length-1]),this.render(h),!0},deleteColumn:function(t){const e=this.toColumnItemList(t,(t=>!t.private));return!!e.length&&(this.removeColumnsHandler(e),this.onNextUpdated((function(){this.trigger(y.onColumnRemoved,e)})),this.render("columns"),!0)},removeColumnsHandler:function(t){const e=[].concat(t);e.sort((function(t,e){return e.tg_index-t.tg_index})),e.forEach((t=>{let e;if(t===this.sortColumn&&this.removeSortColumn(),t.tg_parent)e=t.tg_parent.subs,e.splice(t.tg_sub_index,1);else{e=this.columns;const i=e.findIndex((e=>e===t));-1!==i&&e.splice(i,1)}!e.length&&t.tg_parent&&(t.tg_parent.subs=null)}))}},T={setColumnWidth:function(t,e){return this.updateColumnWidth(t,e)?(this.resize(),this):this},updateColumnWidth:function(t,e){const i=this.getColumnItem(t);return!!i&&(!!d.isNum(e)&&(e=Math.round(e),e=Math.max(0,e),i.tg_width!==e&&(i.width=e,i.minWidth=Math.min(i.minWidth,e),i.maxWidth=Math.max(i.maxWidth,e),this.updateViewColumnWidth(i),!0)))},showColumn:function(t){return this.updateColumnsInvisible(this.toColumnItemList(t),!1)},hideColumn:function(t){return this.updateColumnsInvisible(this.toColumnItemList(t),!0)},updateColumnsInvisible:function(t,e){if(!t.length)return!1;const i=[];return t.forEach((t=>{t.invisible!==e&&(t.invisible=e,t.tg_invisible=e,i.push(t))})),!!i.length&&(this.render("columns"),!0)}},E={showColumnLine:function(t){t&&(this.$columnLineContainer.show(),this.renderColumnLine(t))},hideColumnLine:function(){this.previousColumnLineActive||this.$columnLineContainer.hide()},setColumnLineActive:function(t){this.setTextSelectable(!t),this.rowHoverable=!t,t!==this.previousColumnLineActive&&(this.previousColumnLineActive=t,t?this.$columnLineItem.addClass("tg-active"):this.$columnLineItem.removeClass("tg-active"))},getColumnLineLeft:function(t){let e=t.tg_left;return t.tg_frozen||(e-=this.scrollLeft),this.frozenInfo.right&&(t.tg_frozen?e=t.tg_left+this.paneWidthL:e-=this.columnsWidthR),e},renderColumnLine:function(t){const e=this.getHeaderItemNode(t).offsetTop,i=t.tg_width,o=this.getColumnLineLeft(t);this.$columnLineItemL.css({top:e,left:o}),this.$columnLineItemR.css({top:e,left:o+i-1}),this.frozenInfo.right||(this.frozenInfo.columns&&!t.tg_frozen&&o<this.paneWidthL?this.$columnLineItemL.hide():this.$columnLineItemL.show())},setColumnWidthDragStatus:function(t,e){e?this.$headerFrame.addClass("tg-column-dragging"):this.$headerFrame.removeClass("tg-column-dragging");const i=this.getHeaderItemNode(t);if(!i)return;const o=w(i).find(".tg-column-resizing");e?o.addClass("tg-resizing-active"):o.removeClass("tg-resizing-active")},columnWidthDragStartHandler:function(t,e){const i=e.columnItem;this.setColumnLineActive(!0),this.setColumnWidthDragStatus(i,!0);const o=this.getColumnHeaderNode(i);e.tg_width=o.clientWidth},columnWidthDragMoveHandler:function(t,e){const i=e.columnItem;let o=e.tg_width+e.offsetX;o=d.clamp(o,i.minWidth,i.maxWidth),i.tg_width!==o&&(i.width=o,this.updateViewColumnWidth(i),this.renderColumnLine(i))},columnWidthDragEndHandler:function(t,e){if(!e.changed)return;const i=e.columnItem;this.setColumnWidthDragStatus(i,!1),this.setColumnLineActive(!1);const o=e.e&&e.e.target;o&&"tg-column-resizing"!==o.className&&this.hideColumnLine(),this.onNextUpdated((()=>{this.renderColumnLine(e.columnItem)})),this.resize()},columnWidthTouchStartHandler:function(t,e){d.preventDefault(e.e);const i=e.columnItem;this.showColumnLine(i),this.setColumnLineActive(!0),e.index=i.tg_index;const o=this.getColumnHeaderNode(i);e.width=o.clientWidth},columnWidthTouchMoveHandler:function(t,e){d.preventDefault(e.e);const i=e.columnItem;let o=e.width+e.offsetX;o=d.clamp(o,i.minWidth,i.maxWidth),i.tg_width!==o&&(i.width=o,this.updateViewColumnWidth(i),this.renderColumnLine(i))},columnWidthTouchEndHandler:function(t,e){d.preventDefault(e.e),this.setColumnLineActive(!1),this.hideColumnLine(),this.resize()}},I={getColumnItem:function(t){return d.isNum(t)?(t<0&&(t=this.columnsInfo.length+t),this.columnsInfo.indexCache[t]):t?d.isNum(t.tg_index)?t:this.getColumnItemById(t.id||t):void 0},getColumnItemById:function(t){return this.getColumnItemBy("id",t)},getColumnItemBy:function(t,e){if(void 0!==e)return this.columnsInfo.indexCache.find((i=>i[t]===e))},getColumnsLength:function(t){return t?this.columnsInfo.length:this.viewColumns.length},getViewColumnItem:function(t){return this.viewAllColumns[t]},isColumnSortable:function(t){return!!t&&(!t.tg_group&&(!(!t.name||!t.id)&&this.isSortable(t)))},isColumnResizable:function(t){return!!t&&(!t.tg_group&&(!d.hasOwn(t,"resizable")||Boolean(t.resizable)))},updateViewColumnWidth:function(t){return t.tg_width=t.width,this.updateColumnHeaderSize(t),this.updateTotalColumnsWidth(),this.updateHeaderLayerHeight(),this.cssRulesInvalid=!0,this.resizeBodyHandler(),this.trigger(y.onColumnWidthChanged,t),!0},updateTotalColumnsWidth:function(){this.blankColumn.tg_width=0;const t=this.viewColumns;let e=0,i=0;const o=this.frozenInfo.columns,n=t.length;let s=0;for(let r=0;r<n;r++){const n=t[r];n.tg_left=s;const l=n.tg_width;l>0&&(s+=l,o&&r>=o?i+=l:e+=l)}if(this.frozenInfo.right){const t=e;e=i,i=t}this.columnsWidthL=e,this.columnsWidthR=i,this.columnsWidth=e+i},updateColumnHeaderSize:function(t){this.updateColumnHeaderWidth(t),this.updateColumnHeaderHeight(t,!0),this.updateColumnGroupWidth(t)},updateColumnHeaderWidth:function(t){const e=this.getColumnHeaderNode(t);if(!e)return;const i=t.tg_width;this.isInvisible(t)||i<=0?e.style.display="none":(e.style.display="",e.style.width=`${i}px`)},updateColumnHeaderHeight:function(t,e){if(t.tg_height=0,t.tg_width<=0)return;if(this.isInvisible(t))return;e&&(t.tg_element_height=0);const i=t.tg_element_height;if(i)return void(t.tg_height=i);const o=this.getColumnHeaderHeight(t);t.tg_height=o,t.tg_element_height=o},getColumnHeaderHeight:function(t){const e=this.getColumnHeaderNode(t);return e?e.clientHeight:0},updateColumnGroupWidth:function(t){const e=t.tg_parent;if(!e)return;const i=this.getColumnGroupWidth(e);e.tg_width!==i&&(e.tg_width=i,this.updateColumnHeaderSize(e))},getColumnGroupWidth:function(t){if(this.isInvisible(t))return 0;let e=0;return t.subs&&t.subs.forEach((t=>{this.isInvisible(t)||d.isNum(t.tg_width)&&(e+=t.tg_width)})),e}},L={initTreeInfo:function(t,e){const i=[];let o=!1,n=0,s=0;const r=function(t,r,l){(t=>{t.invisible?t.tg_invisible=!0:t.tg_invisible&&(t.tg_invisible=!1)})(t),((t,i)=>{if(e>=0&&!t.tg_invisible)return t.tg_frozen=!0,void(e-=1);t.tg_frozen&&(t.tg_frozen=!1)})(t),(t=>{if(d.hasOwn(t,"subs")){if(Array.isArray(t.subs))return o=!0,t.tg_group=!0,void(t.tg_subs_length=t.subs.length);t.subs=null}t.tg_group&&(t.tg_group=!1)})(t),((t,e)=>{t.tg_parent=e;let i=0;e&&(i=e.tg_level+1,i>n&&(n=i)),t.tg_level=i})(t,l),t.tg_index=s,t.tg_sub_index=r,i.push(t),s+=1},l=function(t,e){let i=0;const o=t.length;for(;i<o;){let o=t[i];o&&"object"==typeof o||(o={},t[i]=o),r(o,i,e),o.subs&&l(o.subs,o),i++}};return l(t),{indexCache:i,isTree:o,maxLevel:n,length:s}},initViewList:function(t,e){let i=0;const o=t.length;let n;for(;i<o;)n=t[i],n.tg_view_index=i,e.call(this,n,i),i++;return n&&(n.tg_view_last=!0),this},forEachRow:function(t){return d.forEachTree(this.rows,t),this},forEachColumn:function(t){return d.forEachTree(this.columns,t),this},forEachSelectableRow:function(t){return this.forEachRow(((e,i,o)=>{if(!this.isInvisible(e))return this.isRowSelectable(e)?t(e,i,o):void 0})),this},toRowItemList:function(t,e){let i=d.toList(t).map((t=>this.getRowItem(t))).filter((t=>t));return"function"==typeof e&&(i=i.filter(e)),i},toColumnItemList:function(t,e){let i=d.toList(t).map((t=>this.getColumnItem(t))).filter((t=>t));return"function"==typeof e&&(i=i.filter(e)),i},isRowLeaf:function(t){return!!t&&("blank"!==t.formatter&&(!t.tg_frozen&&!t.tg_group))},isRowSelectable:function(t){return!!t&&(d.hasOwn(t,"selectable")?Boolean(t.selectable):this.isRowLeaf(t))},isEmptyGroup:function(t){return!(!t||!t.tg_group||0!==t.tg_subs_length)},isInvisible:function(t){return!!t&&(!(!t.tg_filtered&&!t.tg_invisible)||!!this.isInvisible(t.tg_parent))},isSortable:function(t){return!!t&&(!d.hasOwn(t,"sortable")||Boolean(t.sortable))},isCollapsedChanged:function(t,e){return Boolean(t.collapsed)!==e},isSelectedChanged:function(t,e){return Boolean(t.selected)!==e}},x={updateCssRules:function(){this.cssRulesInvalid&&(this.cssRulesInvalid=!1,this.initCssRules(),this.updateColumnsCssRules(),this.updateHeadersCssRules(),this.updateStyleElement())},initCssRules:function(){this.removeCssRules(),this.cssList={},this.cssDisplayCache={};const t=this.getRowHeight(),e=this.createCssRule(".tg-row");e.height=`${t}px`,e["line-height"]=`${t}px`},resetCssDisplay:function(t){if(this.cssDisplayCache){t=t||"";for(const e in this.cssDisplayCache)if(d.hasOwn(this.cssDisplayCache,e)){this.cssDisplayCache[e].style.display=t}}},updateColumnsCssRules:function(){const t=this.viewColumns,e=this.frozenInfo.column,i={};let o=0;for(let n=0,s=t.length;n<s;n++){const s=t[n],r=this.getColumnCssWidth(s);this.updateCssRuleItem(s.tg_view_index,o,r),this.updateParentCssRules(s,o,i),n===e?o=0:o+=r}},updateParentCssRules:function(t,e,i){const o=t.tg_parent;if(!o)return;const n=o.tg_view_index;i[n]||(this.updateCssRuleItem(n,e,o.tg_width),i[n]=o,this.updateParentCssRules(o,e,i))},updateHeadersCssRules:function(){let t=0;const e=this.columnsInfo.maxLevel;for(let i=e;i>=0;i--){const e=this.headerLayerHeight[i],o=this.createCssRule(`.tg-h-${i}`);o.bottom=`${t}px`,o.height=`${e}px`,t+=e}this.getLayerCombinations(e).forEach((t=>{const e=this.createCssRule(`.tg-h-${t}`);let i=0;t.split("").forEach((t=>{i+=this.headerLayerHeight[t]||0})),e.height=`${i}px`}))},getLayerCombinations:function(t){let e="";for(;t>=0;)e+=t,t--;if(e.length<2)return[];const i=[],o=function(t,e){const n=t.length;let s=e+2;for(;s<=n;){const o=t.substring(e,s);i.push(o),s++}e<n-2&&o(t,e+1)};return o(e,0),i},updateCssRuleItem:function(t,e,i){const o=this.createCssRule(`.tg-c-${t}`);o.left=`${e}px`,o.width=`${i}px`,o.display=0===i?"none":""},createCssRule:function(t){const i=`.${e.NS}.${this.id} `+t,o={};return this.cssList[i]=o,o},getColumnCssWidth:function(t){let e=t.tg_width;return(this.isInvisible(t)||e<=0)&&(e=0),e},updateStyleElement:function(){this.styleElement||this.createStyleElement(),this.checkNewCssName()&&this.initStyleElement();const t=this.getStyleSheetCssRules();if(t)for(let e=0,i=t.length;e<i;e++)this.updateRuleProperties(t[e])},createStyleElement:function(){const t=this.shadowRoot||document.head;this.styleElement=document.createElement("style"),this.styleElement.setAttribute("context",this.id),t.appendChild(this.styleElement)},checkNewCssName:function(){if(!this.previousCssList)return!0;for(const t in this.cssList)if(d.hasOwn(this.cssList,t)&&!this.previousCssList[t])return!0;return!1},initStyleElement:function(){const t=[];Object.keys(this.cssList).forEach((function(e){t.push(`${e}{}`)}));const e=t.join("\n");this.styleElement.innerHTML=e,this.previousCssList=this.cssList},getStyleSheetCssRules:function(){const t=this.styleElement.sheet;if(t)return t.cssRules},updateRuleProperties:function(t){const e=`${t.selectorText}`,i=this.cssList[e];if(i)for(const o in i)if(d.hasOwn(i,o)){const n=i[o];t.style[o]=n,"display"===o&&"none"===n&&(this.cssDisplayCache[e]=t)}},removeCssRules:function(){this.previousCssList=null,this.cssList=null,this.cssDisplayCache=null,this.styleElement&&(this.removeNode(this.styleElement),this.styleElement=null)}},z={reset:function(){return this.firstUpdated=!1,this.headerCreated=!1,d.cancelAsync(this),d.removePreProps(this,"previous"),this.removeSortColumn(),this.columns=null,this.columnsInfo=null,this.viewColumns=null,this.viewAllColumns=null,this.viewGroupColumns=null,this.rows=null,this.rowsInfo=null,this.viewRows=null,this.headerRowItem=null,this.protectedItem=null,this.selectColumn=null,this.rowDragColumn=null,this.rowNumberColumn=null,this.blankColumn=null,this.sortColumn=null,this.nullFormatter=null,this.frozenRowsHeight=0,this.totalRowsHeight=0,this.scrollRowsHeight=0,this.bodyWidthL=0,this.bodyWidthR=0,this.bodyHeightT=0,this.bodyHeightB=0,this.paneWidthL=0,this.paneWidthR=0,this.paneHeightT=0,this.paneHeightB=0,this.scrollLeft=0,this.scrollTop=0,this.scrollTopOffset=0,this},destroy:function(){this.destroyed||(this.destroyed=!0,this.trigger(y.onDestroy),this.reset(),this.unbindWindowResize(),this.unbindContainerResize(),this.unbindEvents(),this.unbind(),this.delEventListeners(),this.removeCssRules(),this.removeScrollPane(),this.container=null,this.$container&&(this.$container.remove(),this.$container=null),this.holder=null,this.$holder&&(this.$holder.empty(),this.$holder=null),this.removeCache(),d.removePreProps(this,"$"),this.options=null,this.formatters=null,this.data=null)}},M=function(t){Object.assign(this,t),this.timeStamp=(new Date).getTime()};M.prototype={constructor:M,cancelable:!0,defaultPrevented:!1,preventDefault:function(){this.defaultPrevented=!0},isPropagationStopped:!1,stopPropagation:function(){this.isPropagationStopped=!0},isImmediatePropagationStopped:!1,stopImmediatePropagation:function(){this.isImmediatePropagationStopped=!0,this.stopPropagation()}};const P=M,N={getEventItem:function(t,e,i,o){if(!(e=String(e)))return;o=o||{};const n=e.split(".");return{type:n.shift(),target:t,context:e,namespace:n.join("."),handler:i,once:o.once}},getEventListByString:function(t,e,i,o){const n=[];return e.split(" ").forEach((function(e){const s=N.getEventItem(t,e,i,o);s&&n.push(s)})),n},getEventListByObject:function(t,e,i){const o=[];return Object.keys(e).forEach((function(n){const s=N.getEventItem(t,n,e[n],i);s&&o.push(s)})),o},getEventList:function(t,e,i,o){return e?"string"==typeof e?N.getEventListByString(t,e,i,o):"object"==typeof e?N.getEventListByObject(t,e,i):[]:[]},addEvent:function(t,e,i){if(t.events.length>=i){let t="Possible Event memory leak detected. ";return t+=`More than ${i} (max limit) listeners added. `,t+="Use setMaxListeners(n) to increase limit.",void console.warn(t,e)}t.events.push(e)},addEvents:function(t,e,i){e.forEach((function(e){const o=e.type;t[o]||(t[o]={events:[]});if("function"!=typeof e.handler)return;const n=t[o];N.addEvent(n,e,i)}))},removeEventByNamespace:function(t,e){Object.keys(t).forEach((function(i){const o=t[i],n=[];o.events.forEach((function(t){t&&t.namespace!==e&&n.push(t)})),o.events=n}))},removeEventByHandler:function(t,e,i){const o=t[e];if(!o)return;const n=[];o.events.forEach((function(t){t&&t.handler!==i&&n.push(t)})),o.events=n},removeEventByType:function(t,e){const i=t[e];i&&(i.events=[])},removeEvent:function(t,e){const i=e.type,o=e.namespace;if(!i&&o)return void N.removeEventByNamespace(t,o);const n=e.handler;"function"!=typeof n?N.removeEventByType(t,i):N.removeEventByHandler(t,i,n)},removeEvents:function(t,e){e.forEach((function(e){N.removeEvent(t,e)}))},removeAllEvents:function(t){Object.keys(t).forEach((function(e){N.removeEventByType(t,e)}))},sendEventList:function(t,e,i,o){const n=e.events;for(let e=0;e<n.length;e++){const s=n[e];if(!s.onceCalled&&(s.once&&(s.onceCalled=!0),i.namespace=s.namespace,s.handler.call(t,i,o),i.isPropagationStopped))break}e.events=n.filter((t=>!t.onceCalled))},sendEvent:function(t,e,i,o){const n=e[i];if(!n)return;const s=new P({type:i,target:t,currentTarget:t,data:o});N.sendEventList(t,n,s,o)}},_=N;class k{maxListeners=10;setMaxListeners(t){this.maxListeners=Number(t)||10}getMaxListeners(){return this.maxListeners}getEventListeners(){return this.eventListeners||(this.eventListeners={}),this.eventListeners}delEventListeners(){this.eventListeners=null}bind(t,e,i){const o=_.getEventList(this,t,e,i);if(!o.length)return this;const n=this.getEventListeners();return _.addEvents(n,o,this.maxListeners),this}once(t,e){return this.bind(t,e,{once:!0})}unbind(t,e,i){const o=this.getEventListeners();if(!arguments.length)return _.removeAllEvents(o),this;const n=_.getEventList(this,t,e,i);return n.length?(_.removeEvents(o,n),this):this}trigger(t,e){const i=this.getEventListeners();return _.sendEvent(this,i,t,e),this}}const V={DRAG_START:"drag_start",DRAG_MOVE:"drag_move",DRAG_END:"drag_end"};class O extends k{static EVENT=V;generateOptions(t){return d.merge({type:"mouse",startX:0,startY:0,previousX:0,previousY:0,currentX:0,currentY:0,moveX:0,moveY:0,offsetX:0,offsetY:0,changed:!1},t)}start(t,e){t&&(this.unbindEvents(),this.bindEvents(),this.options=this.generateOptions(e),this.startHandler(t))}bindEvents(){this.windowEvents={mousemove:{handler:t=>{this.iframeHandler(t),this.mouseMoveHandler(t)},options:!0},mouseup:{handler:t=>{this.mouseUpHandler(t)},options:{once:!0}}},d.bindEvents(this.windowEvents,window)}unbindEvents(){d.unbindEvents(this.windowEvents),this.windowEvents=null,this.previousIframe&&(this.previousIframe.classList.remove("tg-pointer-events-none"),this.previousIframe=null)}iframeHandler(t){const e=t.target;"IFRAME"===e.nodeName&&e!==this.previousIframe&&(this.previousIframe&&this.previousIframe.classList.remove("tg-pointer-events-none"),e.classList.add("tg-pointer-events-none"),this.previousIframe=e)}startHandler(t){const e=this.options;e.e=t,e.startX=t.pageX,e.startY=t.pageY,e.currentX=e.startX,e.currentY=e.startY,this.hasMoved=!1}mouseMoveHandler(t){d.preventDefault(t);const e=this.options;e.e=t,e.previousX=e.currentX,e.previousY=e.currentY,e.currentX=t.pageX,e.currentY=t.pageY,e.moveX=e.currentX-e.previousX,e.moveY=e.currentY-e.previousY,e.offsetX=e.currentX-e.startX,e.offsetY=e.currentY-e.startY,e.changed=!(0===e.offsetX&&0===e.offsetY),this.hasMoved?this.trigger(V.DRAG_MOVE,e):(this.hasMoved=!0,this.trigger(V.DRAG_START,e))}mouseUpHandler(t){this.unbindEvents();const e=this.options;this.hasMoved&&(e.e=t,d.preventDefault(t),this.trigger(V.DRAG_END,e))}destroy(){this.unbindEvents(),this.unbind()}}const $={Linear:{None:function(t){return t}}},D={MOTION_START:"motion_start",MOTION_MOVE:"motion_move",MOTION_END:"motion_end",MOTION_STOP:"motion_stop"};class B extends k{static EVENT=D;constructor(t){super(),this.constructorOptions=t,this.stopped=!0}generateOptions(t){return d.merge({easing:null,duration:100,from:0,till:1,data:0},this.constructorOptions,t)}stop(){return this.stopped||(this.stopped=!0,this.cancelAnimationFrame(),this.trigger(D.MOTION_STOP,this.data)),this}start(t){return this.stop(),this.stopped=!1,this.options=this.generateOptions(t),this.initCalculation(),this.data=this.calculateHandler(0),this.trigger(D.MOTION_START,this.data),this.stopped||(this.time=Date.now(),this.requestAnimationFrame(this.moveHandler)),this}requestAnimationFrame(t){this.requestId=window.requestAnimationFrame((()=>{t.apply(this)}))}cancelAnimationFrame(){window.cancelAnimationFrame(this.requestId)}getEasing(t){return"function"!=typeof t&&(t=d.getValue($,t,$.Linear.None)),t}moveHandler(){const t=Date.now()-this.time,e=this.duration;if(t<e){const i=t/e;return this.data=this.calculateHandler(i),this.trigger(D.MOTION_MOVE,this.data),void this.requestAnimationFrame(this.moveHandler)}this.cancelAnimationFrame(),this.data=this.calculateHandler(1),this.trigger(D.MOTION_MOVE,this.data),this.trigger(D.MOTION_END,this.data)}initCalculation(){const t=this.options;this.duration=d.toNum(t.duration,!0)||100,this.easing=this.getEasing(t.easing),this.calculateKeys=null;const e=t.from,i=t.till;d.isNum(e)&&d.isNum(i)?this.calculateType=this.calculateNumber:this.calculateType=e&&"object"==typeof e&&i&&"object"==typeof i?this.calculateObject:this.calculateNone}calculateHandler(t){const e=this.easing(t),i=this.options;return this.calculateType(e,i.from,i.till)}calculateObject(t,e,i){const o={};return this.calculateKeys?(this.calculateKeys.forEach((n=>{o[n]=this.calculateNumber(t,e[n],i[n])})),o):(this.calculateKeys=[],Object.keys(e).forEach((n=>{const s=e[n],r=i[n];d.isNum(s)&&d.isNum(r)&&(o[n]=this.calculateNumber(t,s,r),this.calculateKeys.push(n))})),o)}calculateNumber(t,e,i){return(i-e)*t+e}calculateNone(t,e,i){return e}destroy(){this.stop(),this.unbind()}}const A={TOUCH_START:"touch_start",TOUCH_MOVE:"touch_move",TOUCH_END:"touch_end",TOUCH_INERTIA:"touch_inertia"};class W extends k{static EVENT=A;generateOptions(t){return d.merge({type:"touch",startX:0,startY:0,previousX:0,previousY:0,currentX:0,currentY:0,moveX:0,moveY:0,offsetX:0,offsetY:0,changed:!1,touchLength:0,direction:"",inertia:!1,inertiaTime:200},t)}start(t,e){t&&(this.unbindEvents(),this.bindEvents(),this.options=this.generateOptions(e),this.startHandler(t))}bindEvents(){this.touchEvents={touchmove:{handler:t=>{this.touchMoveHandler(t)},options:{passive:!1}},touchend:{handler:t=>{this.touchEndHandler(t)},options:{passive:!1,once:!0}},touchcancel:{handler:t=>{this.touchCancelHandler(t)},options:{passive:!1,once:!0}}},d.bindEvents(this.touchEvents,document.body)}unbindEvents(){this.motionStop(),d.unbindEvents(this.touchEvents),this.touchEvents=null}startHandler(t){this.trackingPoints=[];const e=t.touches,i=e[0];if(!i)return;const o=this.options;o.e=t,o.startX=i.clientX,o.startY=i.clientY,o.currentX=o.startX,o.currentY=o.startY,o.touchLength=e.length,this.addTrackingPoint(o),this.trigger(A.TOUCH_START,o)}touchMoveHandler(t){const e=t.touches,i=e[0];if(!i)return;const o=this.options;o.e=t,o.previousX=o.currentX,o.previousY=o.currentY,o.currentX=i.clientX,o.currentY=i.clientY,o.moveX=o.currentX-o.previousX,o.moveY=o.currentY-o.previousY,o.offsetX=o.currentX-o.startX,o.offsetY=o.currentY-o.startY,o.changed=!(0===o.offsetX&&0===o.offsetY),o.touchLength=e.length,o.direction=this.getDirection(o),this.addTrackingPoint(o),this.trigger(A.TOUCH_MOVE,o)}touchEndHandler(t){this.unbindEvents();const e=this.options;e.e=t,this.trigger(A.TOUCH_END,e);const i=t.changedTouches[0];if(!i)return;const o=t.touches;e.touchLength=o.length,e.touchLength>0||(e.currentX=i.clientX,e.currentY=i.clientY,this.addTrackingPoint(e),this.motionStart())}touchCancelHandler(t){this.unbindEvents(),this.trigger(A.TOUCH_END,this.options)}getMotionInfo(){const t=this.trackingPoints;if(t.length<2)return;if(this.filterTrackingPoints(t),t.length<2)return;const e=t[0],i=t[t.length-1],o=i.t-e.t;if(o<=0)return;let n=i.x-e.x,s=i.y-e.y;const r=Math.abs(n),l=Math.abs(s);r>l?s=0:n=0;return{offsetDistance:Math.max(r,l),offsetTime:o,offsetX:n,offsetY:s}}motionStart(){const t=this.options;if(!t.inertia)return;const e=this.getMotionInfo();if(!e)return;const i=500*e.offsetDistance/50,o=d.clamp(i,20,2e3),n={x:20*(e.offsetX/e.offsetTime),y:20*(e.offsetY/e.offsetTime)};this.motion=new B,this.motion.bind(B.EVENT.MOTION_MOVE,((e,i)=>{t.touchInertiaX=i.x,t.touchInertiaY=i.y,this.trigger(A.TOUCH_INERTIA,t)})),this.motion.start({duration:o,from:n,till:{x:0,y:0}})}motionStop(){this.motion&&(this.motion.destroy(),this.motion=null)}getDirection(t){const i=t.offsetX,o=t.offsetY,n=Math.abs(i),s=Math.abs(o);if(n<s){if(o>0)return e.UP;if(o<0)return e.DOWN}if(n>s){if(i>0)return e.LEFT;if(i<0)return e.RIGHT}return""}filterTrackingPoints(t){t.reverse();const e=t.length,i=Date.now(),o=this.options.inertiaTime;for(let n=0;n<e;n++)if(i-t[n].t>o){t.length=n;break}t.reverse()}addTrackingPoint(t){if(!t.inertia)return;const e=t.currentX,i=t.currentY,o=Date.now(),n=this.trackingPoints;n.push({x:e,y:i,t:o}),n.length>100&&this.filterTrackingPoints(n)}destroy(){this.unbindEvents(),this.unbind()}}const F={getAllEvents:function(){return[].concat(H)},bindEvents:function(){this.unbindEvents(),this.containerEvents={mousedown:{handler:t=>{this.containerMouseDownHandler(t)},options:!0},mousemove:{handler:t=>{this.containerMouseMoveHandler(t)},options:!0},mouseover:{handler:t=>{this.containerMouseOverOutHandler(t,!0)},options:!0},mouseout:{handler:t=>{this.containerMouseOverOutHandler(t,!1)},options:!0},mouseenter:{handler:t=>{this.containerMouseEnterLeaveHandler(t,!0)},options:!0},mouseleave:{handler:t=>{this.containerMouseEnterLeaveHandler(t,!1)},options:!0},touchstart:{handler:t=>{this.containerTouchStartHandler(t)},options:{passive:!1}},touchmove:{handler:t=>{this.containerTouchMoveHandler(t)},options:{passive:!1}},touchend:{handler:t=>{this.containerTouchEndHandler(t)},options:{passive:!1}},touchcancel:{handler:t=>{this.containerTouchCancelHandler(t)},options:{passive:!1}},wheel:{handler:t=>{this.containerWheelHandler(t)},options:{passive:!1}},click:{handler:t=>{this.containerClickHandler(t)},options:!0},dblclick:{handler:t=>{this.containerDblClickHandler(t)},options:!0},contextmenu:{handler:t=>{this.containerContextMenuHandler(t)},options:!0},selectstart:{handler:t=>{this.containerSelectStartHandler(t)},options:!0},keydown:{handler:t=>{this.containerKeyDownHandler(t)},options:!0}},d.bindEvents(this.containerEvents,this.container),this.columnWidthDrag=new O,this.columnWidthDrag.bind(O.EVENT.DRAG_START,((t,e)=>{this.columnWidthDragStartHandler(t,e)})).bind(O.EVENT.DRAG_MOVE,((t,e)=>{this.columnWidthDragMoveHandler(t,e)})).bind(O.EVENT.DRAG_END,((t,e)=>{this.columnWidthDragEndHandler(t,e)})),this.columnWidthTouch=new W,this.columnWidthTouch.bind(W.EVENT.TOUCH_START,((t,e)=>{this.columnWidthTouchStartHandler(t,e)})).bind(W.EVENT.TOUCH_MOVE,((t,e)=>{this.columnWidthTouchMoveHandler(t,e)})).bind(W.EVENT.TOUCH_END,((t,e)=>{this.columnWidthTouchEndHandler(t,e)})),this.rowDrag=new O,this.rowDrag.bind(O.EVENT.DRAG_START,((t,e)=>{this.rowDragStartHandler(t,e)})).bind(O.EVENT.DRAG_MOVE,((t,e)=>{this.rowDragMoveHandler(t,e)})).bind(O.EVENT.DRAG_END,((t,e)=>{this.rowDragEndHandler(t,e)})),this.rowTouch=new W,this.rowTouch.bind(W.EVENT.TOUCH_START,((t,e)=>{this.rowDragStartHandler(t,e)})).bind(W.EVENT.TOUCH_MOVE,((t,e)=>{this.rowDragMoveHandler(t,e)})).bind(W.EVENT.TOUCH_END,((t,e)=>{this.rowDragEndHandler(t,e)})),this.scrollTouch=new W,this.scrollTouch.bind(W.EVENT.TOUCH_START,((t,e)=>{this.scrollTouchStartHandler(t,e)})).bind(W.EVENT.TOUCH_MOVE,((t,e)=>{this.scrollTouchMoveHandler(t,e)})).bind(W.EVENT.TOUCH_END,((t,e)=>{this.scrollTouchEndHandler(t,e)})).bind(W.EVENT.TOUCH_INERTIA,((t,e)=>{this.scrollTouchInertiaHandler(t,e)}))},isDefaultPrevented:function(t){if(t){if(t.defaultPrevented)return!0;if(t.e&&t.e.defaultPrevented)return!0}return!1},getEventClosestNode:function(t,e){if(t&&t!==this.container)return t.classList.contains(e)?t:this.getEventClosestNode(t.parentNode,e)},getEventClosestData:function(t){if(!t||t===this.container)return;const e=this.getNodeDataCache(t);return e||this.getEventClosestData(t.parentNode)},getEventData:function(t){const e=this.getEventClosestData(t.target);if(e)return e.e=t,e},getWheelDelta:function(t,e,i){let o=t.deltaX,n=t.deltaY;return d.isNum(o)||(o=d.toNum(t.wheelDeltaX)),d.isNum(n)||(n=d.toNum(t.wheelDeltaY||t.wheelDelta)),1===t.deltaMode?(n*=e,o*=e):2===t.deltaMode&&(n*=i,o*=i),{deltaX:o,deltaY:n}},columnResizingMouseDownHandler:function(t){const e=this.getEventData(t);e&&this.columnWidthDrag.start(t,{columnItem:e.columnItem})},columnResizingTouchStartHandler:function(t){const e=this.getEventData(t);e&&this.columnWidthTouch.start(t,{columnItem:e.columnItem})},columnResizingMouseEnterLeaveHandler:function(t,e){const i=this.getEventData(t);i&&(e?this.showColumnLine(i.columnItem):this.hideColumnLine())},rowDragMouseDownHandler:function(t){const e=this.getEventData(t);e&&this.rowDrag.start(t,{rowItem:e.rowItem})},rowDragTouchStartHandler:function(t){const e=this.getEventData(t);e&&(this.protectedItem=e,this.rowTouch.start(t,{rowItem:e.rowItem}))},scrollPaneTouchStartHandler:function(t){if(!this.hasHScroll&&!this.hasVScroll)return;const e=this.getEventData(t);this.protectedItem=e,this.scrollTouch.start(t,{inertia:!0})},sortHandler:function(t,e){const i=e.columnItem;if(!this.isColumnSortable(i))return;const o=this.getEventClosestNode(t.target,"tg-column-name"),n=this.getEventClosestNode(t.target,"tg-column-sort");(o||n)&&(this.trigger(y.onSort,e),this.isDefaultPrevented(e)||this.setSortColumn(i))},selectIconAllClickHandler:function(t){const e=w(t);let i=!1;(e.hasClass("tg-selected")||e.hasClass("tg-mixed"))&&(i=!0),i=!i,this.selectAll(i)},cellEnterLeaveHandler:function(t,e){const i=this.getEventData(t);i&&(e?this.trigger(y.onCellMouseEnter,i):this.trigger(y.onCellMouseLeave,i))},rowEnterLeaveHandler:function(t,e){const i=this.getEventData(t);if(i&&(e?this.trigger(y.onRowMouseEnter,i):this.trigger(y.onRowMouseLeave,i),!this.isDefaultPrevented(i)))return this.renderRowHover(i.rowItem,e),this},containerMouseDownHandler:function(t){if(this.getEventClosestNode(t.target,"tg-column-resizing"))this.columnResizingMouseDownHandler(t);else if(this.options.rowDragVisible){this.getEventClosestNode(t.target,"tg-row-drag-icon")&&this.rowDragMouseDownHandler(t)}},containerMouseMoveHandler:function(t){this.scrollbarFadeInOutHandler(t,!0)},containerMouseOverOutHandler:function(t,e){const i=this.getEventClosestNode(t.target,"tg-cell"),o=this.getEventClosestNode(t.target,"tg-header-item");if(i||o){const i=this.getEventData(t);if(!i)return;e?this.trigger(y.onMouseOver,i):this.trigger(y.onMouseOut,i)}},containerMouseEnterLeaveHandler:function(t,e){this.scrollbarFadeInOutHandler(t,e);if(w(t.target).hasClass("tg-column-resizing"))return void this.columnResizingMouseEnterLeaveHandler(t,e);if(w(t.target).hasClass("tg-cell"))return void this.cellEnterLeaveHandler(t,e);w(t.target).hasClass("tg-row")&&this.rowEnterLeaveHandler(t,e)},containerTouchStartHandler:function(t){this.scrollTouch.motionStop();if(this.getEventClosestNode(t.target,"tg-column-resizing"))return void this.columnResizingTouchStartHandler(t);if(this.options.rowDragVisible){if(this.getEventClosestNode(t.target,"tg-row-drag-icon"))return void this.rowDragTouchStartHandler(t)}const e=this.getEventData(t);e&&(this.trigger(y.onTouchStart,e),this.isDefaultPrevented(e))||this.scrollPaneTouchStartHandler(t)},containerTouchMoveHandler:function(t){const e=this.getEventData(t);e&&this.trigger(y.onTouchMove,e)},containerTouchEndHandler:function(t){const e=this.getEventData(t);e&&this.trigger(y.onTouchEnd,e)},containerTouchCancelHandler:function(t){this.trigger(y.onTouchEnd,{e:t})},containerWheelHandler:function(t){if(this.hasMask)return;const e=this.getRowHeight(),i=this.bodyHeight,o=this.getWheelDelta(t,e,i),n={e:t,deltaX:o.deltaX,deltaY:o.deltaY,delta:o};if(this.trigger(y.onMouseWheel,n),this.isDefaultPrevented(n))return;let s=!1;this.scrollPaneHidden&&(s=this.scrollPaneFrozen.setOffsetH(o.deltaX),o.deltaX=0);(this.scrollPane.mouseWheelHandler(o)||s)&&d.preventDefault(t)},containerClickHandler:function(t){if(this.getEventClosestNode(t.target,"tg-tree-icon-all"))return void this.toggleAllRows();const e=this.getEventClosestNode(t.target,"tg-select-icon-all");if(e)return void this.selectIconAllClickHandler(e);const i=this.getEventData(t);if(!i)return;if(this.getEventClosestNode(t.target,"tg-header-item")){if(this.trigger(y.onClick,i),this.isDefaultPrevented(i))return;return void this.sortHandler(t,i)}if(this.getEventClosestNode(t.target,"tg-tree-icon"))return void this.toggleRow(i.rowItem);this.getEventClosestNode(t.target,"tg-select-icon")?this.setRowSelected(i.rowItem,t):this.trigger(y.onClick,i)},containerDblClickHandler:function(t){const e=this.getEventData(t)||{e:t};this.trigger(y.onDblClick,e)},containerContextMenuHandler:function(t){const e=this.getEventData(t)||{e:t};this.trigger(y.onContextMenu,e)},containerSelectStartHandler:function(t){if(this.options.textSelectable)return;w(t.target).is("input,textarea,code")||d.preventDefault(t)},containerKeyDownHandler:function(t){if(this.hasMask)return;const e={e:t};if(this.trigger(y.onKeyDown,e),this.isDefaultPrevented(e))return;const i=t.keyCode,o={9:this.keyTabHandler,13:this.keyEnterHandler,27:this.keyEscHandler,33:this.keyPageUpHandler,34:this.keyPageDownHandler,35:this.keyEndHandler,36:this.keyHomeHandler,37:this.keyLeftHandler,38:this.keyUpHandler,39:this.keyRightHandler,40:this.keyDownHandler}[i];if(!o)return;o.call(this,t)&&d.preventDefault(t)},unbindEvents:function(){d.unbindEvents(this.containerEvents),this.containerEvents=null,this.columnWidthDrag&&(this.columnWidthDrag.destroy(),this.columnWidthDrag=null),this.columnWidthTouch&&(this.columnWidthTouch.destroy(),this.columnWidthTouch=null),this.rowDrag&&(this.rowDrag.destroy(),this.rowDrag=null),this.rowTouch&&(this.rowTouch.destroy(),this.rowTouch=null),this.scrollTouch&&(this.scrollTouch.destroy(),this.scrollTouch=null),this.protectedItem=null}},G={exportData:function(t){const e=this.getData();return{columns:this.getTreeSnapshot(e.columns,t),rows:this.getTreeSnapshot(e.rows,t)}},isItemExportable:function(t){return!!t&&(!d.hasOwn(t,"exportable")||Boolean(t.exportable))},getTreeSnapshot:function(t,e){const i=(t,o)=>{d.isList(o)&&o.forEach((o=>{if(!this.isItemExportable(o))return;const n=this.getItemSnapshot(o,e),s=o.subs;Array.isArray(s)&&(n.subs=[],i(n.subs,s)),t.push(n)}))},o=[];return i(o,t),o},getItemSnapshot:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i={};return Object.keys(t).forEach((o=>{!0!==e[o]?!1!==e[o]&&"subs"!==o&&0!==o.indexOf("tg_")&&(i[o]=t[o]):i[o]=t[o]})),i}},j={flushRow:function(t){d.toList(t).forEach((t=>{this.deleteRowCache(t)}))},flushRowFrom:function(t){d.isNum(t)&&(0!==t?this.forEachRowsCache((e=>{e>=t&&this.deleteRowCache(e)})):this.flushBody())},flushBody:function(){this.forEachRowsCache((t=>{this.deleteRowCache(t)}))},flushSort:function(){this.frozenInfo.rows?this.flushRowFrom(this.frozenInfo.rows):this.flushBody()},flushColumn:function(t){const e=d.toList(t);this.forEachRowsCache(((t,i,o,n)=>{e.forEach((t=>{this.deleteCellCache(t,o,n)}))}))},flushColumnFrom:function(t){d.isNum(t)&&this.forEachRowsCache(((e,i,o,n)=>{o.forEach(((e,i)=>{i>=t&&this.deleteCellCache(i,o,n)}))}))},flushCell:function(t,e){const i=d.toList(t),o=d.toList(e);i.forEach((t=>{const e=this.getRowCache(t);if(!e)return;const i=e.cellNodes,n=e.observerNodes;o.forEach((t=>{this.deleteCellCache(t,i,n)}))}))},flushWithViewport:function(){const{rows:t,columns:e}=this.viewport;this.forEachRowsCache(((i,o,n,s)=>{t.includes(i)?n.forEach(((t,i)=>{e.includes(i)||this.deleteCellCache(i,n,s)})):this.deleteRowCache(i)}))}},U={"sort-h":'\n<svg class="tg-icon-sort-h" pointer-events="none" viewBox="0 0 19 6">\n    <path class="tg-icon-item tg-icon-item-light tg-asc" fill="currentColor" d="M0 0h10l-5 6z" />\n    <path class="tg-icon-item tg-asc" fill="currentColor" d="M9 6h10l-5 -6z" />\n    <path class="tg-icon-item tg-desc" fill="currentColor" d="M0 0h10l-5 6z" />\n    <path class="tg-icon-item tg-icon-item-light tg-desc" fill="currentColor" d="M9 6h10l-5 -6z" />\n</svg>\n',"sort-v":'\n<svg class="tg-icon-sort-v" pointer-events="none" viewBox="0 0 10 16">\n    <path class="tg-icon-item tg-icon-item-light tg-asc" fill="currentColor" d="M0 9h10l-5 6z" />\n    <path class="tg-icon-item tg-asc" fill="currentColor" d="M0 7h10l-5 -6z" />\n    <path class="tg-icon-item tg-desc" fill="currentColor" d="M0 9h10l-5 6z" />\n    <path class="tg-icon-item tg-icon-item-light tg-desc" fill="currentColor" d="M0 7h10l-5 -6z" />\n</svg>\n',checkbox:'\n<svg class="tg-icon-checkbox" pointer-events="none" viewBox="0 0 16 16">\n    <path class="tg-checkbox-item tg-checkbox-none" fill="currentColor" d="M0,3 q0,-3 3,-3 h9 q3,0 3,3 v9 q0,3 -3,3 h-9 q-3,0 -3,-3 v-9 zM1,3,v9,q0,2 2,2,h9,q2,0 2,-2,v-9,q0,-2 -2,-2,h-9,q-2,0 -2,2,z" />\n    <path class="tg-checkbox-item tg-checkbox-selected" fill="currentColor" d="M0,3 q0,-3 3,-3 h9 q3,0 3,3 v9 q0,3 -3,3 h-9 q-3,0 -3,-3 v-9 zM12,2.5 l-6,8 l-3,-3 l-1,1 l4,4 l7,-9 z" />\n    <path class="tg-checkbox-item tg-checkbox-mixed" fill="currentColor" d="M0,3 q0,-3 3,-3 h9 q3,0 3,3 v9 q0,3 -3,3 h-9 q-3,0 -3,-3 v-9 zM2.5,7 v1 h10 v-1 h-10 z" />\n</svg>\n',radio:'<div class="tg-icon-radio"><div>',drag:'\n<svg class="tg-icon-row-drag" pointer-events="none" viewBox="0 0 32 32">\n    <path fill="currentColor" d="M10 6h4v4h-4zm8 0h4v4h-4zm-8 8h4v4h-4zm8 0h4v4h-4zm-8 8h4v4h-4zm8 0h4v4h-4z"/>\n</svg>\n',tree:'\n<svg class="tg-icon-tree" pointer-events="none" viewBox="0 0 20 20">\n    <path class="tg-tree-item tg-tree-collapsed" fill="currentColor" d="M0,0 l20,10 l-20,10 z" />\n    <path class="tg-tree-item tg-tree-expanded" fill="currentColor" d="M0,0 l10,20 l10,-20 z" />\n</svg>\n'},X={icons:U,getIcon:function(t){let e=U[t];return e=String(e).trim(),e}},Y={header:function(t,e,i,o){return t},null:function(t,e,i,o){return e&&e.tg_group?t:null==t?"—":t},blank:function(t,e,i,o){return""},string:function(t,e,i,o){return t},number:function(t,e,i,o){return t},icon:function(t,e,i,o){return`<span class="tg-symbols">${t}</span>`},select:function(t,e,i,o){return this.isRowSelectable(e)?this.getSelectFormatterContent(e):""},rowDrag:function(t,e,i,o){return this.getRowDragFormatterContent(e)},rowNumber:function(t,e,i,o){return e.tg_row_number||""},tree:function(t,e,i,o){return this.getTreeFormatterContent(t,e)}},K={setFormatter:function(t,e){this.renderType="all";let i=t;if("string"==typeof t){if(this.formatters)return this.formatters[t]=e,this;i={},i[t]=e}return this.customFormatters=i,this},getFormatter:function(t){if(!t)return;const e=this.formatters[t];return"function"==typeof e?e.bind(this):void 0},getDefaultFormatter:function(t){return(Y[t]||Y.string).bind(this)},getSelectFormatterContent:function(t){let e="radio";this.options.selectMultiple&&(e="checkbox");const i=X.getIcon(e);return`<div class="${d.classMap(["tg-select-icon",`tg-${e}`,{"tg-selected":t.selected}])}">${i}</div>`},getRowDragFormatterContent:function(t){if(t.tg_frozen)return"";return`<div class="tg-row-drag-icon">${X.getIcon("drag")}</div>`},getTreeIndentWidth:function(t,i,o){if(!t)return 0;let n=5;return i||(n+=e.TREE_INDENT),n+=o*e.TREE_INDENT,n},getTreeFormatterContent:function(t,e){const i=this.rowsInfo.isTree,o=e.tg_group,n=this.isEmptyGroup(e);n&&(e.collapsed=!0);const s=e.collapsed,r=d.toNum(e.tg_level),l=this.getTreeIndentWidth(i,o,r),h=[];if(h.push(`<div class="tg-tree" style="padding-left:${l}px;">`),o){const t={"tg-tree-icon":!0,"tg-tree-icon-collapsed":s,"tg-tree-icon-expanded":!s,"tg-tree-icon-empty":n},e=X.getIcon("tree"),i=`<div class="${d.classMap(t)}">${e}</div>`;h.push(i)}return h.push(`<div class="tg-tree-name">${t}</div>`),h.push("</div>"),h.join("")}},q={renderHeaderTables:function(){this.clearHeaderCache();const t=this.viewColumns,e=this.frozenInfo.columns;this.hasTreeColumn=!1,this.hasSortColumn=!1;let i=[],o=[];for(let n=0,s=t.length;n<s;n++){const s=t[n];this.isColumnSortable(s)&&(this.hasSortColumn=!0),e&&n>=e?o.push(s):i.push(s)}if(this.frozenInfo.right){const t=i;i=o,o=t}this.renderHeaderTable(i,this.$headerL),this.renderHeaderTable(o,this.$headerR)},renderHeaderTable:function(t,e){const i=document.createElement("div"),o=["tg-header-table"];this.hasSortColumn&&(o.push("tg-header-sortable"),o.push(`tg-header-sort-${this.options.sortIndicator}`)),i.className=d.classMap(o);const n=t.length;if(n){let e=t[n-1];e&&"tg-column-blank"===e.id&&(e=t[n-2]),t.forEach((t=>{this.renderHeaderItem(t,i,e)}))}e.append(i)},renderHeaderItem:function(t,e,i){const o=t.tg_view_index;if(this.getHeaderCache(o))return;const n=this.getHeaderItemClass(t,i),s=d.styleMap(t.headerStyleMap),r={column:o,class:n,data:t.id};s&&(r.style=s);const l=[this.createColumnHeader(t)];if(this.isColumnResizable(t)){const e=this.createColumnResizing(t);l.push(e)}const h=this.createElement("div",r,l);e.appendChild(h),this.setHeaderCache(o,h),this.setNodeDataCache(h,{rowItem:this.headerRowItem,column:o,columnItem:t,headerNode:h}),t.tg_parent&&this.renderHeaderItem(t.tg_parent,e)},createColumnHeader:function(t){const e={class:this.getHeaderClass(t),style:this.getHeaderStyle(t)},i=[this.createColumnName(t)];if(this.hasSortColumn&&!t.tg_group){const e=this.createColumnSort(t);i.push(e)}return this.createElement("div",e,i)},createColumnName:function(t){const e=["tg-column-name"];t.tg_group&&e.push("tg-header-group-name");const i={class:e.join(" ")};let o=t.name;const n=t.tg_headerFormatter;return"function"==typeof n&&(o=n.call(this,o,this.headerRowItem,t)),"tree"===t.formatter?o=this.createHeaderTreeName(o):t===this.selectColumn&&this.isSelectAllVisible()&&(o=this.createHeaderSelectName()),this.createElement("div",i,o)},createHeaderTreeName:function(t){this.hasTreeColumn=!0;const e=[];if(this.options.collapseAllVisible){const t=X.getIcon("tree"),i=this.createElement("div",{class:"tg-tree-icon tg-tree-icon-all"},t);e.push(i)}else{const t=this.createElement("div",{class:"tg-tree-icon"});e.push(t)}const i=this.createElement("div",{class:"tg-tree-name"},t);e.push(i);return this.createElement("div",{class:"tg-tree"},e)},createHeaderSelectName:function(){const t=X.getIcon("checkbox");return this.createElement("div",{class:"tg-select-icon-all tg-checkbox"},t)},createColumnSort:function(t){let e;return this.isColumnSortable(t)&&(e="h"===this.options.sortIndicator?this.createSortIndicatorH(t):this.createSortIndicatorV(t)),this.createElement("div",{class:"tg-column-sort"},e)},createSortIndicatorH:function(t){const e=X.getIcon("sort-h"),i=[this.createElement("div",{class:"tg-sort-indicator-line"}),this.createElement("div",{class:"tg-sort-indicator-icon"},e)];return this.createElement("div",{class:"tg-sort-indicator"},i)},createSortIndicatorV:function(t){const e=X.getIcon("sort-v"),i=[this.createElement("div",{class:"tg-sort-indicator-icon"},e)];return this.createElement("div",{class:"tg-sort-indicator"},i)},createColumnResizing:function(){return this.createElement("div",{class:"tg-column-resizing"})},getHeaderItemClass:function(t,e){const i=["tg-header-item"];return t.tg_group&&i.push("tg-header-group-item"),t===e&&i.push("tg-header-column-last"),i.push(`tg-c-${t.tg_view_index}`),i.push(`tg-h-${t.tg_layer}`),t.tg_combination&&i.push(`tg-h-${t.tg_combination}`),i.push(d.classMap(t.headerClassMap)),d.classMap(i)},getHeaderClass:function(t){const e=["tg-column-header"];return"tree"===t.formatter&&(e.push("tg-tree-header"),this.rowsInfo.isTree&&e.push("tg-tree-header-indent")),this.isColumnSortable(t)&&e.push(`tg-column-sortable tg-column-sort-${this.options.sortIndicator}`),t.align&&e.push(`tg-align-${t.align}`),e.join(" ")},getHeaderStyle:function(t){const e=[d.styleMap(t.headerStyleMap)],i=t.tg_width;return this.isInvisible(t)||i<=0?e.push("display:none;"):e.push(`width:${i}px;`),e.join("")}},J={renderHeader:function(){this.cssRulesInvalid=!0,this.$headerL.empty(),this.$headerR.empty(),this.resetCssDisplay(),this.renderHeaderTables(),this.renderHeaderSort(),this.headerCreated=!0,this.trigger(y.onHeaderUpdated,{node:this.$headerFrame.get(0)})},initHeaderLayerHeight:function(){this.updateScrollPaneHiddenState(),this.resetCssDisplay(),this.viewAllColumns.forEach((t=>{this.updateColumnHeaderHeight(t)})),this.resetCssDisplay("none"),this.updateHeaderLayerHeight()},updateHeaderLayerHeight:function(){const t={},e=this.columnsInfo.maxLevel;for(let i=0;i<=e;i++)t[i]=0;const i=[];this.viewAllColumns.forEach((function(e){if(e.tg_combination)i.push(e);else{const i=e.tg_height,o=e.tg_layer;t[o]=Math.max(t[o],i)}})),i.forEach((function(e){let i=e.tg_height;const o=e.tg_combination.split(""),n=o.pop();o.forEach((function(e){i-=t[e]||0})),t[n]=Math.max(t[n],i)}));const o=JSON.stringify(t);this.previousHeaderLayerHeight!==o&&(this.previousHeaderLayerHeight=o,this.headerLayerHeight=t,this.cssRulesInvalid=!0)}};var Q=i(915);const Z={create:function(t){this.id=d.uid(4,"tg-"),d.isObject(t)||(t={container:t}),this.constructorOptions=t,this.createCache(),this.createView(t.container)},createView:function(t){this.createHolder(t),this.$holder?(this.createGlobalStyle(),this.createContainer()):console.error("ERROR: Grid requires a container")},createHolder:function(t){const e=w(t);if(!e.length)return;this.$holder=e,this.$holder.empty(),this.holder=this.$holder.get(0);const i=this.holder.getRootNode();this.shadowRoot=null,i&&i.host&&(this.shadowRoot=i)},createGlobalStyle:function(){const t=this.shadowRoot||document.head;if(t.querySelector(`style[context="${e.ID}"]`))return;const i=document.createElement("style");i.setAttribute("context",e.ID),i.innerHTML=Q.A.toString(),t.appendChild(i)},createContainer:function(){return this.$container=w('<div tabindex="0">\r\n\r\n    <div class="tg-header-frame">\r\n\r\n        <div class="tg-scroll-pane tg-pane tg-pane-header tg-pane-left tg-pane-header-left">\r\n            <div class="tg-scroll-view">\r\n                <div class="tg-scroll-body tg-header tg-header-left"></div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class="tg-scroll-pane tg-pane tg-pane-header tg-pane-right tg-pane-header-right">\r\n            <div class="tg-scroll-view">\r\n                <div class="tg-scroll-body tg-header tg-header-right"></div>\r\n            </div>\r\n        </div>\r\n\r\n    </div>\r\n\r\n    <div class="tg-body-frame">\r\n\r\n        <div class="tg-scroll-pane tg-pane tg-pane-top tg-pane-left tg-pane-top-left">\r\n            <div class="tg-scroll-view">\r\n                <div class="tg-scroll-body tg-body tg-body-top tg-body-left tg-body-top-left"></div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class="tg-scroll-pane tg-pane tg-pane-top tg-pane-right tg-pane-top-right">\r\n            <div class="tg-scroll-view">\r\n                <div class="tg-scroll-body tg-body tg-body-top tg-body-right tg-body-top-right"></div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class="tg-scroll-pane tg-pane tg-pane-bottom tg-pane-left tg-pane-bottom-left">\r\n            <div class="tg-scroll-view">\r\n                <div class="tg-scroll-body tg-body tg-body-bottom tg-body-left tg-body-bottom-left"></div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class="tg-scroll-pane tg-pane tg-pane-bottom tg-pane-right tg-pane-bottom-right">\r\n            <div class="tg-scroll-view">\r\n                <div class="tg-scroll-body tg-body tg-body-bottom tg-body-right tg-body-bottom-right"></div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class="tg-body-message"></div>\r\n\r\n    </div>\r\n\r\n    <div class="tg-column-line">\r\n        <div class="tg-column-line-item tg-column-line-l"></div>\r\n        <div class="tg-column-line-item tg-column-line-r"></div>\r\n    </div>\r\n\r\n    <div class="tg-mask"></div>\r\n\r\n    <div class="tg-loading">\r\n        <div class="tg-loading-default">\r\n            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" pointer-events="none" width="100%" height="100%">\r\n                <path\r\n                    d="M1,8 A7 7 0 1 1 8 15"\r\n                    stroke="currentColor"\r\n                    stroke-width="2"\r\n                    stroke-linecap="round"\r\n                    fill="none"\r\n                />\r\n            </svg>\r\n        </div>\r\n    </div>\r\n\r\n</div>\r\n').appendTo(this.$holder),this.$container.attr("id",this.id),this.$container.addClass(`${e.NS} ${this.id}`),this.container=this.$container.get(0),d.setInstance(this.container,this),this.$headerFrame=this.$container.find(".tg-header-frame"),this.$paneHL=this.$headerFrame.find(".tg-pane-header-left"),this.$paneHR=this.$headerFrame.find(".tg-pane-header-right"),this.$headerL=this.$paneHL.find(".tg-header-left"),this.$headerR=this.$paneHR.find(".tg-header-right"),this.$header=w().add(this.$headerL).add(this.$headerR),this.$bodyFrame=this.$container.find(".tg-body-frame"),this.$paneTL=this.$bodyFrame.find(".tg-pane-top-left"),this.$paneTR=this.$bodyFrame.find(".tg-pane-top-right"),this.$paneBL=this.$bodyFrame.find(".tg-pane-bottom-left"),this.$paneBR=this.$bodyFrame.find(".tg-pane-bottom-right"),this.$bodyTL=this.$paneTL.find(".tg-body-top-left"),this.$bodyTR=this.$paneTR.find(".tg-body-top-right"),this.$bodyBL=this.$paneBL.find(".tg-body-bottom-left"),this.$bodyBR=this.$paneBR.find(".tg-body-bottom-right"),this.$body=w().add(this.$bodyTL).add(this.$bodyTR).add(this.$bodyBL).add(this.$bodyBR),this.$columnLineContainer=this.$container.find(".tg-column-line"),this.$columnLineItem=this.$columnLineContainer.find(".tg-column-line-item"),this.$columnLineItemL=this.$columnLineContainer.find(".tg-column-line-l"),this.$columnLineItemR=this.$columnLineContainer.find(".tg-column-line-r"),this}},tt={initColumnsHandler:function(){this.columns=this.data.columns,this.columns.forEach(((t,e)=>{t&&"object"==typeof t||(this.columns[e]={})}));const t=this.getPrivateColumns();this.columnsInfo=this.initTreeInfo(t,this.frozenInfo.column);const e=[],i=[],o=(t,n)=>{if(!d.isList(t))return;let s,r=0;t.forEach((t=>{if(!this.isInvisible(t))if(t.tg_group){if(this.isEmptyGroup(t))return;i.push(t),o(t.subs,t)}else t.tg_list_index=r,r+=1,t.tg_list_last=!1,s=t,e.push(t)})),s&&(s.tg_list_last=!0)};o(t),e.forEach((t=>{this.initColumnItemHandler(t)})),i.forEach((t=>{this.initColumnGroupHandler(t)}));const n=[].concat(e).concat(i);this.initViewList(n,((t,e)=>{})),this.viewColumns=e,this.viewGroupColumns=i,this.viewAllColumns=n,this.initHeaderHandler(t),this.initSortColumn()},getPrivateColumns:function(){const t=this.options;this.selectColumn=t.selectColumn,this.rowDragColumn=t.rowDragColumn,this.rowNumberColumn=t.rowNumberColumn,this.blankColumn=t.blankColumn;let e=[];const i=()=>{t.selectVisible&&e.push(this.selectColumn),t.rowDragVisible&&e.push(this.rowDragColumn),t.rowNumberVisible&&(this.rowNumberColumn.width=t.rowNumberWidth,e.push(this.rowNumberColumn))};if(this.frozenInfo.right){const t=this.frozenInfo.column;this.columns.forEach(((o,n)=>{e.push(o),n===t&&i()}))}else i(),e=e.concat(this.columns);return e.push(this.blankColumn),e},setColumns:function(t){this.data.columns=d.toList(t),this.rerender()},getColumns:function(){return this.columns},getViewColumns:function(t){return t?this.viewAllColumns:this.viewColumns},initColumnItemHandler:function(t){this.initColumnProps(t),this.initColumnFormatter(t),this.initColumnWidth(t)},initColumnGroupHandler:function(t){this.initColumnFormatterByName(t,"headerFormatter","header")},initColumnProps:function(t){const e=this.options.columnTypes;if(!d.hasOwn(t,"type")){const i=e[t.id];"string"==typeof i&&(t.type=i)}let i=this.options.columnProps;const o=e[t.type];o&&"object"==typeof o&&(i=d.merge(i,o));for(const e in i)d.hasOwn(t,e)||(t[e]=i[e])},initColumnFormatter:function(t){this.initColumnFormatterByName(t,"headerFormatter","header");let e=t.type;const i=t.formatter;"string"==typeof i&&(e=i),this.initColumnFormatterByName(t,"formatter",e)},initColumnFormatterByName:function(t,e,i){let o=t[e];"function"!=typeof o?(o=this.getFormatter(i),t[`tg_${e}`]=o||this.getFormatter("string")):t[`tg_${e}`]=o.bind(this)},initColumnWidth:function(t){if(t!==this.blankColumn)return d.isNum(t.width)&&t.width>=0?(t.tg_width=t.width,t.minWidth=Math.min(t.minWidth,t.tg_width),void(t.maxWidth=Math.max(t.maxWidth,t.tg_width))):void this.initColumnWidthByName(t);t.tg_width=0},initColumnWidthByName:function(t){const e=this.getComputedColumnWidth(t);d.isNum(e)&&(t.tg_width=e)},getComputedColumnWidth:function(t){const e=t.name||"",i=d.getCharLen(e);let o=Math.round(10*i);return o>103&&(o=Math.max(103,Math.round(10*i/2)),o>133&&(o=Math.max(133,Math.round(10*i/3)),o>163&&(o=Math.max(163,Math.round(10*i/4))))),d.clamp(o,t.minWidth,t.maxWidth)},initSortColumn:function(){this.sortColumn=null;const t=this.options,e=t.sortField;if(!e)return;const i=this.getColumnItemById(e);return i&&this.isColumnSortable(i)?(d.hasOwn(i,"sortAsc")||(i.sortAsc=t.sortAsc),this.sortColumn=i,this):void 0}},et={initHeaderHandler:function(t){this.initHeaderRowItem(),this.viewGroupColumns.reverse(),this.initGroupColumnsWidth(),this.initGroupColumnsLayer(t)},initHeaderRowItem:function(){this.headerRowItem={tg_index:-1,tg_view_index:-1},this.viewAllColumns.forEach((t=>{d.hasOwn(t,"id")&&(this.headerRowItem[t.id]=t.name)}))},initGroupColumnsWidth:function(){this.viewGroupColumns.forEach((t=>{let e=0;t.subs.forEach((t=>{this.isInvisible(t)||(e+=t.tg_width)})),t.tg_width=e}))},initGroupColumnsLayer:function(t){const e=this.columnsInfo.maxLevel;this.viewColumns.forEach((function(t){t.tg_layer=e,t.tg_parent&&(t.tg_parent.tg_layer=e-1)})),this.viewGroupColumns.forEach((function(t){const e=t.tg_layer,i=t.tg_parent;if(i){let t=e-1;d.isNum(i.tg_layer)&&(t=Math.min(t,i.tg_layer)),i.tg_layer=t}})),this.initColumnRowspanHandler(t,0)},initColumnRowspanHandler:function(t,e){t.forEach((t=>{const i=this.initColumnCombinationHandler(t,e);t.tg_group&&this.initColumnRowspanHandler(t.subs,e+i)}))},initColumnCombinationHandler:function(t,e){const i=[],o=t.tg_layer;for(;e<=o;)i.push(e),e+=1;i.reverse();const n=i.length;let s="";return n>1&&(s=i.join("")),t.tg_combination=s,n}},it={},ot={name:"",minWidth:81,maxWidth:300},nt=function(t){return null==t},st=function(t,e){const i=nt(t),o=nt(e);return i&&o?0:i?1:o?-1:void 0},rt=function(t,e){return t.tg_index>e.tg_index?1:-1},lt=function(t,e){return rt(t,e)},ht=function(t,e){if("string"==typeof t&&"string"==typeof e){const i=t.toUpperCase(),o=e.toUpperCase();if(i!==o)return i>o?-1:1}return t>e?-1:1},at=function(t,e,i,o){return t?-1:e?1:ht(i,o)},ct=function(t,e){const i="number"==typeof t,o="number"==typeof e;return i&&o?t>e?-1:1:at(i,o,t,e)},dt=function(t,e){const i=new Date(t),o=new Date(e),n=d.isDate(i),s=d.isDate(o);if(n&&s){const t=i.getTime(),e=o.getTime();if(t===e)return;return t>e?-1:1}return at(n,s,t,e)},ut=function(t,e){const i="boolean"==typeof t,o="boolean"==typeof e;return i&&o?t>e?-1:1:at(i,o,t,e)},gt=function(t,e,i,o){const n=t[i.sortField],s=e[i.sortField],r=st(n,s);if("number"==typeof r)return 0===r?lt(t,e):i.sortBlankFactor*r;if(n!==s&&"function"==typeof o){const t=o(n,s);if(d.isNum(t))return i.sortFactor*t}return lt(t,e)},ft={blankValue:st,equal:lt,index:rt,value:gt,diffType:at,string:function(t,e,i){return gt(t,e,i,ht)},stringValue:ht,number:function(t,e,i){return gt(t,e,i,ct)},numberValue:ct,date:function(t,e,i){return gt(t,e,i,dt)},dateValue:dt,boolean:function(t,e,i){return gt(t,e,i,ut)},booleanValue:ut};const pt={initOptionsHandler:function(){return this.options=this.generateOptions(),this.initOptionsFormatters(),this.initOptionsSort(),this.initOptionsFrozen(),this.initOptionsScrollbar(),this.initOptionsContainer(),this.initBindWindowResize(),this.initBindContainerResize(),this},generateOptions(){const t={className:e.NS,theme:e.ID,headerVisible:!0,rowHeight:32,rowFilter:null,rowFilteredSort:null,rowNotFound:"",rowMoveCrossLevel:!0,rowCacheLength:0,rowProps:it,columnTypes:{tree:{type:"tree",formatter:"tree",width:230,minWidth:120,maxWidth:810},number:{type:"number",align:"right"},date:{type:"date",align:"right"},name:"tree"},columnCacheLength:0,columnProps:ot,collapseAllOnInit:null,collapseAllVisible:!0,selectAllOnInit:null,selectVisible:!1,selectAllVisible:!0,selectMultiple:!0,selectColumn:{private:!0,id:"tg-column-select",name:"",formatter:"select",headerClassMap:"tg-header-select",classMap:"tg-cell-select",width:36,align:"center",resizable:!1,sortable:!1,exportable:!1},rowDragCrossLevel:!0,rowDragVisible:!1,rowDragColumn:{private:!0,id:"tg-column-row-drag",name:"",formatter:"rowDrag",headerClassMap:"tg-header-row-drag",classMap:"tg-cell-row-drag",align:"center",width:36,resizable:!1,sortable:!1,exportable:!1},rowNumberWidth:36,rowNumberFilter:null,rowNumberVisible:!1,rowNumberColumn:{private:!0,id:"tg-column-row-number",name:"",formatter:"rowNumber",headerClassMap:"tg-header-row-number",classMap:"tg-cell-row-number",align:"center",maxWidth:100,sortable:!1,exportable:!1},blankColumn:{private:!0,id:"tg-column-blank",name:"",formatter:"blank",headerClassMap:"tg-header-blank",classMap:"tg-cell-blank",width:0,minWidth:0,maxWidth:4096,resizable:!1,sortable:!1,exportable:!1},sortField:"",sortAsc:!0,sortBlankValueBottom:!0,sortComparers:ft,sortOnInit:!1,sortIndicator:"h",highlightKeywords:{textKey:"tg_text_",textGenerator:null,highlightKey:"tg_highlight_",highlightPre:"<mark>",highlightPost:"</mark>"},frozenRow:-1,frozenRowMax:10,frozenRowHoverable:!1,frozenBottom:!1,frozenColumn:-1,frozenColumnMax:10,frozenRight:!1,scrollbarSize:12,scrollbarSizeH:null,scrollbarSizeV:null,scrollbarRound:!1,scrollbarFade:!1,scrollbarFadeTimeout:1e3,scrollbarType:"auto",scrollPaneMinWidth:30,scrollPaneGradient:30,autoHeight:!1,textSelectable:!1,bindWindowResize:!1,bindContainerResize:!1,cellResizeObserver:null},i=this.generateThemeOptions();return d.merge(t,i,this.constructorOptions,this.customOptions,this.dataOptions)},generateThemeOptions(){const t=this.pickOptions("theme").pop();if(t)return this.getThemeOptions(t)},pickOptions(t){return[this.constructorOptions,this.customOptions,this.dataOptions].map((e=>e&&e[t])).filter((t=>t))},initOptionsFormatters(){let t;const e=this.pickOptions("formatters");e.length&&(t=d.merge.apply(null,e)),this.formatters=d.merge(Y,t,this.customFormatters),this.nullFormatter=this.getFormatter("null")},initOptionsSort(){"v"!==this.options.sortIndicator&&(this.options.sortIndicator="h")},initOptionsFrozen:function(){const t=this.options;this.frozenInfo={column:-1,row:-1,columns:0,rows:0,bottom:Boolean(t.frozenBottom),right:Boolean(t.frozenRight)};let e=d.toNum(t.frozenColumn,!0);e=d.clamp(e,-1,t.frozenColumnMax),e>-1&&!this.frozenInfo.right&&(t.selectVisible&&(e+=1),t.rowDragVisible&&(e+=1),t.rowNumberVisible&&(e+=1)),this.frozenInfo.column=e,e>-1?this.frozenInfo.columns=e+1:(this.frozenInfo.columns=0,this.frozenInfo.right=!1);let i=d.toNum(t.frozenRow,!0);i=d.clamp(i,-1,t.frozenRowMax),this.frozenInfo.row=i,i>-1?this.frozenInfo.rows=i+1:(this.frozenInfo.rows=0,this.frozenInfo.bottom=!1)},initOptionsScrollbar:function(){const t=this.options;("auto"===t.scrollbarType&&d.isTouchDevice()||["touch","mobile"].includes(t.scrollbarType))&&(t.scrollbarFade=!0,t.scrollbarSize=6,t.scrollbarRound=!0);const e=d.toNum(t.scrollbarSize);this.scrollbarSizeH=e,d.isNum(t.scrollbarSizeH)&&(this.scrollbarSizeH=t.scrollbarSizeH),this.scrollbarSizeV=e,d.isNum(t.scrollbarSizeV)&&(this.scrollbarSizeV=t.scrollbarSizeV)},initOptionsContainer:function(){this.$container.attr("id",this.id);const t=this.options;this.$container.removeClass();const i=[e.NS,this.id,`tg-${t.theme}`,t.className];t.textSelectable||i.push("tg-text-unselectable"),d.isTouchDevice()&&i.push("tg-touch-device"),this.$container.addClass(d.classMap(i))},setTextSelectable:function(t){this.options.textSelectable&&(t?this.$container.removeClass("tg-text-unselectable"):this.$container.addClass("tg-text-unselectable"))}},mt={initBindWindowResize:function(){this.unbindWindowResize(),this.options.bindWindowResize&&(this.windowResizeEvents={resize:{handler:t=>{this.resize()}}},d.bindEvents(this.windowResizeEvents,window))},unbindWindowResize:function(){d.unbindEvents(this.windowResizeEvents)},createResizeObserver:function(t){if("undefined"==typeof ResizeObserver)return console.error("ERROR: This browser does not support ResizeObserver"),{observe:()=>{},unobserve:()=>{},disconnect:()=>{}};return new ResizeObserver((e=>{t.call(this,e)}))},initBindContainerResize:function(){this.unbindContainerResize(),this.options.bindContainerResize&&this.holder&&(this.resizeObserver=this.createResizeObserver((t=>{Boolean(this.holder.offsetWidth||this.holder.offsetHeight||this.holder.getClientRects().length)&&this.resize()})),this.resizeObserver.observe(this.holder))},unbindContainerResize:function(){this.resizeObserver&&(this.resizeObserver.disconnect(),this.resizeObserver=null)}},bt={initRowsHandler:function(){this.rows=this.data.rows,this.rowsInfo=this.initTreeInfo(this.rows,this.frozenInfo.row)},getRows:function(){return this.rows},getViewRows:function(){return this.viewRows},createViewRows:function(){this.initRowFilterHandler();const t=[],e=this.getRowNumberFilter();let i=1;const o=(t,o)=>{if(e.call(this,t,o))return t.tg_row_number=i,void(i+=1);t.tg_row_number=""},n=(e,i,s)=>{if(!d.isList(e))return;let r,l=0;e.forEach((e=>{if(this.isInvisible(e))return;e.tg_list_index=l,l+=1,e.tg_list_last=!1,r=e,this.gridRowItemHandler(e),o(e,l),s||t.push(e);const i=s||e.tg_group&&e.collapsed;n(e.subs,e,i)})),r&&(r.tg_list_last=!0)};n(this.rows);let s,r=0;return this.initViewList(t,((t,e)=>{t.tg_top=r,r+=this.getRowHeight(t),t.tg_group_line=!1,t.collapsed&&(t.tg_group_line=!0),s&&(t.tg_group||t.tg_level<s.tg_level)&&(s.tg_group_line=!0),s=t})),this.viewRows=t,this},getRowNumberFilter:function(){const t=this.options.rowNumberFilter;return"function"==typeof t?t:function(t,e){return!t.tg_group&&!t.tg_frozen}},gridRowItemHandler:function(t){let e=t.formatter;e&&("function"!=typeof e?(e=this.getFormatter(e),e&&(t.tg_formatter=e)):t.tg_formatter=e.bind(this))},initRowFilterHandler:function(){const t=this.options.rowFilter;if("function"!=typeof t)return;if(this.forEachRow(((e,i,o)=>{if(e.tg_invisible)return;const n=!t.call(this,e,i,o);if(e.tg_filtered=n,!n){let t=e;for(;t.tg_parent;)t.tg_parent.tg_filtered=!1,t=t.tg_parent}})),this.sortColumn)return;let e=this.options.rowFilteredSort;if("function"==typeof e&&(e=e.call(this)),!e)return;"string"==typeof e&&(e={sortField:e,sortAsc:this.options.sortAsc});const i=e.sortField||e.id;i&&this.sortRows(i,e)},highlightKeywordsFilter:function(t,e,i){const{textKey:o,textGenerator:n,highlightKey:s}=this.options.highlightKeywords;if(e.forEach((e=>{t[`${s}${e}`]=null})),!i)return!0;const r=`${i}`.trim().toLowerCase().split(/\s+/g).filter((t=>t));if(!r.length)return!0;let l=!1;const h=(e,i)=>(/<\/?[a-z][\s\S]*>/i.test(e)&&(e=((e,i)=>{const n=`${o}${i}`,s=t[n];if(s)return s;const r=document.createElement("div");r.innerHTML=e;const l=r.innerText;return t[n]=l,l})(e,i)),(t=>{const e=t.toLowerCase();let i=0;for(const t of r){const o=e.indexOf(t,i);if(-1===o)return;i=o+t.length}return!0})(e));let a=function(t,e){return t[e]};return"function"==typeof n&&(a=n),e.forEach((e=>{const i=a(t,e);if(null==i)return;const o=`${i}`.trim();if(!o)return;const n=h(o,e);n&&(t[`${s}${e}`]=n,l=!0,this.highlightKeywords=r)})),l},highlightKeywordsHandler:function(){const{highlightCells:t}=this.renderSettings;if(!t.length)return;const e=this.highlightKeywords;e&&(this.asyncHighlightKeywords||(this.asyncHighlightKeywords=d.debounce(this.highlightKeywordsSync,10)),this.asyncHighlightKeywords.apply(this,[t,e]))},highlightKeywordsSync:function(t,e){t.forEach((t=>{const i=Array.from(t.querySelectorAll("svg")).concat(Array.from(t.querySelectorAll("textarea"))),o=document.createTreeWalker(t,NodeFilter.SHOW_TEXT,(t=>{if(i.length)for(const e of i)if(e.contains(t))return NodeFilter.FILTER_SKIP;return NodeFilter.FILTER_ACCEPT})),n=[];let s=o.nextNode();for(;s;)n.push(s),s=o.nextNode();n.length&&this.highlightTextNodes(n,e)}))},highlightTextNodes:function(t,e){const{highlightPre:i,highlightPost:o}=this.options.highlightKeywords;let n=0;const s=()=>(n>=e.length&&(n=0),e[n++]);let r=s();t.forEach((t=>{const e=t.textContent,n=e.toLowerCase(),l=[];let h=0;const a=e.length;let c=!1;for(;h<a;){const t=n.indexOf(r,h);if(-1===t)break;l.push(e.slice(h,t)),l.push(i),h=t+r.length,r=s(),c=!0,l.push(e.slice(t,h)),l.push(o)}if(c){h<a&&l.push(e.slice(h,a));const i=document.createElement("span");i.innerHTML=l.join(""),t.parentNode.replaceChild(i,t)}}))},setRowSubs:function(t,e){const i=this.getRowItem(t);return i?(d.isList(e)&&(i.collapsed=!1),i.subs=e,this.initRowsHandler(),this.flushRowFrom(i.tg_view_index),this.render("rows"),this):this},setRows:function(t){this.data.rows=d.toList(t),this.initRowsHandler(),this.flushBody(),this.render("rows")},getRowParentSubs:function(t){return t.tg_parent?t.tg_parent.subs:this.rows}},wt={initHandler:function(){this.reset(),this.initOptionsHandler(),this.initColumnsHandler(),this.initRowsHandler(),this.initSortOnInitHandler(),this.initSelectAllOnInitHandler(),this.initCollapseAllOnInitHandler(),this.initScrollPane(),this.bindEvents()},updateViewRowsAndSize:function(){return this.createViewRows(),this.renderCollapseAllState(),this.renderSelectAllState(),this.resizeHandler(),this},initSortOnInitHandler:function(){this.options.sortOnInit&&this.updateRowsSort()},initSelectAllOnInitHandler:function(){if(this.globalSelectedIndex=0,!this.options.selectMultiple){let t;return void this.forEachSelectableRow((e=>{if(e.selected){if(t)return void(e.selected=!1);t=e}}))}const t=this.options.selectAllOnInit;!0!==t?!1===t&&this.updateAllRowsSelected(!1):this.updateAllRowsSelected(!0)},updateAllRowsSelected:function(t){this.forEachSelectableRow((e=>{e.selected=t}))},initCollapseAllOnInitHandler:function(){const t=this.options.collapseAllOnInit;!0!==t?!1===t&&this.updateAllRowsCollapsed(!1):this.updateAllRowsCollapsed(!0)},getToBeAddedItemList:function(t){const e=[];return d.toList(t).forEach((t=>{t&&"object"==typeof t?e.push(t):void 0!==t&&e.push({name:t})})),e},getToBeAddedParentSubs:function(t,e){return t?(t.subs||(t.subs=[]),t.subs):e},getToBeAddedPositionIndex:function(t,e){const i=e.length;return d.isNum(t)&&t>=0&&t<=i?Math.round(t):i},generateDataSnapshot:function(t){if(!t||"object"!=typeof t)return t;const e=this.cleanTreeList(t.rows),i=this.cleanTreeList(t.columns);return this.convertNumberType(e,i),t.rows=e,t.columns=i,t},cleanTreeList:function(t){if(!d.isList(t))return[];const e=(t,i)=>{i.forEach((i=>{if(!i||"object"!=typeof i)return void t.push({});const o=this.getItemSnapshot(i),n=i.subs;Array.isArray(n)&&(o.subs=[],e(o.subs,n)),t.push(o)}))},i=[];return e(i,t),i},convertNumberType:function(t,e){const i=[];d.forEachTree(e,(function(t){"number"===t.type&&t.id&&i.push(t.id)})),i.length&&d.forEachTree(t,(function(t){i.forEach((function(e){t[e]=d.convertNum(t[e])}))}))}},vt={setDefaultLoading:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t)return;const i=t.style;e.size&&(i.width=e.size,i.height=e.size),e.color&&(i.color=e.color),e.size||e.color||t.removeAttribute("style"),e.fast?t.classList.add("tg-loading-fast"):t.classList.remove("tg-loading-fast")},getDefaultLoading:function(t){return this.setDefaultLoading(this.$defaultLoading,t),this.$defaultLoading},getLoadingHolder:function(){return this.$container?this.$container.find(".tg-loading"):w()},setLoading:function(t){if(!this.$container)return this;this.$defaultLoading||(this.$defaultLoading=this.$container.find(".tg-loading-default").get(0));const e=this.getLoadingHolder().get(0);return"function"==typeof t&&(t=t.call(this,e)),d.isObject(t)&&(t=this.getDefaultLoading(t)),t||(t=this.getDefaultLoading()),this.renderNodeContent(e,t),this},showLoading:function(){return this.getLoadingHolder().show(),this},hideLoading:function(){return this.getLoadingHolder().hide(),this}},Ht={showMask:function(t){if(!this.hasMask||t){const e=this.$container.find(".tg-mask"),i=e.get(0);if(t&&i){const e=d.styleMap(t);e&&(i.style.cssText=e)}e.show(),this.hasMask=!0}return this},hideMask:function(){return this.hasMask&&(this.$container.find(".tg-mask").hide(),this.hasMask=!1),this}},Ct={renderNodeContent:function(t,e){if(t){if(e&&e.nodeType)return this.emptyNode(t),void t.appendChild(e);if(Array.isArray(e))return this.emptyNode(t),void e.forEach((e=>{e&&e.nodeType&&t.appendChild(e)}));void 0===e&&(e=""),t.innerHTML=e}},emptyNode:function(t){if(t)for(;t.firstChild;)t.removeChild(t.firstChild)},removeNode:function(t){t&&t.parentNode&&t.parentNode.removeChild(t)},appendNode:function(t,e){t&&e&&t.appendChild(e)},createElement:function(t,e,i){const o=document.createElement(t);e&&Object.keys(e).forEach((function(t){const i=e[t];void 0!==i&&o.setAttribute(t,i)})),d.isArray(i)||(i=[i]);let n="";return i.forEach((function(t){t&&t.nodeType?o.appendChild(t):void 0!==t&&(n+=t)})),n&&(o.innerHTML=n),o},find:function(t,e){return w(e||this.$container).find(t)},getRowNodes:function(t){const e=this.getRowItem(t);if(e)return this.getRowNodesByIndex(e.tg_view_index)},getCellNode:function(t,e){const i=this.getRowItem(t);if(!i)return;const o=this.getColumnItem(e);return o?this.getCellNodeByIndex(i.tg_view_index,o.tg_view_index):void 0},getHeaderItemNode:function(t){const e=this.getColumnItem(t);if(e)return this.getHeaderCache(e.tg_view_index)},getColumnHeaderNode:function(t){const e=this.getHeaderItemNode(t);if(e)return e.querySelector(".tg-column-header")}},yt={render:function(){this.asyncRender||(this.asyncRender=d.microtask(this.renderSync)),this.asyncRender.apply(this,arguments)},renderSync:function(){this.renderStartedTimestamp=Date.now();const t=this.generateRenderSettings.apply(this,arguments);return this.renderSettings=t,"all"===t.type?(this.flushBody(),this.initHandler(),this.renderHeader(),this.updateViewRowsAndSize(),this.renderBody(),this):"columns"===t.type?(this.flushBody(),this.initColumnsHandler(),this.renderHeader(),this.updateViewRowsAndSize(),this.renderBody(),this):"rows"===t.type?(this.updateViewRowsAndSize(),this.renderBody(),this):"rows_cache"===t.type?(this.cssRulesInvalid=!0,this.updateViewRowsAndSize(),this.updateRowCacheTopAndHeight(),this.renderBody(),this):"resize"===t.type?(this.resizeHandler(),this.renderBody(),this):(this.renderBody(),this)},generateRenderSettings:function(t){const e={type:this.renderType,scrollLeft:null,scrollTop:null,scrollColumn:null,scrollRow:null,highlightCells:[]};return"string"==typeof t?e.type=t:t&&Object.assign(e,t),this.headerCreated||(e.type="all"),e},renderBody:function(){this.scrollOnInit(),this.scrollTopOffset=this.scrollPane.getScrollTopOffset();const t=this.getViewport();return this.viewport=t,this.flushWithViewport(),this.previousScrollTopOffset!==this.scrollTopOffset&&(this.previousScrollTopOffset=this.scrollTopOffset,this.updateRowCacheTopOffset()),this.renderRows(t.rows),this.renderCells(t.rows,t.columns),this.renderUpdatedTimestamp=Date.now(),this.renderDuration=this.renderUpdatedTimestamp-this.renderStartedTimestamp,this.trigger(y.onUpdated,t),this.firstUpdated||(this.firstUpdated=!0,this.trigger(y.onFirstUpdated,t)),this.layoutEventHandler(),this.resizeEventHandler(),this.highlightKeywordsHandler(),this.renderSettings=null,this.renderType=null,this},rerender:function(){return this.render("all"),this}},Rt={resize:function(){return this.asyncResize||(this.asyncResize=d.throttle(this.resizeSync,100)),this.asyncResize.apply(this,arguments),this},resizeSync:function(){return this.headerCreated?(this.resizeHolderHandler.apply(this,arguments),this.firstUpdated&&this.isHolderInvisible()||this.render("resize"),this):this},resizeHolderHandler(t,e){if(0!==arguments.length)return 1===arguments.length?t&&"object"==typeof t?void this.$holder.css(t):void this.$holder.css({width:t}):void this.$holder.css({width:t,height:e})},isHolderInvisible(){const t=this.$holder.width(),e=this.$holder.height();return!t||!e},resizeHandler:function(){this.containerWidth=this.$container.width(),this.containerHeight=this.$container.height(),this.headerWidth=this.containerWidth,this.bodyWidth=this.containerWidth,this.updateTotalColumnsWidth(),this.resizeHeaderHandler(),this.resizeBodyHandler()},layoutEventHandler:function(){const t=this.previousLayout||{},e={headerWidth:this.headerWidth,headerHeight:this.headerHeight,bodyWidth:this.bodyWidth,bodyHeight:this.bodyHeight,scrollbarWidth:this.getScrollbarWidth(),scrollbarHeight:this.getScrollbarHeight()};Object.values(e).join("")!==Object.values(t).join("")&&(this.previousLayout=e,this.trigger(y.onLayout,d.merge({previous:t},e)))},resizeEventHandler:function(){const t=this.previousSize||{},e={width:this.containerWidth,height:this.containerHeight};Object.values(e).join("")!==Object.values(t).join("")&&(this.previousSize=e,this.trigger(y.onResize,d.merge({previous:t},e)))},resizeHeaderHandler:function(){this.initHeaderLayerHeight();const t=this.options;t.autoHeight&&this.viewRows.length>5e3&&(t.autoHeight=!1),this.headerHeight=0,t.headerVisible&&(this.containerHeight>0||t.autoHeight)&&this.updateHeaderTableHeight(),this.$headerFrame.css({width:this.headerWidth,height:this.headerHeight})},updateHeaderTableHeight:function(){let t=0;Object.keys(this.headerLayerHeight).forEach((e=>{t+=this.headerLayerHeight[e]}));const e=this.$headerL.find(".tg-header-table"),i=this.$headerR.find(".tg-header-table");e.css({height:t}),i.css({height:t}),this.headerHeight=t},resizeBodyHandler:function(){this.updateScrollState(),this.bodyHeight=this.containerHeight-this.headerHeight,this.$bodyFrame.css({width:this.bodyWidth,height:this.bodyHeight}),this.updatePaneWidth(),this.updatePaneHeight(),this.updateCanvasWidth(),this.updateCanvasHeight(),this.updateScrollPane(),this.updateCssRules()},updatePaneWidth:function(){let t=this.bodyWidth,e=0;if(this.frozenInfo.columns){const i=this.getScrollbarWidth();this.frozenInfo.right?(e=this.columnsWidthR+i,t=this.bodyWidth-e):(t=this.columnsWidthL,e=this.bodyWidth-t),this.scrollPaneHidden&&(this.frozenInfo.right?(t<=0&&(t=0),e=Math.max(0,this.bodyWidth-t)):(e<i&&(e=i),t=Math.max(0,this.bodyWidth-e)))}this.paneWidthL=t,this.paneWidthR=e,this.$paneHL.css({left:0,width:this.paneWidthL}),this.$paneHR.css({left:this.paneWidthL,width:this.paneWidthR}),this.$paneTL.css({left:0,width:this.paneWidthL}),this.$paneTR.css({left:this.paneWidthL,width:this.paneWidthR}),this.$paneBL.css({left:0,width:this.paneWidthL}),this.$paneBR.css({left:this.paneWidthL,width:this.paneWidthR})},updatePaneHeight:function(){let t=this.bodyHeight,e=0;if(this.frozenInfo.rows)if(this.frozenInfo.bottom){const i=this.getScrollbarHeight();t=this.bodyHeight-this.frozenRowsHeight-i,e=this.frozenRowsHeight+i}else t=this.frozenRowsHeight,e=this.bodyHeight-this.frozenRowsHeight;this.paneHeightT=t,this.paneHeightB=e,this.$paneTL.css({top:0,height:this.paneHeightT}),this.$paneTR.css({top:0,height:this.paneHeightT}),this.$paneBL.css({top:this.paneHeightT,height:this.paneHeightB}),this.$paneBR.css({top:this.paneHeightT,height:this.paneHeightB})},updateCanvasWidth:function(){const t=this.columnsWidthL,e=this.columnsWidthR;this.bodyWidthChanged=!1,t===this.bodyWidthL&&e===this.bodyWidthR||(this.bodyWidthL=t,this.bodyWidthR=e,this.bodyWidthChanged=!0,this.cssRulesInvalid=!0),(this.bodyWidthChanged||this.scrollStateChanged)&&(this.updateHeaderCanvasWidth(),this.updateBodyCanvasWidth())},updateCanvasHeight:function(){let t,e;this.frozenInfo.rows?this.frozenInfo.bottom?(t=this.scrollRowsHeight,e=this.frozenRowsHeight):(t=this.frozenRowsHeight,e=this.scrollRowsHeight):(t=this.totalRowsHeight,e=0),this.bodyHeightT=t,this.bodyHeightB=e,this.updateBodyCanvasHeight()},updateHeaderCanvasWidth:function(){const t=this.maxLimitSize(this.bodyWidthL),e=this.maxLimitSize(this.bodyWidthR);this.$headerL.width(t),this.$headerR.width(e)},updateBodyCanvasWidth:function(){const t=this.maxLimitSize(this.bodyWidthL),e=this.maxLimitSize(this.bodyWidthR);this.$bodyTL.width(t),this.$bodyBL.width(t),this.$bodyTR.width(e),this.$bodyBR.width(e)},updateBodyCanvasHeight:function(){const t=this.maxLimitSize(this.bodyHeightT),e=this.maxLimitSize(this.bodyHeightB);this.$bodyTL.height(t),this.$bodyTR.height(t),this.$bodyBL.height(e),this.$bodyBR.height(e)},maxLimitSize:function(t){return t=Math.min(t,1533e3)}},St={addRow:function(t,e,i){let o=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];const n=this.getToBeAddedItemList(t);if(!n.length)return!1;let s;if(null!=e&&(s=this.getRowItem(e),!s))return!1;const r=this.getToBeAddedParentSubs(s,this.rows),l=this.getToBeAddedPositionIndex(i,r),h=[l,0].concat(n);r.splice.apply(r,h),this.initRowsHandler(),s?(s.collapsed=!1,this.flushRowFrom(s.tg_view_index+l)):this.flushRowFrom(l),this.onNextUpdated((function(){this.trigger(y.onRowAdded,n)}));const a={type:"rows"};return o&&(a.scrollRow=n[n.length-1]),this.render(a),!0},deleteRow:function(t){const e=d.toList(t),i=[];if(e.forEach((t=>{const e=this.getRowItem(t);e&&i.push(e)})),!i.length)return!1;const o=this.removeRowsHandler(i);this.initRowsHandler();const n=this.getRemovedMinIndex(o);return this.flushRowFrom(n),this.onNextUpdated((function(){this.trigger(y.onRowRemoved,i)})),this.render("rows"),!0},getRemovedMinIndex:function(t){let e=0;const i=t[t.length-1];if(this.isInvisible(i))return e;e=i.tg_view_index,e>0&&(e-=1);let o=i.tg_parent;for(;o;)o.collapsed&&(e=o.tg_view_index),o=o.tg_parent;return e},removeRowsHandler:function(t){const e=[].concat(t);e.sort((function(t,e){return e.tg_index-t.tg_index}));const i=[];return e.forEach((t=>{this.getRowParentSubs(t).splice(t.tg_sub_index,1),i.push(t)})),i}},Tt={renderCollapseAllState:function(){this.hasTreeColumn&&(this.asyncRenderCollapseAllState||(this.asyncRenderCollapseAllState=d.microtask(this.renderCollapseAllStateSync)),this.asyncRenderCollapseAllState.apply(this,arguments))},renderCollapseAllStateSync:function(){const t=this.$header.find(".tg-tree-header");this.rowsInfo.isTree?t.addClass("tg-tree-header-indent"):t.removeClass("tg-tree-header-indent"),this.renderCollapseAllIcon()},checkCollapseAllState:function(t){if(t!==this.allRowsCollapsed){if(t){let t=0;const e=this.rows.length;for(;t<e;){const e=this.rows[t];if(e.tg_group&&!e.collapsed)return;t++}}else{let t=!1;if(this.forEachRow((e=>{if(e.tg_group&&e.tg_subs_length&&e.collapsed)return t=!0,!1})),t)return}this.allRowsCollapsed=t,this.renderCollapseAllIcon()}},expandAllRows:function(){return this.renderAllRowsCollapsed(!1)},collapseAllRows:function(){return this.renderAllRowsCollapsed(!0)},toggleAllRows:function(){return this.allRowsCollapsed?this.expandAllRows():this.collapseAllRows()},renderAllRowsCollapsed:function(t){const e=this.updateAllRowsCollapsed(t);return e.length?(this.flushBody(),this.onNextUpdated((()=>{this.renderCollapseAllIcon(),t?this.trigger(y.onRowCollapsed,e):this.trigger(y.onRowExpanded,e)})),this.render("rows"),this):this},updateAllRowsCollapsed:function(t){this.allRowsCollapsed=t;const e=[];return this.forEachRow((i=>{i.subs&&i.tg_subs_length&&this.isCollapsedChanged(i,t)&&(i.collapsed=t,e.push(i))})),e},expandRow:function(t){const e=this.getRowItem(t);return e?this.isEmptyGroup(e)?(this.trigger(y.onRowSubsRequest,e),this):this.isCollapsedChanged(e,!1)?(e.collapsed=!1,this.flushRowFrom(e.tg_view_index),this.renderCollapseIcon(e),this.onNextUpdated((()=>{this.checkCollapseAllState(!1),this.trigger(y.onRowExpanded,e)})),this.render("rows"),this):this:this},collapseRow:function(t){const e=this.getRowItem(t);return e&&e.subs&&e.tg_subs_length&&this.isCollapsedChanged(e,!0)?(e.collapsed=!0,this.flushRowFrom(e.tg_view_index),this.renderCollapseIcon(e),this.onNextUpdated((()=>{this.checkCollapseAllState(!0),this.trigger(y.onRowCollapsed,e)})),this.render("rows"),this):this},toggleRow:function(t){const e=this.getRowItem(t);return e?(e.collapsed?this.expandRow(e):this.collapseRow(e),this):this},expandRowLevel:function(t){t=d.toNum(t,!0);const e=[],i=[];return this.forEachRow((o=>{o.subs&&o.tg_subs_length&&(o.tg_level<=t?this.isCollapsedChanged(o,!1)&&(o.collapsed=!1,i.push(o)):this.isCollapsedChanged(o,!0)&&(o.collapsed=!0,e.push(o)))})),e.length||i.length?(this.flushBody(),this.onNextUpdated((()=>{e.length&&this.trigger(y.onRowCollapsed,e),i.length&&this.trigger(y.onRowExpanded,i)})),this.render("rows"),this):this},renderCollapseAllIcon:function(){if(!this.options.collapseAllVisible||!this.hasTreeColumn)return;const t=this.$header.find(".tg-tree-icon-all");this.renderTreeIcon(t,this.allRowsCollapsed)},renderCollapseIcon:function(t){if(!this.headerCreated)return;const e=this.getRowNodesByIndex(t.tg_view_index);if(!e)return;const i=e.find(".tg-tree-icon");this.renderTreeIcon(i,t.collapsed)},renderTreeIcon:function(t,e){t&&(e?t.removeClass("tg-tree-icon-expanded").addClass("tg-tree-icon-collapsed"):t.removeClass("tg-tree-icon-collapsed").addClass("tg-tree-icon-expanded"))}},Et={rowDragStartHandler:function(t,e){this.removeSortColumn();const i=e.rowItem;if(!i)return;const o=this.getRowNodesByIndex(i.tg_view_index);if(!o)return;e.dragCloneNodes=this.getRowDragCloneNodes(o),e.dropPlaceholder=this.getRowDropPlaceholder(o),e.dragStartTop=this.getRowTop(i),e.dragRowHeight=this.getRowHeight(i),e.dragStartScrollTop=this.scrollTop,e.dragMaxScrollTop=this.scrollPane.getMaxScrollTop();const n={e:t,rowItem:i};this.trigger(y.onRowDragged,n),this.isDefaultPrevented(n)||("touch"===e.type&&d.preventDefault(e.e),this.setRowState(i,"dragging"),this.setTextSelectable(!1),this.rowDropListHandler(e),this.updateDragCloneRowPosition(e))},rowDragMoveHandler:function(t,e){"touch"===e.type&&d.preventDefault(e.e),this.updateDragCloneRowPosition(e),this.updateDragPlaceholderPosition(e),this.rowDragAutoScrollHandler(e)},rowDragEndHandler:function(t,e){"touch"===e.type&&(this.protectedItem=null,d.preventDefault(e.e)),this.autoScrollStop(),this.setRowState(e.rowItem,"dragging",!1),this.setTextSelectable(!0),e.dragCloneNodes&&(e.dragCloneNodes.remove(),e.dragCloneNodes=null),e.dropPlaceholder&&(e.dropPlaceholder.remove(),e.dropPlaceholder=null),e.changed&&this.rowDropHandler(e)},updateDragCloneRowPosition:function(t){const e=this.scrollTop-t.dragStartScrollTop,i=t.dragStartTop+t.offsetY+e,o=i-this.scrollTopOffset;t.dragCloneNodes&&t.dragCloneNodes.css("top",o).show(),t.dragCurrentPosition=i+.5*t.dragRowHeight},getRowDragCloneNodes:function(t){const e=w();return t.each((function(t){const i=w(t),o=i.clone();o.appendTo(i.parent()),e.add(o)})),e.addClass("tg-clone").hide(),e},getRowDropPlaceholder:function(t){const e=w();return t.each((function(t){const i=w(t),o=w("<div/>").addClass("tg-row-placeholder").hide(),n=i.parent();n.find(".tg-row-placeholder").remove(),o.appendTo(n),e.add(o)})),e},updateDragPlaceholderPosition:function(t){this.rowDropItemHandler(t);const e=t.dropItem;if(!e)return;let i=t.dropPosition-1;t.dropBottom?e.tg_view_last&&(i=t.dropPosition-2):e.tg_view_index-this.frozenInfo.rows==0&&(i=t.dropPosition);const o=i-this.scrollTopOffset;t.dropPlaceholder&&t.dropPlaceholder.css("top",o).show()},rowDragAutoScrollHandler:function(t){const e=t.dragCurrentPosition,i=this.scrollTop,o=this.bodyHeight-this.frozenRowsHeight,n=i+o,s=this.options.rowHeight,r=Math.min(3*s,.5*o);if(!(r<s))if(t.offsetY<0){if(e<i+r){const o=e-(i+r),n=this.getAutoScrollOffset(o,r);return void this.autoScrollStart(n,t)}this.autoScrollStop()}else if(e>n-r){const i=e-(n-r),o=this.getAutoScrollOffset(i,r);this.autoScrollStart(o,t)}else this.autoScrollStop()},getAutoScrollOffset:function(t,e){return Math.floor(t/e*20)},autoScrollStop:function(){this.autoScrollMotion&&(this.autoScrollMotion.destroy(),this.autoScrollMotion=null)},autoScrollStart:function(t,e){this.autoScrollStop();const i=e.dragMaxScrollTop;this.autoScrollMotion=new B,this.autoScrollMotion.bind(B.EVENT.MOTION_MOVE,(()=>{const o=d.clamp(this.scrollTop+t,0,i);o!==this.scrollTop?(this.setScrollTop(o),this.updateDragCloneRowPosition(e),this.updateDragPlaceholderPosition(e)):this.autoScrollStop()})),this.autoScrollMotion.once(B.EVENT.MOTION_END,(()=>{this.autoScrollStart(t,e)})),this.autoScrollMotion.start({duration:200})},rowDropListHandler:function(t){const e=this.getRowDropList(t);if(!d.isList(e))return;const i=t.rowItem,o=e.filter((t=>{if(t===i)return!1;if(t.tg_frozen)return!1;let e=t.tg_parent;for(;e;){if(e===i)return!1;e=e.tg_parent}return!0}));if(!d.isList(o))return;const n=[];o.forEach((t=>{const e=this.getRowTop(t),i=this.getRowHeight(t);n.push({rowItem:t,position:e}),n.push({rowItem:t,position:e+i-1,dropBottom:!0})})),t.dropList=n},getRowDropList:function(t){const e=this.options.rowDragCrossLevel;return e?"function"==typeof e?e.call(this,t):this.viewRows:this.getRowParentSubs(t.rowItem)},rowDropItemHandler:function(t){const e=t.dropList;if(!e)return;const i=t.dragCurrentPosition;let o=Number.MAX_VALUE;for(let n=0,s=e.length;n<s;n++){const s=e[n],r=Math.abs(i-s.position);if(r>o)break;o=r,t.dropItem=s.rowItem,t.dropBottom=s.dropBottom,t.dropPosition=s.position}},rowDragDropPositionHandler:function(t,e,i){const o=this.getRowParentSubs(t),n=t.tg_sub_index;let s,r;return this.isDropIntoGroupFirstChild(e,i)?(s=e.subs,r=0):(s=this.getRowParentSubs(e),r=e.tg_sub_index,o===s&&n<r&&(r-=1),i&&(r+=1)),this.updateDragDropPosition(o,s,n,r,t)},isDropIntoGroupFirstChild:function(t,e){return!!e&&(!!t.tg_group&&(this.isEmptyGroup(t)?(t.collapsed=!1,!0):!t.collapsed))},updateDragDropPosition:function(t,e,i,o,n){if(t!==e||i!==o)return t.splice(i,1),e.splice(o,0,n),{rowItem:n,dragFrom:t,dragIndex:i,dropInto:e,dropIndex:o}},rowDropHandler:function(t){const e=t.dropItem;if(!e)return;const i=t.rowItem,o=t.dropBottom,n=this.rowDragDropPositionHandler(i,e,o);if(!n)return;this.initRowsHandler();let s=Math.min(i.tg_view_index,e.tg_view_index);s&&(s-=1),this.flushRowFrom(s),this.removeSortColumn(),this.onNextUpdated((()=>{this.trigger(y.onRowDropped,n)})),this.render({type:"rows",scrollRow:i})}},It={getMoveFocusRow:function(t,e){let i=t[0];return e>0&&(i=t[t.length-1]),i},getMoveLengthInList:function(t,e){let i=0;return t.forEach((t=>{this.getRowParentSubs(t)===e&&(i+=1)})),i},getMoveInfo:function(t,e,i){const o=this.getRowParentSubs(i);let n=i.tg_sub_index+e;const s=i.tg_parent;if(s&&this.options.rowMoveCrossLevel){const e=0,i=s.tg_subs_length-1;if(n<e){const e=n+1;return this.getMoveInfo(t,e,s)}if(n>i){const e=n-i;return this.getMoveInfo(t,e,s)}}if(e>0){n-=this.getMoveLengthInList(t,o)-1}return n=d.clamp(n,0,o.length),{list:o,index:n}},moveRowsHandler:function(t,e){(t=this.removeRowsHandler(t)).reverse();const i=this.getMoveFocusRow(t,e),o=this.getMoveInfo(t,e,i),n=[o.index,0].concat(t);return o.list.splice.apply(o.list,n),this.initRowsHandler(),this.onNextUpdated((function(){this.scrollRowIntoView(i),this.trigger(y.onRowMoved,t)})),this.removeSortColumn(),this.update(),!0},moveRows:function(t,e){t=d.toList(t);const i=[];return t.forEach((t=>{const e=this.getRowItem(t);e&&i.push(e)})),!!i.length&&(!(i.length>=this.getRowsLength())&&(0!==(e=d.toNum(e,!0))&&this.moveRowsHandler(i,e)))},moveRowsUp:function(t){return this.moveRows(t,-1)},moveRowsDown:function(t){return this.moveRows(t,1)},moveRowsToTop:function(t){return this.moveRows(t,-this.getRowsLength(!0))},moveRowsToBottom:function(t){return this.moveRows(t,this.getRowsLength(!0))},moveSelectedRowsUp:function(){return this.moveRows(this.getSelectedRows(),-1)},moveSelectedRowsDown:function(){return this.moveRows(this.getSelectedRows(),1)},moveSelectedRowsToTop:function(){return this.moveRows(this.getSelectedRows(),-this.getRowsLength(!0))},moveSelectedRowsToBottom:function(){return this.moveRows(this.getSelectedRows(),this.getRowsLength(!0))}},Lt={getSelectedRow:function(){let t=null;return this.forEachSelectableRow((function(e){if(e.selected)return t=e,!1})),t},getSelectedRows:function(){const t=[];return this.forEachSelectableRow((function(e){e.selected&&t.push(e)})),t.length>1&&t.sort((function(t,e){const i=t.tg_selected_index,o=e.tg_selected_index;return i>o?1:i<o?-1:0})),t},selectAll:function(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(t=Boolean(t),this.globalSelectedIndex=0,t&&!this.options.selectMultiple)return this;const e=this.getAllSelectedChangedList(t);return e.length?(this.updateRowsSelectedState(e),this):this},setRowSelected:function(){return(this.options.selectMultiple?this.setRowMultipleSelected:this.setRowSingleSelected).apply(this,arguments)},setRowSingleSelected:function(t){const e=this.getRowItem(t);if(!e)return this;if(!this.isRowSelectable(e))return this;if(e.selected)return this;const i=[],o=this.getSelectedRow();return o&&o.selected&&i.push(o),e.selected||i.push(e),i.length?(this.updateRowsSelectedState(i),this):this},setRowMultipleSelected:function(t,e){if(0===arguments.length)return this;if(1===arguments.length&&!1===arguments[0])return this.selectAll(!1);const i=this.toRowItemList(t,(t=>this.isRowSelectable(t)));return i.length?!1===e?(this.setRowListUnselected(i),this):d.hasShiftKey(e)&&1===i.length?(this.setRowBetweenListSelected(i[0]),this):(this.updateRowsSelectedState(i),this):this},setRowListUnselected:function(t){const e=this.getSelectedChangedList(t,!1);e.length&&this.updateRowsSelectedState(e)},setRowBetweenListSelected:function(t){const e=this.previousSelectedRow;if(e&&e!==t){const i=this.getBetweenSelectedChangedList(e,t);if(!i.length)return;this.updateRowsSelectedState(i,!0)}else this.updateRowsSelectedState([t])},getAllSelectedChangedList:function(t){const e=[];return this.forEachSelectableRow((i=>{this.isSelectedChanged(i,t)&&e.push(i)})),e},getSelectedChangedList:function(t,e){const i=[];return t.forEach((t=>{this.isSelectedChanged(t,e)&&i.push(t)})),i},getBetweenSelectedChangedList:function(t,e){const i=t.tg_index,o=e.tg_index,n=[];if(i<o){let t=i+1;for(;t<=o;)n.push(t),t++}else{let t=i-1;for(;t>=o;)n.push(t),t--}return this.toRowItemList(n,(t=>this.isRowSelectable(t)&&!t.selected))},updateRowsSelectedState:function(t,e){let i;t.forEach((t=>{const e=!t.selected;t.selected=e,e&&(t.tg_selected_index=this.globalSelectedIndex++,i=t),this.renderRowSelectedState(t)})),e||(this.previousSelectedRow=i),this.renderSelectAllState(),this.onNextUpdated((()=>{this.trigger(y.onSelectChanged,t)})),this.render()},renderRowSelectedState:function(t){const e=t.tg_view_index;this.viewport.rows.includes(e)&&(this.renderRowState(t,"selected"),this.flushCell(e,this.selectColumn.tg_view_index))},renderSelectAllState:function(){this.isSelectAllVisible()&&(this.asyncRenderSelectAllState||(this.asyncRenderSelectAllState=d.microtask(this.renderSelectAllStateSync)),this.asyncRenderSelectAllState.apply(this,arguments))},renderSelectAllStateSync:function(){const t=this.getSelectAllState();if(t===this.previousSelectAllState)return;this.previousSelectAllState=t;const e=this.selectColumn,i=w(this.getColumnHeaderNode(e)).find(".tg-select-icon-all");i.length&&(i.removeClass("tg-selected tg-mixed"),t&&i.addClass(`tg-${t}`))},getSelectAllState:function(){let t=0;this.forEachSelectableRow((e=>{t+=1}));const e=this.getSelectedRows().length;let i="mixed";return 0===e?(i="",this.previousSelectedRow=null):e===t&&(i="selected"),i},isSelectAllVisible:function(){const t=this.options;return!!(t.selectVisible&&t.selectAllVisible&&t.selectMultiple)}},xt={setRowHover:function(t,e){const i=this.getRowItem(t);return i?(this.renderRowHover(i,e),this):this},renderRowHover:function(t,e){if(this.previousHover&&(this.previousHover.removeClass("tg-hover"),this.previousHover=null),!e)return this;if(!1===this.rowHoverable)return;if(!1===t.hoverable)return this;if(t.tg_frozen&&!this.options.frozenRowHoverable)return this;const i=t.tg_view_index;return this.previousHover=this.$body.find(`.tg-row[row='${i}']`).addClass("tg-hover"),this},setRowState:function(t,e){let i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const o=this.getRowItem(t);return o?(o.tg_state_names||(o.tg_state_names=new Set),o.tg_state_names.add(e),o[e]=i,this.renderRowState(o,e),this):this},renderRowState:function(t,e){const i=this.getRowNodesByIndex(t.tg_view_index);if(i){const o=`tg-${e}`;t[e]?i.addClass(o):i.removeClass(o)}}},zt={getRowItem:function(t){return d.isNum(t)?(t<0&&(t=this.rowsInfo.length+t),this.rowsInfo.indexCache[t]):t?d.isNum(t.tg_index)?t:this.getRowItemById(t.id||t):void 0},getRowItemById:function(t){return this.getRowItemBy("id",t)},getRowItemBy:function(t,e){if(void 0!==e)return this.rowsInfo.indexCache.find((i=>i[t]===e))},getRowsLength:function(t){return t?this.rowsInfo.length:this.viewRows.length},getViewRowItem:function(t){return this.viewRows[t]},getPreRenderRowInfo:function(t){const e={rows:[],rowNotFound:!1,benchmark:0};if(!t.length)return 0===this.getRowsLength()&&(e.rowNotFound=!0),e;const i=[],o=this.frozenInfo.row;return t.forEach((t=>{this.getRowCache(t)?t>o&&i.push(t):e.rows.push(t)})),i.length&&(e.benchmark=Math.min.apply(Math,i)),e.rows.sort((function(t,i){return t<e.benchmark&&i<e.benchmark?i-t:t-i})),e},rowNotFoundHandler:function(t){let e=this.options.rowNotFound;e?("function"==typeof e&&(e=e.call(this,t)),t.rowNotFound?this.showPaneMessage(e):this.hidePaneMessage()):this.hidePaneMessage()},showPaneMessage:function(t){this.$bodyFrame.addClass("tg-row-not-found");const e=this.$bodyFrame.find(".tg-body-message").show();this.renderNodeContent(e.get(0),t),this.paneMessageVisible=!0},hidePaneMessage:function(){this.paneMessageVisible&&(this.$bodyFrame.removeClass("tg-row-not-found"),this.$bodyFrame.find(".tg-body-message").hide(),this.paneMessageVisible=!1)},renderRows:function(t){const e=this.getPreRenderRowInfo(t);this.rowNotFoundHandler(e),!e.rowNotFound&&e.rows.length&&e.rows.forEach((t=>{this.renderRowNodes(t,e.benchmark)}))},createRowNode:function(t,e,i,o,n,s){const r=document.createElement("div");return r.setAttribute("row",t),r.className=i,o&&(r.style.cssText=o),r.style.top=`${n}px`,s!==this.options.rowHeight&&(r.style.height=`${s}px`,r.style.lineHeight=`${s}px`),this.setNodeDataCache(r,{row:t,rowItem:e,rowNode:r}),r},appendRowNode:function(t,e,i,o){i<o?t.prepend(e):t.append(e)},renderRowNodes:function(t,e){const i=this.getViewRowItem(t);if(!i)return;let o=w();const n=this.getRowVPos(t),s=this.getRowClass(i),r=d.styleMap(i.styleMap),l=this.getViewRowTop(i),h=this.getRowHeight(i),a=this.createRowNode(t,i,s,r,l,h),c=this.getRowCanvas(n,"left");if(this.appendRowNode(c,a,t,e),o=o.add(a),this.frozenInfo.columns){const a=this.createRowNode(t,i,s,r,l,h),c=this.getRowCanvas(n,"right");this.appendRowNode(c,a,t,e),o=o.add(a)}this.setRowCache(t,o)},isGroupLine:function(t){return t&&t.tg_group_line&&t.tg_view_index!==this.frozenInfo.row},isNoneLine:function(t){const e=this.frozenInfo.bottom;return!(t!==this.viewRows.length-1||!this.hasVScroll||!e&&this.hasHScroll)||!(t!==this.frozenInfo.row||!e||this.hasHScroll)},isTopLine:function(t){return!(0!==t||!this.frozenInfo.bottom)},getRowClass:function(t){const e=t.tg_view_index,i=["tg-row"],o=e%2==1;i.push({"tg-odd":o,"tg-even":!o,"tg-list-first":0===t.tg_list_index,"tg-list-last":t.tg_list_last,"tg-group-line":this.isGroupLine(t),"tg-none-line":this.isNoneLine(e),"tg-top-line":this.isTopLine(e),"tg-group":t.tg_group,"tg-selected":t.selected}),t.type&&i.push(`tg-${t.type}`);const n=t.tg_state_names;return n&&n.forEach((function(e){t[e]&&i.push(`tg-${e}`)})),i.push(d.classMap(t.classMap)),d.classMap(i)},getRowHeight:function(t){return t&&d.isNum(t.tg_height)?t.tg_height:this.options.rowHeight},getRowsHeight:function(){let t=0;const e=this.getRowsLength();for(let i=0;i<e;i++)t+=this.getRowHeight(this.viewRows[i]);return t},getFrozenRowsHeight:function(){let t=0;const e=this.frozenInfo.rows;for(let i=0;i<e;i++)t+=this.getRowHeight(this.viewRows[i]);return t},getViewRowTop:function(t){let e=t.tg_top;const i=t.tg_view_index;if(this.frozenInfo.rows){if(!(i>this.frozenInfo.row))return e;e-=this.frozenRowsHeight}return e-=this.scrollTopOffset,e},getRowTop:function(t){let e=t.tg_top;return t.tg_frozen||(e-=this.frozenRowsHeight),e},getRowVPos:function(t){const e=this.frozenInfo.bottom,i=this.frozenInfo.row;let o="top";return this.frozenInfo.rows&&(t<=i?e&&(o="bottom"):e||(o="bottom")),o},getRowCanvas:function(t,e){return"top"===t?"left"===e?this.$bodyTL:this.$bodyTR:"left"===e?this.$bodyBL:this.$bodyBR}},Mt={CHANGE:"change",START:"start",END:"end"},Pt={h:{type:"h",className:"tg-scrollbar-h",offset:"left",size:"width",page:"pageX",axis:"x",offsetName:"offsetX"},v:{type:"v",className:"tg-scrollbar-v",offset:"top",size:"height",page:"pageY",axis:"y",offsetName:"offsetY"}};class Nt extends k{static EVENT=Mt;static H="h";static V="v";type="h";settings={};size=0;viewSize=0;bodySize=0;trackSize=0;position=0;scale=0;thumbPosition=0;thumbScale=0;constructor(t,e){super(),this.settings=Pt[t]||Pt.h,this.type=this.settings.type,this.id=d.uid(4,`tg-scrollbar-${this.type}-`),this.$holder=w(e),this.$holder.find(`.${this.settings.className}`).remove(),this.options=this.generateOptions()}generateOptions(t){return d.merge({size:15,round:!1,blank:!1,motionDuration:200},t)}updateOptions(t){this.options=this.generateOptions(t);let e=this.options.size;d.isNum(e)||(e=d.toNum(e)),e=Math.round(e),e=Math.max(e,0),e=Math.min(e,30),this.size=e}create(){this.$container=w('<div><div class="tg-scrollbar-track"></div><div class="tg-scrollbar-thumb"></div></div>').appendTo(this.$holder),this.$container.attr("id",this.id),this.$container.addClass(d.classMap(["tg-scrollbar",this.settings.className,{"tg-scrollbar-round":this.options.round}])),this.$track=this.$container.find(".tg-scrollbar-track"),this.$thumb=this.$container.find(".tg-scrollbar-thumb"),this.thumbDrag=new O,this.thumbDrag.bind(O.EVENT.DRAG_START,((t,e)=>{this.thumbDragStart(e)})).bind(O.EVENT.DRAG_MOVE,((t,e)=>{this.thumbDragMove(e)})).bind(O.EVENT.DRAG_END,((t,e)=>{this.thumbDragEnd(e)}));const t=this.$container.get(0);return this.scrollEvents={mousedown:{handler:e=>{e.target.classList.contains("tg-scrollbar-thumb")?this.thumbMouseDownHandler(e):(this.trackEvents={mouseup:{handler:t=>{this.trackMouseupHandler(t)},options:{once:!0}}},d.bindEvents(this.trackEvents,t),this.trackMousedownHandler(e))},options:!0},selectstart:{handler:t=>{d.preventDefault(t)},options:!0}},d.bindEvents(this.scrollEvents,t),this}getBlank(){return this.options.blank}getSize(){return this.size}getViewSize(){return this.viewSize}getBodySize(){return this.bodySize}getTrackMouseDirection(){let t=1;return this.trackMousePosition<this.thumbPosition&&(t=-1),t}getTrackMousePos(t){const e=this.$track.offset();return t[this.settings.page]-e[this.settings.offset]}getMaxThumbPosition(){return this.trackSize-this.thumbSize}setThumbPosition(t){return t===this.thumbPosition||(this.thumbPosition=t,this.$thumb&&this.$thumb.css(this.settings.offset,t)),this}updateThumbPosition(){let t=0;const e=this.getMaxPosition();if(e>0){const i=this.getMaxThumbPosition();t=Math.round(i*this.position/e),t=d.clamp(t,0,i)}return this.setThumbPosition(t),this}trackMousedownHandler(t){return this.motionStop(),this.trackMousePosition=this.getTrackMousePos(t),this.motionStart(),this}trackMouseupHandler(t){return d.unbindEvents(this.trackEvents),this.motionStop(),this.motionStarted||(this.trackMousePosition=this.getTrackMousePos(t),this.trackScrollHandler(),this.triggerEvent()),this}trackScrollHandler(){const t=Math.max(0,this.viewSize-20)*this.getTrackMouseDirection();return this.setOffset(t),this}motionStop(){return this.motion&&(this.motion.destroy(),this.motion=null),this}motionStart(){const t=this.position,e=Math.round(this.trackMousePosition/this.viewSize*this.getMaxPosition());return this.motionStarted=!1,this.motion=new B,this.motion.bind(B.EVENT.MOTION_START,((t,e)=>{this.motionStarted=!0})),this.motion.bind(B.EVENT.MOTION_MOVE,((t,e)=>{this.motionUpdateHandler(t,e)})),this.motion.start({duration:this.options.motionDuration,from:t,till:e}),this}motionUpdateHandler(t,e){e!==this.position&&(this.setPosition(e),this.triggerEvent())}thumbMouseDownHandler(t){this.$thumb.addClass("tg-scrollbar-thumb-hold"),this.thumbDrag.start(t,{target:this.$thumb})}thumbDragStart(t){this.motionStop(),t.thumbPositionStart=this.thumbPosition,this.trigger(Mt.START)}thumbDragMove(t){let e=t.thumbPositionStart+t[this.settings.offsetName];const i=this.getMaxThumbPosition();e=d.clamp(e,0,i),this.setThumbPosition(e);let o=0;i>0&&(o=d.per(e/i)*this.getMaxPosition(),o=Math.round(o)),this.position=o,this.triggerEvent()}thumbDragEnd(t){this.$thumb&&this.$thumb.removeClass("tg-scrollbar-thumb-hold"),this.trigger(Mt.END)}triggerEvent(){this.trigger(Mt.CHANGE,this.position)}getPosition(){return this.position}setPosition(t){t=d.toNum(t,!0);const e=this.getMaxPosition();t=d.clamp(t,0,e),this.position=t,this.updateThumbPosition()}getMaxPosition(){return this.bodySize-this.viewSize}updatePosition(){const t=this.getMaxPosition(),e=d.clamp(this.position,0,t);this.position=e}setOffset(t){t=d.toNum(t);const e=this.position+t;return this.setPosition(e),this}getScale(){return this.scale}setScale(t){return t=d.per(t),this.scale=t,this.scaleChangeHandler(),this}scaleChangeHandler(){let t=Math.round(this.viewSize*this.scale);if(t=Math.max(t,Math.round(1.5*this.options.size)),t=Math.min(t,this.viewSize-1),this.thumbSize=t,this.$thumb){const t={};"h"===this.type?(t.height=this.size,t.width=this.thumbSize):(t.width=this.size,t.height=this.thumbSize),this.$thumb.css(t)}}updateTrackSize(){const t={};return"h"===this.type?(t.width=this.trackSize,t.height=this.size):(t.height=this.trackSize,t.width=this.size),this.$container.css(t),this}updateThumbSize(){let t=0;return this.bodySize&&(t=this.trackSize/this.bodySize),this.setScale(t),this}parseSize(t){return t=d.toNum(t),t=Math.round(t),t=Math.max(t,0)}updateSize(t,e,i){t=this.parseSize(t),this.viewSize=t,e=this.parseSize(e),this.bodySize=e,i=d.isNum(i)?this.parseSize(i):t,this.trackSize=i,this.previousFadeIn=null}fade(t){return!(!this.$container||!this.size)&&(this.previousFadeIn!==t&&(this.previousFadeIn=t,t?this.$container.hasClass("tg-fade-out")&&this.$container.removeClass("tg-fade-out").addClass("tg-fade-in"):this.$container.removeClass("tg-fade-in").addClass("tg-fade-out"),!0))}show(){if(this.updatePosition(),!this.getBlank())return!this.$container&&this.size>0&&this.create(),this.$container?(this.updateTrackSize(),this.updateThumbSize(),this):this;this.remove()}hide(){return this.updatePosition(),this.remove(),this}remove(){if(this.motionStop(),d.unbindEvents(this.scrollEvents),d.unbindEvents(this.trackEvents),this.thumbDrag&&(this.thumbDrag.destroy(),this.thumbDrag=null),!this.$container)return this;this.$thumb=null,this.$track=null,this.$container.remove(),this.$container=null}destroy(){return this.remove(),this}}const _t={CHANGE:"change",START:"start",END:"end"};class kt extends k{static EVENT=_t;visible=!0;constructor(t,e){super(),this.id=d.uid(4,`tg-scroll-pane-${e}-`),this.gradientInfo=[],this.$container=w(t).attr("id",this.id),this.$container.addClass("tg-scroll-pane"),this.$scrollView=this.$container.find(".tg-scroll-view"),this.$scrollBody=this.$scrollView.find(".tg-scroll-body"),this.scrollbarH=new Nt(Nt.H,this.$container),this.scrollbarH.bind(Nt.EVENT.CHANGE,((t,e)=>{this.scrollHChangeHandler()})).bind(Nt.EVENT.START,(t=>{this.scrollStartEndHandler(!0)})).bind(Nt.EVENT.END,(t=>{this.scrollStartEndHandler()})),this.scrollbarV=new Nt(Nt.V,this.$container),this.scrollbarV.bind(Nt.EVENT.CHANGE,((t,e)=>{this.scrollVChangeHandler()})).bind(Nt.EVENT.START,(t=>{this.scrollStartEndHandler(!0)})).bind(Nt.EVENT.END,(t=>{this.scrollStartEndHandler()})),this.options=this.generateOptions()}generateOptions(t){return d.merge({scrollbarH:{},scrollbarV:{},scrollbarFade:!1,scrollSizeOnKeyPress:20,gradient:30},t)}scrollStartEndHandler(t){t?this.trigger(_t.START):this.trigger(_t.END)}show(){return this.$container.show(),this.visible=!0,this}hide(){return this.$container.hide(),this.visible=!1,this}width(){return this.scrollPaneW}height(){return this.scrollPaneH}render(t){return this.visible?(this.options=this.generateOptions(t),this.update(),this):this}update(){this.scrollPaneW=this.options.scrollPaneW,this.scrollPaneH=this.options.scrollPaneH,this.scrollBodyW=this.options.scrollBodyW,this.scrollBodyH=this.options.scrollBodyH,this.updateScrollbar()}setGroupH(t){this.groupH=d.toList(t)}setGroupV(t){this.groupV=d.toList(t)}updateGroupH(){if(!d.isList(this.groupH))return this;const t=this.scrollbarH.getPosition();return this.groupH.forEach((function(e){e&&e.updateScrollHFromGroup(t)})),this}updateGroupV(){if(!d.isList(this.groupV))return this;const t=this.scrollbarV.getPosition();return this.groupV.forEach((function(e){e&&e.updateScrollVFromGroup(t)})),this}updateGroupList(){this.updateGroupH(),this.updateGroupV()}updateScrollHFromGroup(t){this.scrollbarH.getPosition()!==t&&(this.scrollbarH.setPosition(t),this.updateScrollLeft(),this.triggerEvent())}updateScrollVFromGroup(t){this.scrollbarV.getPosition()!==t&&(this.scrollbarV.setPosition(t),this.updateScrollTop(),this.triggerEvent())}setPosition(t,e){return this.scrollbarH.setPosition(t),this.scrollbarV.setPosition(e),this.updateScrollLeft(),this.updateScrollTop(),this.updateGroupList(),this}updateScrollbar(){this.scrollbarH.updateOptions(this.options.scrollbarH),this.scrollbarV.updateOptions(this.options.scrollbarV),this.updateScrollState(),this.updateScrollView(),this.updateScrollTrack(),this.scrollbarH.updateSize(this.scrollViewW,this.scrollBodyW,this.scrollTrackW),this.scrollbarV.updateSize(this.scrollViewH,this.scrollBodyH,this.scrollTrackH),this.hasScrollH?(this.scrollbarH.show(),this.scrollbarH.setPosition(this.scrollbarH.getPosition())):this.scrollbarH.hide(),this.hasScrollV?(this.scrollbarV.show(),this.scrollbarV.setPosition(this.scrollbarV.getPosition())):this.scrollbarV.hide(),this.updateScrollLeft(),this.updateScrollTop(),this.updateGroupList()}updateScrollState(){const t=this.scrollbarH.getSize(),e=this.scrollbarV.getSize(),i=this.scrollbarH.getBlank(),o=this.scrollbarV.getBlank(),n=this.options.scrollbarFade;let s=!1,r=0;(function(){(this.scrollPaneW<this.scrollBodyW||i)&&(s=!0,r=t,n&&(r=0))}).call(this);let l=!1,h=0;(function(){(this.scrollPaneH<this.scrollBodyH+r||o)&&(l=!0,h=e,n&&(h=0),!s&&this.scrollPaneW<this.scrollBodyW+h&&(s=!0,r=t,n&&(r=0)))}).call(this),this.hasScrollH=s,this.hasScrollV=l,this.scrollSizeH=r,this.scrollSizeV=h}updateScrollView(){this.scrollViewW=this.scrollPaneW,this.hasScrollV&&(this.scrollViewW=this.scrollPaneW-this.scrollSizeV),this.scrollViewH=this.scrollPaneH,this.hasScrollH&&(this.scrollViewH=this.scrollPaneH-this.scrollSizeH);const t=this.scrollbarH.getBlank(),e=this.scrollbarV.getBlank();let i=this.scrollViewW;e&&!0!==e&&(i=this.scrollPaneW);let o=this.scrollViewH;return t&&!0!==t&&(o=this.scrollPaneH),this.$scrollView.css({width:`${i}px`,height:`${o}px`}),this}updateScrollTrack(){this.scrollTrackW=this.scrollViewW,this.scrollTrackH=this.scrollViewH,this.options.scrollbarFade&&this.hasScrollH&&this.hasScrollV&&(this.scrollTrackW-=this.scrollbarV.getSize(),this.scrollTrackH-=this.scrollbarH.getSize())}fade(t){const e=this.scrollbarH.fade(t),i=this.scrollbarV.fade(t);return e||i}hasScrollbar(){return!!this.visible&&(!(!this.hasScrollH&&!this.hasScrollV)&&!(!this.scrollbarV.getSize()&&!this.scrollbarH.getSize()))}updateScrollLeft(){const t=this.getScrollLeft();return this.$scrollBody.css("left",-t+"px"),this.updateGradient(),this}updateScrollTop(){const t=this.getScrollTop()-this.getScrollTopOffset();return this.$scrollBody.css("top",-t+"px"),this.updateGradient(),this}updateGradient(){const t=this.options;if(!t.scrollbarFade)return;t.gradient&&(this.asyncUpdateGradient||(this.asyncUpdateGradient=d.microtask(this.updateGradientSync)),this.asyncUpdateGradient.apply(this,arguments))}updateGradientSync(){const t=[],e=this.options.gradient,i=this.getScrollLeft(),o=this.getScrollTop(),n=this.getMaxScrollLeft(),s=this.getMaxScrollTop();this.hasScrollH&&(i>e&&t.push("left"),i<n-e&&t.push("right")),this.hasScrollV&&(o>e&&t.push("top"),o<s-e&&t.push("bottom")),t.join("")!==this.gradientInfo.join("")&&(this.gradientInfo=t,["left","right","top","bottom"].forEach((e=>{const i=`tg-gradient-${e}`;t.includes(e)?this.$container.addClass(i):this.$container.removeClass(i)})))}getScrollLeft(){return this.scrollbarH.getPosition()}getScrollTop(){return this.scrollbarV.getPosition()}getMaxScrollLeft(){return this.scrollbarH.getMaxPosition()}getMaxScrollTop(){return this.scrollbarV.getMaxPosition()}getScrollTopOffset(){const t=this.getScrollTop();return t-t%1e4}triggerEvent(){this.trigger(_t.CHANGE,{scrollLeft:this.getScrollLeft(),scrollTop:this.getScrollTop()})}scrollHChangeHandler(){this.updateScrollLeft(),this.updateGroupList(),this.triggerEvent()}scrollVChangeHandler(){this.updateScrollTop(),this.updateGroupList(),this.triggerEvent()}setOffsetH(t){const e=this.getScrollLeft();this.scrollbarH.setOffset(t);return this.getScrollLeft()!==e&&(this.updateScrollLeft(),this.updateGroupList(),this.triggerEvent(),!0)}setOffsetV(t){const e=this.getScrollTop();this.scrollbarV.setOffset(t);return this.getScrollTop()!==e&&(this.updateScrollTop(),this.updateGroupList(),this.triggerEvent(),!0)}mouseWheelHandler(t){const e=t.deltaX,i=t.deltaY,o=Math.abs(e);if(o>Math.abs(i)){if(this.hasScrollH)return this.setOffsetH(e)}else{if(this.hasScrollV)return this.setOffsetV(i);if(this.hasScrollH&&!o)return this.setOffsetH(i)}return!1}keyPageUpHandler(t){return this.setOffsetV(-this.scrollViewH)}keyPageDownHandler(t){return this.setOffsetV(this.scrollViewH)}keyEndHandler(t){return this.setOffsetV(this.scrollBodyH)}keyHomeHandler(t){return this.setOffsetV(-this.scrollBodyH)}keyLeftHandler(t){return this.setOffsetH(-this.options.scrollSizeOnKeyPress)}keyUpHandler(t){return this.setOffsetV(-this.options.scrollSizeOnKeyPress)}keyRightHandler(t){return this.setOffsetH(this.options.scrollSizeOnKeyPress)}keyDownHandler(t){return this.setOffsetV(this.options.scrollSizeOnKeyPress)}destroy(){return this.visible=!1,this.groupH=null,this.groupV=null,this.scrollbarV&&(this.scrollbarV.destroy(),this.scrollbarV=null),this.scrollbarH&&(this.scrollbarH.destroy(),this.scrollbarH=null),this.$container=null,this.$scrollView=null,this.$scrollBody=null,this}}const Vt={initScrollPane:function(){this.initFrozenStyle(),this.createScrollPane()},initFrozenStyle:function(){const t={HL:{container:this.$paneHL,cls:[]},HR:{container:this.$paneHR,cls:[]},TL:{container:this.$paneTL,cls:[]},TR:{container:this.$paneTR,cls:[]},BL:{container:this.$paneBL,cls:[]},BR:{container:this.$paneBR,cls:[]}},e="tg-frozen-h";this.frozenInfo.rows&&(this.frozenInfo.bottom?(t.BL.cls.push(e),t.BR.cls.push(e)):(t.TL.cls.push(e),t.TR.cls.push(e)));const i="tg-frozen-v",o="tg-frozen-line-v";this.frozenInfo.columns&&(this.frozenInfo.right?(t.HR.cls.push(i),t.TR.cls.push(i),t.BR.cls.push(i)):(t.HL.cls.push(i),t.TL.cls.push(i),t.BL.cls.push(i)),t.HL.cls.push(o),t.TL.cls.push(o),t.BL.cls.push(o));const n="tg-frozen",s=[n,e,i,o].join(" ");Object.keys(t).forEach((function(e){const i=t[e],o=i.container;o.removeClass(s);const r=i.cls;if(!r.length)return;const l=[n].concat(r).join(" ");o.addClass(l)}))},createScrollPane:function(){this.removeScrollPane(),this.scrollPaneMap={HL:new kt(this.$paneHL,"header-left"),HR:new kt(this.$paneHR,"header-right"),TL:new kt(this.$paneTL,"top-left"),TR:new kt(this.$paneTR,"top-right"),BL:new kt(this.$paneBL,"bottom-left"),BR:new kt(this.$paneBR,"bottom-right")},this.scrollPaneMap.BR.setGroupH([this.scrollPaneMap.HR,this.scrollPaneMap.TR]),this.scrollPaneMap.TR.setGroupH([this.scrollPaneMap.HR,this.scrollPaneMap.BR]),this.scrollPaneMap.BL.setGroupH([this.scrollPaneMap.HL,this.scrollPaneMap.TL]),this.scrollPaneMap.TL.setGroupH([this.scrollPaneMap.HL,this.scrollPaneMap.BL]),this.scrollPaneMap.BR.setGroupV(this.scrollPaneMap.BL),this.scrollPaneMap.BL.setGroupV(this.scrollPaneMap.BR),this.scrollPaneMap.TR.setGroupV(this.scrollPaneMap.TL),this.scrollPaneMap.TL.setGroupV(this.scrollPaneMap.TR),this.initActiveScrollPane(),this.initPaneVisibility()},initActiveScrollPane:function(){const t=this.getScrollPaneVP(),e=this.getScrollPaneHP(),i=`${t}${e}`;this.scrollPane=this.scrollPaneMap[i],this.scrollPane.bind(kt.EVENT.CHANGE,((t,e)=>{this.scrollPaneChangeHandler(t,e)})).bind(kt.EVENT.START,(t=>{this.rowHoverable=!1})).bind(kt.EVENT.END,(t=>{this.rowHoverable=!0}));let o={L:"L",R:"L"};this.frozenInfo.columns&&this.frozenInfo.right&&(o={L:"R",R:"L"});const n=`${t}${o[e]}`;this.scrollPaneFrozen=this.scrollPaneMap[n]},getScrollPaneVP:function(){return this.frozenInfo.rows&&!this.frozenInfo.bottom?"B":"T"},getScrollPaneHP:function(){return this.frozenInfo.columns&&!this.frozenInfo.right?"R":"L"},initPaneVisibility:function(){this.scrollPaneMap.HL.show(),this.scrollPaneMap.TL.show(),this.frozenInfo.columns?(this.scrollPaneMap.HR.show(),this.scrollPaneMap.TR.show(),this.frozenInfo.rows?(this.scrollPaneMap.BL.show(),this.scrollPaneMap.BR.show()):(this.scrollPaneMap.BL.hide(),this.scrollPaneMap.BR.hide())):(this.scrollPaneMap.HR.hide(),this.scrollPaneMap.TR.hide(),this.scrollPaneMap.BR.hide(),this.frozenInfo.rows?this.scrollPaneMap.BL.show():this.scrollPaneMap.BL.hide())},scrollPaneChangeHandler:function(t,e){this.hideColumnLine(),this.scrollLeft=e.scrollLeft,this.scrollTop=e.scrollTop,this.scrollRenderHandler()},scrollbarFadeInOutHandler:function(t,e){this.options.scrollbarFade&&(e?this.updateScrollPaneFade(!0):this.options.scrollbarFadeTimeout||this.updateScrollPaneFade(!1))},updateScrollPaneFade:function(t){if(!this.options.scrollbarFade)return;this.updateScrollPaneFadeSync(t);const e=this.options.scrollbarFadeTimeout;e&&(clearTimeout(this.timeout_fade),this.timeout_fade=setTimeout((()=>{this.updateScrollPaneFadeSync(!1)}),e))},updateScrollPaneFadeSync:function(t){if(this.previousScrollbarFadeIn===t)return;this.previousScrollbarFadeIn=t;const e=[];Object.keys(this.scrollPaneMap).forEach((t=>{const i=this.scrollPaneMap[t];i.hasScrollbar()&&e.push(i)})),e.length&&e.forEach((function(e){e.fade(t)}))},updateScrollPane:function(){const t=this.getScrollbarOptions();this.scrollPaneMap.HL.render(this.getScrollPaneOptions({scrollPaneW:this.paneWidthL,scrollPaneH:this.headerHeight,scrollBodyW:this.bodyWidthL,scrollBodyH:this.headerHeight,scrollbarV:t.HLV,scrollbarH:t.HLH})),this.scrollPaneMap.HR.render(this.getScrollPaneOptions({scrollPaneW:this.paneWidthR,scrollPaneH:this.headerHeight,scrollBodyW:this.bodyWidthR,scrollBodyH:this.headerHeight,scrollbarV:t.HRV,scrollbarH:t.HRH})),this.scrollPaneMap.TL.render(this.getScrollPaneOptions({scrollPaneW:this.paneWidthL,scrollPaneH:this.paneHeightT,scrollBodyW:this.bodyWidthL,scrollBodyH:this.bodyHeightT,scrollbarV:t.TLV,scrollbarH:t.TLH})),this.scrollPaneMap.TR.render(this.getScrollPaneOptions({scrollPaneW:this.paneWidthR,scrollPaneH:this.paneHeightT,scrollBodyW:this.bodyWidthR,scrollBodyH:this.bodyHeightT,scrollbarV:t.TRV,scrollbarH:t.TRH})),this.scrollPaneMap.BL.render(this.getScrollPaneOptions({scrollPaneW:this.paneWidthL,scrollPaneH:this.paneHeightB,scrollBodyW:this.bodyWidthL,scrollBodyH:this.bodyHeightB,scrollbarV:t.BLV,scrollbarH:t.BLH})),this.scrollPaneMap.BR.render(this.getScrollPaneOptions({scrollPaneW:this.paneWidthR,scrollPaneH:this.paneHeightB,scrollBodyW:this.bodyWidthR,scrollBodyH:this.bodyHeightB,scrollbarV:t.BRV,scrollbarH:t.BRH})),this.scrollLeft=this.getScrollLeft(),this.scrollTop=this.getScrollTop(),this.updateScrollPaneFade(Boolean(this.options.scrollbarFadeTimeout))},getScrollPaneOptions:function(t){const e=this.options;return t.scrollbarFade=e.scrollbarFade,t.gradient=d.clamp(d.toNum(e.scrollPaneGradient,!0),0,100),t},getScrollbarOptions:function(){const t=this.options.scrollbarRound,e={};return["HLH","HLV","HRH","HRV","TLH","TLV","TRH","TRV","BLH","BLV","BRH","BRV"].forEach((function(i){e[i]={size:0,round:t,blank:!1}})),this.scrollbarOptionsHandler(e),this.scrollbarFadeHandler(e),e},scrollbarOptionsHandler:function(t){const e=this.scrollbarSizeH,i=this.scrollbarSizeV;this.scrollbarHeaderHandler(t,e,i),this.frozenInfo.columns?this.frozenInfo.rows?this.scrollbarC1R1Handler(t,e,i):this.scrollbarC1R0Handler(t,e,i):this.frozenInfo.rows?this.scrollbarC0R1Handler(t,e,i):this.scrollbarC0R0Handler(t,e,i)},scrollbarFadeHandler:function(t){if(this.options.scrollbarFade)for(const e in t)if(d.hasOwn(t,e)){const i=t[e];i.size>0&&i.blank&&(i.blank=!1,i.size=0)}},scrollbarHeaderHandler:function(t,e,i){this.hasVScroll&&(this.frozenInfo.columns?(t.HRV.size=i,t.HRV.blank=1):(t.HLV.size=i,t.HLV.blank=1))},scrollbarC0R0Handler:function(t,e,i){t.TLH.size=e,t.TLV.size=i},scrollbarC0R1Handler:function(t,e,i){this.frozenInfo.bottom?this.scrollbarC0R1B1Handler(t,e,i):this.scrollbarC0R1B0Handler(t,e,i)},scrollbarC0R1B1Handler:function(t,e,i){t.BLH.size=e,t.TLV.size=i,this.hasVScroll&&(t.BLV.size=i,t.BLV.blank=1)},scrollbarC0R1B0Handler:function(t,e,i){t.BLH.size=e,t.BLV.size=i,this.hasVScroll&&(t.TLV.size=i,t.TLV.blank=1)},scrollbarC1R0Handler:function(t,e,i){this.frozenInfo.right?this.scrollbarC1R0R1Handler(t,e,i):this.scrollbarC1R0R0Handler(t,e,i)},scrollbarC1R0R1Handler:function(t,e,i){this.hasHScroll&&(t.TLH.size=e,this.scrollPaneHidden?(t.TRH.size=e,t.TLH.blank=!0):(t.TRH.size=e,t.TRH.blank=!0)),t.TRV.size=i},scrollbarC1R0R0Handler:function(t,e,i){this.hasHScroll&&(t.TRH.size=e,this.scrollPaneHidden?(t.TLH.size=e,t.TRH.blank=!0):(t.TLH.size=e,t.TLH.blank=!0)),t.TRV.size=i},scrollbarC1R1Handler:function(t,e,i){this.frozenInfo.right?this.frozenInfo.bottom?this.scrollbarC1R1R1B1Handler(t,e,i):this.scrollbarC1R1R1B0Handler(t,e,i):this.frozenInfo.bottom?this.scrollbarC1R1R0B1Handler(t,e,i):this.scrollbarC1R1R0B0Handler(t,e,i)},scrollbarC1R1R1B1Handler:function(t,e,i){this.hasHScroll&&(t.BLH.size=e,this.scrollPaneHidden&&(t.BRH.size=e,t.BLH.blank=!0)),t.TRV.size=i,this.hasVScroll&&(t.BRV.size=i,t.BRV.blank=1)},scrollbarC1R1R1B0Handler:function(t,e,i){this.hasHScroll&&(t.BLH.size=e,this.scrollPaneHidden?(t.BRH.size=e,t.BLH.blank=!0):(t.BRH.size=e,t.BRH.blank=!0)),t.BRV.size=i,this.hasVScroll&&(t.TRV.size=i,t.TRV.blank=1)},scrollbarC1R1R0B1Handler:function(t,e,i){this.hasHScroll&&(t.BRH.size=e,this.scrollPaneHidden&&(t.BLH.size=e,t.BRH.blank=!0)),t.TRV.size=i,this.hasVScroll&&(t.BRV.size=i,t.BRV.blank=1)},scrollbarC1R1R0B0Handler:function(t,e,i){this.hasHScroll&&(t.BRH.size=e,this.scrollPaneHidden?(t.BLH.size=e,t.BRH.blank=!0):(t.BLH.size=e,t.BLH.blank=!0)),t.BRV.size=i,this.hasVScroll&&(t.TRV.size=i,t.TRV.blank=1)},removeScrollPane:function(){clearTimeout(this.timeout_fade),this.previousScrollbarFadeIn=null,this.scrollPaneMap&&(Object.keys(this.scrollPaneMap).forEach((t=>{const e=this.scrollPaneMap[t];e&&e.destroy()})),this.scrollPaneMap=null,this.scrollPane=null,this.scrollPaneFrozen=null)}},Ot={updateScrollState:function(){this.updateGlobalScrollInfo(),this.updateHScrollState(),this.updateVScrollState(),this.updateBlankColumnWidth(),this.scrollStateChanged=!1,this.previousHasHScroll===this.hasHScroll&&this.previousHasVScroll===this.hasVScroll||(this.scrollStateChanged=!0,this.previousHasHScroll=this.hasHScroll,this.previousHasVScroll=this.hasVScroll,this.trigger(y.onScrollStateChanged,{hasHScroll:this.hasHScroll,hasVScroll:this.hasVScroll}))},updateGlobalScrollInfo:function(){this.totalRowsLength=this.getRowsLength(),this.totalRowsHeight=this.getRowsHeight(),this.frozenRowsHeight=this.getFrozenRowsHeight(),this.scrollRowsHeight=this.totalRowsHeight-this.frozenRowsHeight,this.totalRowsHeight=Math.max(this.totalRowsHeight,1),this.scrollRowsHeight=Math.max(this.scrollRowsHeight,1),this.flushRowFrom(this.totalRowsLength)},updateHScrollState:function(){if(this.hasHScroll=!0,this.updateScrollPaneHiddenState(),this.updateHScrollByScrollPaneHidden(),this.scrollPaneHidden)return;this.containerWidth-this.columnsWidth>=0&&(this.hasHScroll=!1)},getScrollPaneCurrentWidth:function(){return this.frozenInfo.right?this.bodyWidth-this.columnsWidthR:this.bodyWidth-this.columnsWidthL},updateHScrollByScrollPaneHidden:function(){if(this.scrollPaneHidden){this.hasHScroll=!1;this.getScrollPaneCurrentWidth()<this.getScrollbarWidth()&&(this.hasHScroll=!0)}},updateScrollPaneHiddenState:function(){if(this.scrollPaneHidden=!1,this.frozenInfo.columns){this.getScrollPaneCurrentWidth()<this.getScrollPaneMinWidth()&&(this.scrollPaneHidden=!0)}this.updateScrollHeaderVisibility()},updateScrollHeaderVisibility:function(){if(this.previousHasScrollHeader===this.scrollPaneHidden)return;this.previousHasScrollHeader=this.scrollPaneHidden;let t=this.$headerR.get(0);this.frozenInfo.right&&(t=this.$headerL.get(0)),this.scrollPaneHidden?t.style.visibility="hidden":t.style.visibility="",this.updateScrollPaneColumnsHidden(this.scrollPaneHidden),this.cssRulesInvalid=!0},updateScrollPaneColumnsHidden:function(t){const e=this.frozenInfo.columns,i=this.viewAllColumns;for(let o=e,n=i.length-1;o<n;o++){i[o].tg_filtered=t}},getScrollPaneMinWidth:function(){let t=this.options.scrollPaneMinWidth;return(!d.isNum(t)||t<0)&&(t=this.scrollbarSizeV),t},updateVScrollState:function(){this.hasVScroll=!0;const t=this.getScrollbarHeight();if(this.options.autoHeight)this.hasVScroll=!1,this.containerHeight=this.headerHeight+this.totalRowsHeight+t,this.$holder.height(this.containerHeight);else{this.containerHeight-this.headerHeight-t>=this.totalRowsHeight&&(this.hasVScroll=!1)}},updateBlankColumnWidth:function(){let t=this.containerWidth-this.columnsWidth;!this.hasVScroll||this.hasHScroll||this.options.scrollbarFade||(t-=this.scrollbarSizeV),this.scrollPaneHidden&&(t=0),this.hasHScroll||(t>=0?(this.frozenInfo.columns?this.columnsWidthR+=t:this.columnsWidthL+=t,this.blankColumn.tg_width=t):this.hasHScroll=!0)}},$t={scrollToRow:function(t){const e=this.getRowItem(t);return this.scrollToItem(e,null),this},scrollToColumn:function(t){const e=this.getColumnItem(t);return this.scrollToItem(null,e),this},scrollToCell:function(t,e){const i=this.getRowItem(t),o=this.getColumnItem(e);return this.scrollToItem(i,o),this},scrollToFirstRow:function(){return this.setScrollTop(0),this},scrollToLastRow:function(){const t=this.getViewRows(),e=t[t.length-1],i=this.getScrollRowPosition(e);if(d.isNum(i))return this.setScrollTop(i),this},scrollToFirstColumn:function(){return this.setScrollLeft(0),this},scrollToLastColumn:function(t){const e=this.getViewColumns();let i=e[e.length-2];t&&(i=e[e.length-1]);const o=this.getScrollColumnPosition(i);if(d.isNum(o))return this.setScrollLeft(o),this},scrollRowIntoView:function(t){const e=this.getRowItem(t);return this.scrollItemIntoView(e,null),this},scrollColumnIntoView:function(t){const e=this.getColumnItem(t);return this.scrollItemIntoView(null,e),this},scrollCellIntoView:function(t,e){const i=this.getRowItem(t),o=this.getColumnItem(e);return this.scrollItemIntoView(i,o),this},setScroll:function(t,e){return t===this.scrollLeft&&e===this.scrollTop||(this.scrollLeft=t,this.scrollTop=e,this.scrollHandler()),this},setScrollLeft:function(t){return t===this.scrollLeft||(this.scrollLeft=t,this.scrollHandler()),this},setScrollTop:function(t){return t===this.scrollTop||(this.scrollTop=t,this.scrollHandler()),this},getScrollRowPosition:function(t){if(!t)return;let e=t.tg_view_index;return e-=this.frozenInfo.rows,e>=0?this.getRowTop(t):void 0},getScrollColumnPosition:function(t){if(!t)return;let e=t.tg_left;return this.frozenInfo.columns&&(e-=this.bodyWidthL),e>=0?e:void 0},scrollToItem:function(t,e){return this.scrollToChanged=!1,this.scrollToRowHandler(t),this.scrollToColumnHandler(e),this.scrollToChanged?(this.scrollHandler(),this):this},scrollToRowHandler:function(t){if(!t)return;const e=this.getScrollRowPosition(t);d.isNum(e)&&e!==this.scrollTop&&(this.scrollTop=e,this.scrollToChanged=!0)},scrollToColumnHandler:function(t){if(!t)return;const e=this.getScrollColumnPosition(t);d.isNum(e)&&e!==this.scrollLeft&&(this.scrollLeft=e,this.scrollToChanged=!0)},scrollItemIntoView:function(t,e){return this.scrollIntoViewChanged=!1,this.scrollRowIntoViewHandler(t),this.scrollColumnIntoViewHandler(e),this.scrollIntoViewChanged?(this.scrollHandler(),this):this},scrollRowIntoViewHandler:function(t){if(!t)return;const e=this.getScrollRowPosition(t);if(!d.isNum(e))return;if(e<this.scrollTop)return this.scrollTop=e,void(this.scrollIntoViewChanged=!0);const i=this.getRowHeight(t),o=this.getScrollViewHeight();if(e+i>this.scrollTop+o){const t=e-(o-i);this.scrollTop=t,this.scrollIntoViewChanged=!0}},scrollColumnIntoViewHandler:function(t){if(!t)return;const e=this.getScrollColumnPosition(t);if(!d.isNum(e))return;if(e<this.scrollLeft)return this.scrollLeft=e,void(this.scrollIntoViewChanged=!0);const i=t.tg_width,o=this.getScrollViewWidth();if(e+i>this.scrollLeft+o){const t=e-(o-i);this.scrollLeft=t,this.scrollIntoViewChanged=!0}},scrollOnInit:function(){const{scrollLeft:t,scrollTop:e,scrollColumn:i,scrollRow:o}=this.renderSettings;this.scrollIntoViewChanged=!1,Number.isInteger(t)&&t!==this.scrollLeft&&(this.scrollLeft=t,this.scrollIntoViewChanged=!0),Number.isInteger(e)&&e!==this.scrollTop&&(this.scrollTop=e,this.scrollIntoViewChanged=!0),i&&this.scrollColumnIntoViewHandler(i),o&&this.scrollRowIntoViewHandler(o),this.scrollIntoViewChanged&&this.scrollPane.setPosition(this.scrollLeft,this.scrollTop)},scrollHandler:function(){this.scrollPane.setPosition(this.scrollLeft,this.scrollTop),this.scrollRenderHandler()},scrollRenderHandler:function(){this.previousScrollLeft===this.scrollLeft&&this.previousScrollTop===this.scrollTop||(this.previousScrollLeft=this.scrollLeft,this.previousScrollTop=this.scrollTop,this.onNextUpdated((()=>{this.updateScrollPaneFade(!0),this.trigger(y.onScroll,{scrollLeft:this.scrollLeft,scrollTop:this.scrollTop})})),this.render())},scrollTouchStartHandler:function(t,e){this.hideColumnLine(),this.scrollTouchLeft=this.getScrollLeft(),this.scrollTouchTop=this.getScrollTop(),this.scrollMaxTouchLeft=this.getMaxScrollLeft(),this.scrollMaxTouchTop=this.getMaxScrollTop()},getTouchOrientation:function(t){return t.orientation?t.orientation:[e.LEFT,e.RIGHT].includes(t.direction)?(t.orientation||(t.orientation="Y"),t.orientation):[e.UP,e.DOWN].includes(t.direction)?(t.orientation||(t.orientation="X"),t.orientation):void 0},scrollTouchMoveHandler:function(t,e){if(e.touchLength>1)return;let i=e.offsetX,o=e.offsetY;const n=this.getTouchOrientation(e);"X"===n?i=0:"Y"===n&&(o=0);let s=this.scrollTouchLeft-i,r=this.scrollTouchTop-o;s=d.clamp(s,0,this.scrollMaxTouchLeft),r=d.clamp(r,0,this.scrollMaxTouchTop);let l=!1;this.scrollPaneHidden&&(l=this.scrollPaneFrozen.setOffsetH(-e.moveX),s=0);const h=this.getScrollLeft(),a=this.getScrollTop();(s!==h||r!==a||l)&&(d.preventDefault(e.e),this.setScroll(s,r))},scrollTouchEndHandler:function(){this.protectedItem=null},scrollTouchInertiaHandler:function(t,e){const i=this.getScrollLeft(),o=this.getScrollTop(),n=i-e.touchInertiaX,s=o-e.touchInertiaY;this.setScroll(n,s)},getScrollViewWidth:function(){let t=this.getScrollPaneWidth();return this.frozenInfo.right||(t-=this.getScrollbarWidth()),t},getScrollViewHeight:function(){let t=this.getScrollPaneHeight();return this.frozenInfo.bottom||(t-=this.getScrollbarHeight()),t},getScrollPaneWidth:function(){return this.scrollPane.width()},getScrollPaneHeight:function(){return this.scrollPane.height()},getScrollbarWidth:function(){return this.hasVScroll&&!this.options.scrollbarFade?this.scrollbarSizeV:0},getScrollbarHeight:function(){return this.hasHScroll&&!this.options.scrollbarFade?this.scrollbarSizeH:0},getScrollLeft:function(){return this.scrollPane.getScrollLeft()},getScrollTop:function(){return this.scrollPane.getScrollTop()},getMaxScrollLeft:function(){return this.scrollPane.getMaxScrollLeft()},getMaxScrollTop:function(){return this.scrollPane.getMaxScrollTop()}};class Dt{constructor(t){this.options=this.generateOptions(t)}generateOptions(t){return d.merge({ignore:null,sortField:"",sortFactor:1,sortBlankFactor:1,sortComparer:null},t)}sortList(t){if(!d.isList(t)||1===t.length)return!1;this.ignoreExcludeHandler(t);const e=this.comparerHandler(t);return this.ignoreIncludeHandler(t),e}getDefaultComparer(t){return(ft[t]||ft.string).bind(this)}comparerHandler(t){const e=this.options,i=e.sortField,o=e.sortFactor,n=e.sortBlankFactor,s=e.sortComparer;return"function"==typeof s&&(t.sort(((t,e)=>s.call(this,t,e,{sortField:i,sortFactor:o,sortBlankFactor:n}))),!0)}ignoreExcludeHandler(t){const e=this.options.ignore;this.ignoreListTop=[],this.ignoreListBottom=[];const i=[];for(let o=0,n=t.length;o<n;o++){const n=e(t[o]);n&&(n.top?this.ignoreListTop.unshift(n):this.ignoreListBottom.push(n),i.push(o))}i.reverse(),i.forEach((function(e){t.splice(e,1)}))}ignoreIncludeHandler(t){this.ignoreListTop.forEach((e=>{t.unshift(e.item)})),this.ignoreListBottom.forEach((e=>{t.push(e.item)}))}}const Bt={removeSortColumn:function(){return this.sortColumn=null,this.$header&&this.$header.find(".tg-column-sorted").removeClass("tg-column-sorted"),this},setSortColumn:function(t){if(!(t=this.getColumnItem(t)))return;if(!this.isColumnSortable(t))return;t===this.sortColumn?t.sortAsc=!t.sortAsc:d.hasOwn(t,"sortAsc")||(t.sortAsc=this.options.sortAsc),this.sortColumn=t;if(this.getRowsLength()-this.frozenInfo.rows<2)return;if(!this.headerCreated)return;this.updateRowsSort()&&(this.renderHeaderSort(),this.flushSort(),this.render("rows"))},renderHeaderSort:function(){const t=this.sortColumn;if(!t)return this;if(!this.isColumnSortable(t))return this;this.$header.find(".tg-column-sorted").removeClass("tg-column-sorted");const e=t.tg_view_index,i=this.$header.find(`.tg-header-item[column='${e}']`).find(".tg-column-header").addClass("tg-column-sorted");return t.sortAsc?i.removeClass("tg-sort-desc").addClass("tg-sort-asc"):i.removeClass("tg-sort-asc").addClass("tg-sort-desc"),this},getSortComparer:function(t){const e=t.comparer;if("function"==typeof e)return e;const i=this.options.sortComparers,o=i[e||t.type];return"function"==typeof o?o:i.string},updateRowsSort:function(){const t=this.sortColumn;if(!t)return!1;const e=t.id;return!!e&&this.sortRows(e,t)},sortRows:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i=e.sortAsc?-1:1,o=this.options.sortBlankValueBottom?1:i,n=this.getSortComparer(e);let s=!1;const r=new Dt({ignore:function(t){return t.tg_frozen?{item:t,top:!0}:t.sortFixed?{item:t,top:"top"===t.sortFixed}:void 0},sortField:t,sortFactor:i,sortBlankFactor:o,sortComparer:n}),l=function(t){r.sortList(t)&&(s=!0),t.forEach((function(t,e){t.tg_sub_index=e,t.subs&&l(t.subs)}))};return l(this.rows),s&&this.initRowsHandler(),s}},At={default:{},lightblue:{rowHeight:35,scrollbarSize:10,scrollbarRound:!0},dark:{}},Wt={getAllThemes:function(){return Object.keys(At)},getThemeOptions:function(t){return At[t]}},Ft={update:function(){return this.flushBody(),this.render("rows"),this},updateRow:function(t,e){const i=this.getRowItem(t);if(!i)return this;if(e&&"object"==typeof e){const t=this.getItemSnapshot(e);Object.keys(t).forEach((function(e){i[e]=t[e]}))}return this.flushRow(i.tg_view_index),this.render("rows"),this},updateCell:function(t,e,i){const o=this.getRowItem(t);if(!o)return this;const n=this.getColumnItem(e);return n?(arguments.length>2&&(o[n.id]=i),this.flushCell(o.tg_view_index,n.tg_view_index),this.render("rows"),this):this},onNextUpdated:function(t){return"function"!=typeof t||this.once(y.onUpdated,t),this}},Gt={getViewport:function(){this.scrollLeft=this.getScrollLeft(),this.scrollTop=this.getScrollTop();return{rows:this.getViewportRows(),columns:this.getViewportColumns()}},getViewportRows:function(){const t=[],e=this.viewRows,i=e.length;if(!i)return t;let o=this.options.rowCacheLength;o=d.clamp(d.toNum(o,!0),0,i);const n=this.frozenInfo.rows;if(n){let e=0;for(;e<n;)t.push(e),e+=1}const s=i-1,r=this.scrollTop;let l=this.getRowByPosition(e,n,s,r);l-=o,l=Math.max(l,n);const h=this.scrollTop+(this.bodyHeight-this.frozenRowsHeight);let a=this.getRowByPosition(e,n,s,h);for(a+=o,a=Math.min(a,s);l<=a;)t.push(l),l+=1;return this.protectedItemHandler(t,"row"),t},getRowByPosition:function(t,e,i,o){for(;i-e>1;){const n=Math.floor(.5*(e+i)),s=t[n],r=this.getRowTop(s),l=this.getRowHeight(s);if(o<r)i=n;else{if(!(o>r+l))return n;e=n}}const n=t[i];return o<this.getRowTop(n)?e:i},getViewportColumns:function(){const t=this.getColumnListFromFrozen();let e=this.scrollLeft;e=Math.max(e,0),this.frozenInfo.columns&&(this.frozenInfo.right?e+=this.columnsWidthR:e+=this.columnsWidthL);let i=this.scrollLeft+this.bodyWidth;i=Math.min(i,this.columnsWidth);const o=this.getColumnListFromRange(e,i),n=[].concat(t).concat(o);return this.protectedItemHandler(n,"column"),n},getColumnListFromFrozen:function(){const t=this.frozenInfo.columns;if(!t)return[];const e=[];let i=0;for(;i<t;)e.push(i),i++;return e},getColumnListFromRange:function(t,e){if(t>=e)return[];const i=[],o=this.frozenInfo.columns,n=this.viewColumns;for(let s=o,r=n.length;s<r;s++){const o=n[s];this.isColumnInRange(o,t,e)&&i.push(s)}return this.updateColumnListFromCache(i),i},updateColumnListFromCache:function(t){if(!t.length)return;const e=this.viewColumns.length;let i=this.options.columnCacheLength;i=d.clamp(d.toNum(i,!0),0,e);const o=this.frozenInfo.column;for(;i>0;){const n=t[0]-1;n>o&&t.unshift(n);const s=t[t.length-1]+1;s<e&&t.push(s),i-=1}},isColumnInRange:function(t,e,i){if(this.isInvisible(t))return!1;const o=t.tg_left,n=o+t.tg_width;return!(o>i)&&!(n<e)},protectedItemHandler:function(t,e){if(!this.protectedItem)return;const i=this.protectedItem[e];d.isNum(i)&&(t.includes(i)||(t.push(i),t.sort((function(t,e){return t-e}))))}};class jt extends k{static $=w;static getInstance=d.getInstance;static getAllThemes=Wt.getAllThemes;static getAllEvents=F.getAllEvents;static VERSION=e.VERSION;static TIMESTAMP=e.TIMESTAMP;$=w;VERSION=e.VERSION;TIMESTAMP=e.TIMESTAMP;constructor(t){super(),this.create(t)}setOption(t,e){this.renderType="all";let i=t;if("string"==typeof t){if(this.options)return this.options[t]=e,this;i={},i[t]=e}return this.customOptions=i,this}getOption(t){const e=this.options;return arguments.length?e?e[t]:void 0:e}setData(t){this.renderType="all";const e={columns:[],rows:[]};let i;if(t&&"object"==typeof t){Array.isArray(t.columns)&&(e.columns=t.columns),Array.isArray(t.rows)&&(e.rows=t.rows);const o=t.rowsLength;Number.isInteger(o)&&o>0&&(e.rows.length=o),i=t.options}return this.data=e,this.dataOptions=i,this}setDataSnapshot(t){return this.setData(this.generateDataSnapshot(t)),this}getData(){return this.data}toString(){return"[object Grid]"}}var Ut;Ut=jt.prototype,[v,R,S,T,E,I,L,x,z,F,G,j,K,q,J,Z,tt,et,pt,mt,bt,wt,vt,Ht,{keyTabHandler:function(t){},keyEnterHandler:function(t){},keyEscHandler:function(t){},keyPageUpHandler:function(t){return this.scrollPane.keyPageUpHandler(t)},keyPageDownHandler:function(t){return this.scrollPane.keyPageDownHandler(t)},keyEndHandler:function(t){return this.scrollPane.keyEndHandler(t)},keyHomeHandler:function(t){return this.scrollPane.keyHomeHandler(t)},keyLeftHandler:function(t){return this.scrollPaneHidden?this.scrollPaneFrozen.keyLeftHandler(t):this.scrollPane.keyLeftHandler(t)},keyUpHandler:function(t){return this.scrollPane.keyUpHandler(t)},keyRightHandler:function(t){return this.scrollPaneHidden?this.scrollPaneFrozen.keyRightHandler(t):this.scrollPane.keyRightHandler(t)},keyDownHandler:function(t){return this.scrollPane.keyDownHandler(t)}},Ct,yt,Rt,St,Tt,{showRow:function(t){return this.updateRowsInvisible(this.toRowItemList(t),!1)},hideRow:function(t){return this.updateRowsInvisible(this.toRowItemList(t),!0)},updateRowsInvisible:function(t,e){if(!t.length)return!1;const i=[];return t.forEach((t=>{t.invisible!==e&&(t.invisible=e,t.tg_invisible=e,i.push(t))})),!!i.length&&(this.update(),!0)}},Et,It,Lt,xt,zt,Vt,Ot,$t,Bt,Wt,Ft,Gt].forEach((t=>{for(const e in t){if(d.hasOwn(Ut,e))throw new Error(`ERROR: extends with an existing key: "${e}"`);Ut[e]=t[e]}}));const Xt=jt,Yt=e.VERSION,Kt=e.TIMESTAMP,qt={VERSION:Yt,TIMESTAMP:Kt,Grid:Xt,$:w,CONST:e,EventBase:k,Icon:X,Motion:B,ScrollPane:kt,Util:d}})();var n=o.$,s=o.MP,r=o._d,l=o.xA,h=o.In,a=o.T8,c=o.Gr,d=o.ht,u=o.J0,g=o.xv,f=o.Ay;export{n as $,s as CONST,r as EventBase,l as Grid,h as Icon,a as Motion,c as ScrollPane,d as TIMESTAMP,u as Util,g as VERSION,f as default};