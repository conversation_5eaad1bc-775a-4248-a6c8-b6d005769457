{"last_node_id": 48, "last_link_id": 95, "nodes": [{"id": 8, "type": "VAEDecode", "pos": [1210, 190], "size": [210, 46], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 35}, {"name": "vae", "type": "VAE", "link": 76}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [56, 93], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "widgets_values": []}, {"id": 39, "type": "VAELoader", "pos": [866.3932495117188, 499.18597412109375], "size": [306.36004638671875, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [76], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["wan_2.1_vae.safetensors"]}, {"id": 28, "type": "SaveAnimatedWEBP", "pos": [1460, 190], "size": [870.8511352539062, 643.7430419921875], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 56}], "outputs": [], "properties": {}, "widgets_values": ["ComfyUI", 16, false, 90, "default", ""]}, {"id": 7, "type": "CLIPTextEncode", "pos": [413, 389], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 75}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [52], "slot_index": 0}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走"], "color": "#322", "bgcolor": "#533"}, {"id": 38, "type": "CLIPLoader", "pos": [12.94982624053955, 184.6981658935547], "size": [390, 82], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [74, 75], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPLoader"}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"]}, {"id": 40, "type": "EmptyHunyuanLatentVideo", "pos": [520, 620], "size": [315, 130], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [91], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyHunyuanLatentVideo"}, "widgets_values": [832, 480, 33, 1]}, {"id": 47, "type": "SaveWEBM", "pos": [2367.213134765625, 193.6114959716797], "size": [315, 130], "flags": {}, "order": 10, "mode": 4, "inputs": [{"name": "images", "type": "IMAGE", "link": 93}], "outputs": [], "properties": {"Node name for S&R": "SaveWEBM"}, "widgets_values": ["ComfyUI", "vp9", 24, 32]}, {"id": 3, "type": "K<PERSON><PERSON><PERSON>", "pos": [863, 187], "size": [315, 262], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 95}, {"name": "positive", "type": "CONDITIONING", "link": 46}, {"name": "negative", "type": "CONDITIONING", "link": 52}, {"name": "latent_image", "type": "LATENT", "link": 91}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [35], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [82628696717253, "randomize", 30, 6, "uni_pc", "simple", 1]}, {"id": 48, "type": "ModelSamplingSD3", "pos": [440, 50], "size": [210, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 94}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [95], "slot_index": 0}], "properties": {"Node name for S&R": "ModelSamplingSD3"}, "widgets_values": [8]}, {"id": 37, "type": "UNETLoader", "pos": [20, 40], "size": [346.7470703125, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [94], "slot_index": 0}], "properties": {"Node name for S&R": "UNETLoader"}, "widgets_values": ["wan2.1_t2v_1.3B_fp16.safetensors", "default"]}, {"id": 6, "type": "CLIPTextEncode", "pos": [415, 186], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 74}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [46], "slot_index": 0}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a fox moving quickly in a beautiful winter scenery nature trees mountains daytime tracking camera"], "color": "#232", "bgcolor": "#353"}], "links": [[35, 3, 0, 8, 0, "LATENT"], [46, 6, 0, 3, 1, "CONDITIONING"], [52, 7, 0, 3, 2, "CONDITIONING"], [56, 8, 0, 28, 0, "IMAGE"], [74, 38, 0, 6, 0, "CLIP"], [75, 38, 0, 7, 0, "CLIP"], [76, 39, 0, 8, 1, "VAE"], [91, 40, 0, 3, 3, "LATENT"], [93, 8, 0, 47, 0, "IMAGE"], [94, 37, 0, 48, 0, "MODEL"], [95, 48, 0, 3, 0, "MODEL"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.1167815779425205, "offset": [-5.675057867608515, 8.013751263058214]}}, "version": 0.4}