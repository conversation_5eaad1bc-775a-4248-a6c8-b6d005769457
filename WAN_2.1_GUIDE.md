# 🎬 WAN 2.1 Video Generation Guide

## 🚀 Quick Start

Your WAN 2.1 setup is complete and ready to generate videos! Here are the different ways to create videos:

## 📋 Prerequisites

- ✅ ComfyUI server running at http://127.0.0.1:8188
- ✅ WAN 2.1 models downloaded (9.1GB total)
- ✅ WAN Video Wrapper custom nodes installed
- ✅ 16GB RAM with MPS acceleration

## 🎯 Method 1: ComfyUI Web Interface (Recommended)

### Step 1: Open ComfyUI
```bash
# If not already running, start ComfyUI:
cd ComfyUI
python3 main.py --lowvram
```

### Step 2: Access Web Interface
- Open your browser and go to: http://127.0.0.1:8188
- You should see the ComfyUI node-based interface

### Step 3: Load WAN Workflow
- Click "Load" button in the interface
- Select `wan_text_to_video_workflow.json` from your project folder
- The workflow nodes will appear in the interface

### Step 4: Set Your Prompt
- Find the "WanVideo TextEncode" node
- Enter your prompt in the text field
- Example: "A cat walking in a garden, realistic style"

### Step 5: Generate Video
- Click "Queue Prompt" button
- Wait for generation (usually 2-5 minutes)
- Video will be saved in `ComfyUI/output/` folder

## 🔄 Method 2: Batch Processing

### For Multiple Videos from Prompt List

1. **Edit the prompt list** in `batch_video_generator.py`:
```python
prompts = [
    "A cat walking in a garden, realistic style",
    "A dog running on the beach, cinematic",
    "Your custom prompt here",
    # Add more prompts...
]
```

2. **Run the batch generator**:
```bash
python3 batch_video_generator.py
```

3. **Monitor progress**:
- The script will show progress for each prompt
- Videos are saved with timestamps in `ComfyUI/output/`

## ⚙️ Customization Options

### Video Settings
- **Resolution**: 512x512 (default), can be adjusted in workflow
- **Frames**: 16 frames (default), ~2 seconds at 8 FPS
- **Quality**: Adjustable in SaveAnimatedWEBP node

### Generation Parameters
- **Steps**: 20 (default) - higher = better quality, slower
- **CFG Scale**: 7.0 (default) - how closely to follow prompt
- **Scheduler**: "euler" (default) - sampling method
- **Seed**: Random or fixed for reproducible results

### Prompt Tips
- **Be specific**: "A red cat walking slowly in a sunny garden"
- **Add style**: "cinematic", "realistic", "artistic", "macro shot"
- **Use negative prompts**: "blurry, low quality, distorted"

## 📁 Output Files

Videos are saved as:
- **Format**: Animated WebP (.webp)
- **Location**: `ComfyUI/output/`
- **Naming**: `wan_video_[timestamp]_[number].webp`

## 🔧 Troubleshooting

### If ComfyUI Server Stops
```bash
cd ComfyUI
python3 main.py --lowvram
```

### If Generation Fails
1. Check ComfyUI console for error messages
2. Ensure all models are in correct folders
3. Try simpler prompts first
4. Restart ComfyUI server if needed

### Memory Issues
- Use `--lowvram` flag (already configured)
- Close other applications
- Try shorter videos (fewer frames)

## 🎨 Example Prompts

### Nature & Animals
- "A majestic eagle soaring over mountains, cinematic"
- "A peaceful lake with swans, morning mist, serene"
- "A colorful butterfly on a blooming flower, macro"

### Urban & Architecture  
- "A busy city street at night, neon lights, cyberpunk"
- "An old lighthouse during a storm, dramatic"
- "A cozy coffee shop interior, warm lighting"

### Abstract & Artistic
- "Flowing liquid gold, abstract art, mesmerizing"
- "Geometric patterns morphing, kaleidoscope effect"
- "Watercolor paint spreading on paper, artistic"

## 📊 Performance Notes

- **Generation Time**: 2-5 minutes per video on M1 Pro
- **Memory Usage**: ~8-12GB RAM during generation
- **Storage**: ~5-15MB per video file
- **Concurrent**: Generate one video at a time for best performance

## 🔄 Next Steps

1. **Experiment** with different prompts and settings
2. **Create collections** of related videos
3. **Adjust parameters** for your preferred style
4. **Share results** and iterate on successful prompts

Happy video generating! 🎬✨
